// |reftest| error:SyntaxError
// This file was procedurally generated from the following sources:
// - src/invalid-private-names/call-expression-this.case
// - src/invalid-private-names/default/block.template
/*---
description: this evaluated in call expression (Invalid private names should throw a SyntaxError, block statement)
esid: sec-static-semantics-early-errors
features: [class-fields-private]
flags: [generated]
negative:
  phase: parse
  type: SyntaxError
info: |
    ScriptBody:StatementList
      It is a Syntax Error if AllPrivateNamesValid of StatementList with an empty List
      as an argument is false unless the source code is eval code that is being
      processed by a direct eval.

    ModuleBody:ModuleItemList
      It is a Syntax Error if AllPrivateNamesValid of ModuleItemList with an empty List
      as an argument is false.


    Static Semantics: AllPrivateNamesValid

    MemberExpression : MemberExpression . PrivateName

    1. If StringValue of PrivateName is in names, return true.
    2. Return false.

    CallExpression : CallExpression . PrivateName

    1. If StringValue of PrivateName is in names, return true.
    2. Return false.

---*/


$DONOTEVALUATE();

{ (() => this)().#x }
