# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
name = "clang-sys"
version = "1.7.0"
authors = ["<PERSON> <<EMAIL>>"]
build = "build.rs"
links = "clang"
description = "Rust bindings for libclang."
documentation = "https://docs.rs/clang-sys"
readme = "README.md"
license = "Apache-2.0"
repository = "https://github.com/KyleMayes/clang-sys"

[package.metadata.docs.rs]
features = [
    "clang_17_0",
    "runtime",
]

[dependencies.glob]
version = "0.3"

[dependencies.libc]
version = "0.2.39"
default-features = false

[dependencies.libloading]
version = "0.8"
optional = true

[dev-dependencies.glob]
version = "0.3"

[dev-dependencies.serial_test]
version = "1"

[dev-dependencies.tempfile]
version = "3"

[build-dependencies.glob]
version = "0.3"

[features]
clang_10_0 = ["clang_9_0"]
clang_11_0 = ["clang_10_0"]
clang_12_0 = ["clang_11_0"]
clang_13_0 = ["clang_12_0"]
clang_14_0 = ["clang_13_0"]
clang_15_0 = ["clang_14_0"]
clang_16_0 = ["clang_15_0"]
clang_17_0 = ["clang_16_0"]
clang_3_5 = []
clang_3_6 = ["clang_3_5"]
clang_3_7 = ["clang_3_6"]
clang_3_8 = ["clang_3_7"]
clang_3_9 = ["clang_3_8"]
clang_4_0 = ["clang_3_9"]
clang_5_0 = ["clang_4_0"]
clang_6_0 = ["clang_5_0"]
clang_7_0 = ["clang_6_0"]
clang_8_0 = ["clang_7_0"]
clang_9_0 = ["clang_8_0"]
runtime = ["libloading"]
static = []
