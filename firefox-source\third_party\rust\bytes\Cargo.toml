# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "bytes"
version = "1.4.0"
authors = [
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
]
description = "Types and traits for working with bytes"
readme = "README.md"
keywords = [
    "buffers",
    "zero-copy",
    "io",
]
categories = [
    "network-programming",
    "data-structures",
]
license = "MIT"
repository = "https://github.com/tokio-rs/bytes"

[package.metadata.docs.rs]
rustdoc-args = [
    "--cfg",
    "docsrs",
]

[dependencies.serde]
version = "1.0.60"
features = ["alloc"]
optional = true
default-features = false

[dev-dependencies.serde_test]
version = "1.0"

[features]
default = ["std"]
std = []

[target."cfg(loom)".dev-dependencies.loom]
version = "0.5"
