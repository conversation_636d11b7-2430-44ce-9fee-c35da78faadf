# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "subtle"
version = "2.5.0"
authors = ["Isis Lovecruft <<EMAIL>>", "<PERSON> de Valence <<EMAIL>>"]
exclude = ["**/.gitignore", ".travis.yml"]
description = "Pure-Rust traits and utilities for constant-time cryptographic implementations."
homepage = "https://dalek.rs/"
documentation = "https://docs.rs/subtle"
readme = "README.md"
keywords = ["cryptography", "crypto", "constant-time", "utilities"]
categories = ["cryptography", "no-std"]
license = "BSD-3-Clause"
repository = "https://github.com/dalek-cryptography/subtle"
[dev-dependencies.rand]
version = "0.8"

[features]
const-generics = []
core_hint_black_box = []
default = ["std", "i128"]
i128 = []
nightly = []
std = []
[badges.travis-ci]
branch = "master"
repository = "dalek-cryptography/subtle"
