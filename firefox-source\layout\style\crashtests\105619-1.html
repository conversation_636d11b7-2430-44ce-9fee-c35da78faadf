<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head>
	<title>International Herald Tribune</title>
	
	<script type="text/javascript">

	function displayFix() {
		document.getElementById("bodyNode").style.display = "block";
	}

	</script>
	<style type="text/css">

	#clippingsContainer {overflow:auto;}
	#menuSearch {position:absolute;}

	</style>
	
</head>

<body id="bodyNode" onload="displayFix()">
	<div>
		<div id="menuSearch"><input type="text"></div>
		<div id="clippingsContainer"></div>
	</div>

	<table>
		<tr><td></td><td></td><td></td></tr>
		<tr><td></td><td></td><td></td></tr>
	</table>

</body></html>
