# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
name = "strck"
version = "1.0.0"
authors = ["<PERSON> <quinno<PERSON><PERSON>@gmail.com>"]
description = "Checked owned and borrowed strings"
readme = "README.md"
keywords = [
    "text",
    "ident",
    "identifier",
    "validate",
]
categories = [
    "rust-patterns",
    "memory-management",
]
license = "MIT"
repository = "https://github.com/QnnOkabayashi/strck"
resolver = "1"

[package.metadata.docs.rs]
all-features = true

[dependencies.serde]
version = "1.0"
optional = true

[dependencies.unicode-ident]
version = "1"
optional = true

[dev-dependencies.serde]
version = "1.0"
features = ["derive"]

[dev-dependencies.serde_json]
version = "1.0"

[dev-dependencies.smol_str]
version = "0.3"

[features]
ident = ["dep:unicode-ident"]
