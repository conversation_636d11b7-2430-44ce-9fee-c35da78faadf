# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
name = "clubcard"
version = "0.3.2"
authors = ["<PERSON> <<EMAIL>>"]
description = "Clubcard is an exact membership query filter for static sets"
readme = "README.md"
license = "MPL-2.0"
repository = "https://github.com/mozilla/clubcard/"

[dependencies.rand]
version = "0.8.5"
optional = true

[dependencies.serde]
version = "1.0"
features = ["derive"]

[dev-dependencies.sha2]
version = "0.10"

[features]
builder = ["dep:rand"]
