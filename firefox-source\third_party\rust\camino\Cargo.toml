# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "camino"
version = "1.1.2"
authors = [
    "Without Boats <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
    "Rain <<EMAIL>>",
]
exclude = [
    ".cargo/**/*",
    ".github/**/*",
]
description = "UTF-8 paths"
documentation = "https://docs.rs/camino"
readme = "README.md"
keywords = [
    "paths",
    "utf8",
    "unicode",
    "filesystem",
]
categories = [
    "development-tools",
    "filesystem",
    "os",
]
license = "MIT OR Apache-2.0"
repository = "https://github.com/camino-rs/camino"

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg=doc_cfg"]

[dependencies.proptest]
version = "1.0.0"
optional = true

[dependencies.serde]
version = "1"
features = ["derive"]
optional = true

[features]
proptest1 = ["proptest"]
serde1 = ["serde"]
