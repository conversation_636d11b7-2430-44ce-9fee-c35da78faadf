/**
 * Megisto Browser - Core Service Component
 * XPCOM component that provides core Megisto Browser functionality
 */

const { XPCOMUtils } = ChromeUtils.import("resource://gre/modules/XPCOMUtils.jsm");
const { Services } = ChromeUtils.import("resource://gre/modules/Services.jsm");

const MEGISTO_SERVICE_CID = Components.ID("{12345678-1234-1234-1234-123456789abc}");
const MEGISTO_SERVICE_CONTRACTID = "@megisto.browser/service;1";

/**
 * Megisto Core Service
 * Provides system-level integration for Megisto Browser features
 */
function MegistoService() {
  this.wrappedJSObject = this;
  this._initialized = false;
  this._statistics = {
    cookiesBlocedToday: 0,
    cookiesBlockedTotal: 0,
    youtubeEmbedsReplaced: 0
  };
}

MegistoService.prototype = {
  classID: MEGISTO_SERVICE_CID,
  contractID: MEGISTO_SERVICE_CONTRACTID,
  classDescription: "Megisto Browser Core Service",
  
  QueryInterface: ChromeUtils.generateQI([
    Ci.nsIObserver,
    Ci.nsISupportsWeakReference
  ]),
  
  /**
   * Initialize the service
   */
  init: function() {
    if (this._initialized) {
      return;
    }
    
    console.log("Megisto Service: Initializing...");
    
    // Register observers
    this._registerObservers();
    
    // Load statistics
    this._loadStatistics();
    
    // Set up daily reset timer
    this._setupDailyReset();
    
    this._initialized = true;
    console.log("Megisto Service: Initialized successfully");
  },
  
  /**
   * Register system observers
   */
  _registerObservers: function() {
    Services.obs.addObserver(this, "profile-after-change", true);
    Services.obs.addObserver(this, "quit-application", true);
    Services.obs.addObserver(this, "http-on-modify-request", true);
    Services.obs.addObserver(this, "content-document-global-created", true);
  },
  
  /**
   * Handle system notifications
   */
  observe: function(subject, topic, data) {
    switch (topic) {
      case "profile-after-change":
        this.init();
        break;
        
      case "quit-application":
        this._saveStatistics();
        break;
        
      case "http-on-modify-request":
        this._handleHttpRequest(subject);
        break;
        
      case "content-document-global-created":
        this._handleDocumentCreated(subject);
        break;
    }
  },
  
  /**
   * Handle HTTP requests for blocking
   */
  _handleHttpRequest: function(httpChannel) {
    try {
      if (!(httpChannel instanceof Ci.nsIHttpChannel)) {
        return;
      }
      
      const uri = httpChannel.URI;
      const url = uri.spec.toLowerCase();
      
      // Block known tracking and popup scripts
      if (this._shouldBlockRequest(url)) {
        console.log("Megisto Service: Blocking request:", uri.spec);
        httpChannel.cancel(Cr.NS_BINDING_ABORTED);
        this._incrementBlockedCount();
      }
      
    } catch (error) {
      console.error("Megisto Service: Error handling HTTP request:", error);
    }
  },
  
  /**
   * Check if a request should be blocked
   */
  _shouldBlockRequest: function(url) {
    const blockPatterns = [
      'popup',
      'overlay',
      'modal',
      'consent-manager',
      'cookie-banner',
      'gdpr-banner',
      'privacy-notice',
      'tracking-pixel',
      'analytics-beacon'
    ];
    
    return blockPatterns.some(pattern => url.includes(pattern));
  },
  
  /**
   * Handle document creation for content script injection
   */
  _handleDocumentCreated: function(window) {
    try {
      if (!window || !window.document) {
        return;
      }
      
      const document = window.document;
      const location = document.location;
      
      if (!location || !location.href) {
        return;
      }
      
      // Skip non-HTTP(S) pages
      if (!location.href.startsWith('http')) {
        return;
      }
      
      // Inject Megisto functionality
      this._injectMegistoScripts(window);
      
    } catch (error) {
      console.error("Megisto Service: Error handling document creation:", error);
    }
  },
  
  /**
   * Inject Megisto scripts into web pages
   */
  _injectMegistoScripts: function(window) {
    try {
      // Create and inject the Megisto content script
      const script = window.document.createElement('script');
      script.type = 'text/javascript';
      script.textContent = `
        // Megisto Browser - Injected Content Script
        (function() {
          if (window.megistoInjected) return;
          window.megistoInjected = true;
          
          console.log('Megisto: Content script injected');
          
          // Block popups
          const originalOpen = window.open;
          window.open = function(...args) {
            console.log('Megisto: Blocked popup:', args[0]);
            return null;
          };
          
          // Enhanced cookie banner detection
          const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
              mutation.addedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                  checkForCookieBanners(node);
                }
              });
            });
          });
          
          function checkForCookieBanners(element) {
            const selectors = [
              '[id*="cookie"]', '[class*="cookie"]',
              '[id*="consent"]', '[class*="consent"]',
              '[id*="gdpr"]', '[class*="gdpr"]'
            ];
            
            selectors.forEach(selector => {
              try {
                const banners = element.querySelectorAll ? element.querySelectorAll(selector) : [];
                banners.forEach(banner => {
                  if (banner.offsetHeight > 50) {
                    console.log('Megisto: Removing cookie banner:', banner);
                    banner.remove();
                  }
                });
              } catch (e) {}
            });
          }
          
          // Start observing
          if (document.body) {
            observer.observe(document.body, { childList: true, subtree: true });
            checkForCookieBanners(document.body);
          }
          
        })();
      `;
      
      // Inject the script
      const head = window.document.head || window.document.documentElement;
      if (head) {
        head.appendChild(script);
      }
      
    } catch (error) {
      console.error("Megisto Service: Error injecting scripts:", error);
    }
  },
  
  /**
   * Load statistics from preferences
   */
  _loadStatistics: function() {
    try {
      this._statistics.cookiesBlocedToday = Services.prefs.getIntPref("megisto.stats.blockedToday", 0);
      this._statistics.cookiesBlockedTotal = Services.prefs.getIntPref("megisto.stats.blockedTotal", 0);
      this._statistics.youtubeEmbedsReplaced = Services.prefs.getIntPref("megisto.stats.youtubeReplaced", 0);
      
      console.log("Megisto Service: Statistics loaded:", this._statistics);
    } catch (error) {
      console.error("Megisto Service: Error loading statistics:", error);
    }
  },
  
  /**
   * Save statistics to preferences
   */
  _saveStatistics: function() {
    try {
      Services.prefs.setIntPref("megisto.stats.blockedToday", this._statistics.cookiesBlocedToday);
      Services.prefs.setIntPref("megisto.stats.blockedTotal", this._statistics.cookiesBlockedTotal);
      Services.prefs.setIntPref("megisto.stats.youtubeReplaced", this._statistics.youtubeEmbedsReplaced);
      
      console.log("Megisto Service: Statistics saved");
    } catch (error) {
      console.error("Megisto Service: Error saving statistics:", error);
    }
  },
  
  /**
   * Increment blocked count
   */
  _incrementBlockedCount: function() {
    this._statistics.cookiesBlocedToday++;
    this._statistics.cookiesBlockedTotal++;
    
    // Save periodically
    if (this._statistics.cookiesBlockedTotal % 10 === 0) {
      this._saveStatistics();
    }
  },
  
  /**
   * Set up daily statistics reset
   */
  _setupDailyReset: function() {
    // Reset daily count at midnight
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    
    const msUntilMidnight = tomorrow.getTime() - now.getTime();
    
    setTimeout(() => {
      this._statistics.cookiesBlocedToday = 0;
      this._saveStatistics();
      
      // Set up recurring daily reset
      setInterval(() => {
        this._statistics.cookiesBlocedToday = 0;
        this._saveStatistics();
      }, 24 * 60 * 60 * 1000); // 24 hours
      
    }, msUntilMidnight);
  },
  
  /**
   * Get current statistics
   */
  getStatistics: function() {
    return Object.assign({}, this._statistics);
  },
  
  /**
   * Clear statistics
   */
  clearStatistics: function() {
    this._statistics.cookiesBlocedToday = 0;
    this._statistics.cookiesBlockedTotal = 0;
    this._statistics.youtubeEmbedsReplaced = 0;
    this._saveStatistics();
  }
};

// Component registration
var NSGetFactory = XPCOMUtils.generateNSGetFactory([MegistoService]);
