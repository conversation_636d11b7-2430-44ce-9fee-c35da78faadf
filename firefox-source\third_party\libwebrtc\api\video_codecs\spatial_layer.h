/*
 *  Copyright (c) 2020 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#ifndef API_VIDEO_CODECS_SPATIAL_LAYER_H_
#define API_VIDEO_CODECS_SPATIAL_LAYER_H_

#include "api/video_codecs/simulcast_stream.h"

namespace webrtc {

typedef SimulcastStream SpatialLayer;

}  // namespace webrtc
#endif  // API_VIDEO_CODECS_SPATIAL_LAYER_H_
