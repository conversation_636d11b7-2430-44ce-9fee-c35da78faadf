# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "block-buffer"
version = "0.10.3"
authors = ["RustCrypto Developers"]
description = "Buffer type for block processing of data"
documentation = "https://docs.rs/block-buffer"
readme = "README.md"
keywords = [
    "block",
    "buffer",
]
categories = [
    "cryptography",
    "no-std",
]
license = "MIT OR Apache-2.0"
repository = "https://github.com/RustCrypto/utils"

[dependencies.generic-array]
version = "0.14"
