<?xml version="1.0"?>

<ratified href="WEBGL_compressed_texture_s3tc/">
  <name>WEBGL_compressed_texture_s3tc</name>
  <contact>
    <a href="https://www.khronos.org/webgl/public-mailing-list/">WebGL working group</a> (public_webgl 'at' khronos.org)
  </contact>
  <contributors>
    <contributor>Members of the WebGL working group</contributor>
  </contributors>
  <number>8</number>
  <depends>
    <api version="1.0"/>
  </depends>
  <overview>
    <p>
      This extension exposes the compressed texture formats defined in the 
      <a href="http://www.opengl.org/registry/specs/EXT/texture_compression_s3tc.txt">
      EXT_texture_compression_s3tc</a> OpenGL extension to WebGL.
    </p>
    <features>
      <feature>
        Compression formats <code>COMPRESSED_RGB_S3TC_DXT1_EXT</code>,
        <code>COMPRESSED_RGBA_S3TC_DXT1_EXT</code>, <code>COMPRESSED_RGBA_S3TC_DXT3_EXT</code>, and
        <code>COMPRESSED_RGBA_S3TC_DXT5_EXT</code> may be passed to
        the <code>compressedTexImage2D</code> and <code>compressedTexSubImage2D</code> entry points.
      </feature>
      <feature>
        Calling <code>getParameter</code> with the argument <code>COMPRESSED_TEXTURE_FORMATS</code>
        will include the 4 formats from this specification.
      </feature>
      <feature>
        <p>The following format specific restrictions must be enforced:</p>
        <dl>
          <dt>COMPRESSED_RGB_S3TC_DXT1_EXT</dt>
          <dt>COMPRESSED_RGBA_S3TC_DXT1_EXT</dt>
          <dd><p>The <code>byteLength</code> of the ArrayBufferView, <code>pixels</code>, passed to
          either <code>compressedTexImage2D</code> or <code>compressedTexSubImage2D</code>
          must match the following equation:</p>
          <blockquote><code>
          floor((width + 3) / 4) * floor((height + 3) / 4) * 8
          </code></blockquote>
          <p>
          If it is not an <code>INVALID_VALUE</code> error is generated.
          </p>
          <p>When <code>level</code> equals zero <code>width</code> and <code>height</code>
          must be a multiple of 4. When <code>level</code> is greater than 0 <code>width</code>
          and <code>height</code> must be 0, 1, 2 or a multiple of 4.</p>
          <p>
          If they are not an <code>INVALID_OPERATION</code> error is generated.
          </p>
          <p>
          For <code>compressedTexSubImage2D</code> <code>xoffset</code> and
          <code>yoffset</code> must be a multiple of 4 and
          <code>width</code> must be a multiple of 4 or equal to the original
          width of the <code>level</code>. <code>height</code> must be a multiple of 4 or
          equal to the original height of the <code>level</code>.
          If they are not an <code>INVALID_OPERATION</code> error is generated.
          </p>
          </dd>

          <dt>COMPRESSED_RGBA_S3TC_DXT3_EXT</dt>
          <dt>COMPRESSED_RGBA_S3TC_DXT5_EXT</dt>
          <dd><p>The <code>byteLength</code> of the ArrayBufferView, <code>pixels</code>, passed to
          either <code>compressedTexImage2D</code> or <code>compressedTexSubImage2D</code> must
          match the following equation:</p>
          <blockquote><code>
          floor((width + 3) / 4) * floor((height + 3) / 4) * 16
          </code></blockquote>
          <p>
          If it is not an <code>INVALID_VALUE</code> error is generated.
          </p>
          <p>When <code>level</code> equals zero <code>width</code> and <code>height</code>
          must be a multiple of 4. When <code>level</code> is greater than 0 <code>width</code>
          and <code>height</code> must be 0, 1, 2 or a multiple of 4.</p>
          <p>
          If they are not an <code>INVALID_OPERATION</code> error is generated.
          </p>
          <p>
          For <code>compressedTexSubImage2D</code> <code>xoffset</code> and
          <code>yoffset</code> must be a multiple of 4 and
          <code>width</code> must be a multiple of 4 or equal to the original
          width of the <code>level</code>. <code>height</code> must be a multiple of 4 or
          equal to the original height of the <code>level</code>.
          If they are not an <code>INVALID_OPERATION</code> error is generated.
          </p>
          </dd>
        </dl>
      </feature>
    </features>
  </overview>
  <idl xml:space="preserve">
[NoInterfaceObject]
interface WEBGL_compressed_texture_s3tc {
    /* Compressed Texture Formats */
    const GLenum COMPRESSED_RGB_S3TC_DXT1_EXT        = 0x83F0;
    const GLenum COMPRESSED_RGBA_S3TC_DXT1_EXT       = 0x83F1;
    const GLenum COMPRESSED_RGBA_S3TC_DXT3_EXT       = 0x83F2;
    const GLenum COMPRESSED_RGBA_S3TC_DXT5_EXT       = 0x83F3;
};
  </idl>
  <history>
    <revision date="2012/01/25">
      <change>Initial revision.</change>
    </revision>
    <revision date="2013/01/26">
      <change>Moved from draft to community approved status</change>
    </revision>
    <revision date="2013/05/15">
      <change>Ratified by Khronos Board of Promoters.</change>
    </revision>
    <revision date="2013/07/03">
      <change>Clarified that length of ArrayBufferView is actually byteLength.</change>
    </revision>
    <revision date="2014/07/15">
      <change>Added NoInterfaceObject extended attribute.</change>
    </revision>
  </history>
</ratified>
