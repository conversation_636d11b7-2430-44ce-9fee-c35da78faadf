mozilla_version/__init__.py,sha256=oITKXEftlJ9dVeCJgvhk39LIP3vnXuXoobkJj4mNAxU,391
mozilla_version/balrog.py,sha256=WgnQEtMa16JvKisD4JTFnP2gd5e6_K9QcHxkOqQ9mJk,5072
mozilla_version/errors.py,sha256=C8mutZqgu68eCLuD7sr4dggBym4QzsMsI1iKMNPOGQI,2401
mozilla_version/fenix.py,sha256=10Z7jbST3xVgmLiCuJZH8GqffwChPIfJdBu1hig3jP8,200
mozilla_version/gecko.py,sha256=vc2lmoDGaw9v3ytWSuvWAPbIWypK0MUHuQNaLCMrErM,31142
mozilla_version/ios.py,sha256=Uoms65QtYtUshSBowKQojKtPVAy4L10hdQ-sY0dgGuM,2613
mozilla_version/maven.py,sha256=H5bZ-9QitMdT89o4mRXkTaeb7iQc8yVTJnmauyxSImU,1971
mozilla_version/mobile.py,sha256=09aPz1f1YyEnb4bGNeWF-rvPfnEIp4iK1DL5XK0EYU0,10146
mozilla_version/parser.py,sha256=kwaw3UeAbWgUFtCmCheY9grKwabmq9tc64JyTlPrHS8,1335
mozilla_version/version.py,sha256=wmB66af-0OkjPxZ-NRHraayY1ujAccIZ3q_8GAkTS-U,10212
mozilla_version/__pycache__/__init__.cpython-313.pyc,sha256=-_hkMrk4YukjvgWrXMIL83zBBQsLYB-pwmHNtP7cG10,540
mozilla_version/__pycache__/balrog.cpython-313.pyc,sha256=I_s43qkbZ-jjdR2F-zPvHDDRkHclWNwJ4ouzkepUJVM,6668
mozilla_version/__pycache__/errors.cpython-313.pyc,sha256=m0VWTQneLHKJhM5UoxPHVXwsnvYyiPjHS0YohxL_LGU,3530
mozilla_version/__pycache__/fenix.cpython-313.pyc,sha256=Yw6Ou__1WmBIVAKeYeIQoLWwCdE-WpOxRnCMWbsK-fA,310
mozilla_version/__pycache__/gecko.cpython-313.pyc,sha256=CFK3DnOOzAzMyQbLEqr0hZThnfV67cR2d1hbwNn6RQ8,29038
mozilla_version/__pycache__/ios.cpython-313.pyc,sha256=xHZidba4n6ZqpLJjBjtPAzGkLaKswQFpdOEpbKtvNJA,3713
mozilla_version/__pycache__/maven.cpython-313.pyc,sha256=I1zBOdY-5piP80nPJ5ZA-5-txmSqf-Yz1Vyv_tL5dTg,3194
mozilla_version/__pycache__/mobile.cpython-313.pyc,sha256=dK7CKJk11WHoWgxIvo1gzHdb7isewu9TfQ7KSJzWDSE,10601
mozilla_version/__pycache__/parser.cpython-313.pyc,sha256=A6C5eqZOtKejwaqjfPrxAZRMGZeB3A5klNmusx68ts4,2023
mozilla_version/__pycache__/version.cpython-313.pyc,sha256=O7XDwptX2M59DW6QPTB_v3N3ZHvoo-OWPBUqms59OLo,12213
mozilla_version/test/__init__.py,sha256=ui4glNH_cCoz4Ex7hcZhHTcstOPJb2wcojFiNvvIALI,88
mozilla_version/test/test_balrog.py,sha256=PY1b_q5Vhr6fhyrSB-sMwQC86CQQhLtAD2cIisbu51U,9431
mozilla_version/test/test_default_imports.py,sha256=gj9fuHANTOQq6isXmVIoE1sXDKO46xmM_H3jxgDE7W0,303
mozilla_version/test/test_errors.py,sha256=KLe6NiicS7aer7NURynsOWTZGZwKp8OQauX_nGJ_HdQ,1037
mozilla_version/test/test_fenix.py,sha256=qs8sD39N_cM9rNEZxyCaLuxx53hIIeHZIrJe_EBpYoQ,193
mozilla_version/test/test_gecko.py,sha256=6PirgVPXkfbOEpiQLB_DF1Jeoaf_PMaVGu1H4VQTvf0,49941
mozilla_version/test/test_ios.py,sha256=Xad8MUADaD1YMokNmwEnI6ZRaQzfebQUZCpXJi0LAsE,1808
mozilla_version/test/test_maven.py,sha256=JnBLgIngC83roeKM4U_PL2_op_VZD9_MQT0KIhAWtxw,3904
mozilla_version/test/test_mobile.py,sha256=2koHZxIf5hQpHcvWiuwiua7CmmBq0ADUPYhqJzpw1xM,12792
mozilla_version/test/test_version.py,sha256=7mkWoAViZ8pto9UU6_BSLSa8anKB29cNF7lcgurMyPk,8308
mozilla_version/test/__pycache__/__init__.cpython-313.pyc,sha256=J_fGeX2d1IJOQmRyF40r7xILrRDUwTVOTRdtmc_ZDVI,383
mozilla_version/test/__pycache__/test_balrog.cpython-313-pytest-8.3.5.pyc,sha256=rJ_NR_IPIDdvMtA6oPNH7d-X7iaU9zHHdzmFM47c_p0,28962
mozilla_version/test/__pycache__/test_default_imports.cpython-313-pytest-8.3.5.pyc,sha256=Lfm7o-AEXwPHFF8wsN5gXuPwmA9-9HDQkn8Fv_p1WNs,726
mozilla_version/test/__pycache__/test_errors.cpython-313-pytest-8.3.5.pyc,sha256=kHvwSkjDrZVYm6XeWbutYVcMZsPNyljK-dshZ7cjnUg,3276
mozilla_version/test/__pycache__/test_fenix.cpython-313-pytest-8.3.5.pyc,sha256=hbVJbhNPb1gN9kwSdDzdYemfAYUUDSKXQ_BWsY3W1yY,1421
mozilla_version/test/__pycache__/test_gecko.cpython-313-pytest-8.3.5.pyc,sha256=LGnGS984hN9Y884ZlM1ky14S0cSU5vDDUDUHV34jrLs,91089
mozilla_version/test/__pycache__/test_ios.cpython-313-pytest-8.3.5.pyc,sha256=B077BfsYkjKLgBNIXvs2McWSatuQp2ZqzaIrec6-Tz4,5657
mozilla_version/test/__pycache__/test_maven.cpython-313-pytest-8.3.5.pyc,sha256=fwJm28nzz9hjFaaAalpbKu9rghSSNvusdF4IrNQgaGQ,22316
mozilla_version/test/__pycache__/test_mobile.cpython-313-pytest-8.3.5.pyc,sha256=lxECTxvQ-pMaPTHH79fWPnhuiwcbxCD8x95_d_2v7d0,43297
mozilla_version/test/__pycache__/test_version.cpython-313-pytest-8.3.5.pyc,sha256=SxcaHafoGIKwL3AsKMBTwbLEA9XJP08nSpsuCfRGAuE,45621
mozilla_version/test/integration/__init__.py,sha256=WbyyzEaWz3-1bSc9IOrFjBqzYJdVpLPcPy4PpJchpA8,345
mozilla_version/test/integration/test_product_details.py,sha256=oUdCi3dbkMx_8zJLo_pjy86LCcZLUPDnVq3TVPIkarw,1369
mozilla_version/test/integration/__pycache__/__init__.cpython-313.pyc,sha256=DcoSAm474HLabrrxC9puOkqJNlQoL-M2gNxaD3-F-eQ,787
mozilla_version/test/integration/__pycache__/test_product_details.cpython-313-pytest-8.3.5.pyc,sha256=D-LGLxeUK8oRjz95sTBXY3Oz6yqs_RqBKd6VgJlsA9c,2555
mozilla_version-4.1.0.dist-info/LICENSE,sha256=rxdbnZbuk8IaA2FS4bkFsLlTBNSujCySHHYJEAuo334,15921
mozilla_version-4.1.0.dist-info/METADATA,sha256=AiCH1bI6bbv5NgPRVDJK8bdGJo82wUYcJTN4xn5oKRs,479
mozilla_version-4.1.0.dist-info/WHEEL,sha256=GV9aMThwP_4oNCtvEC2ec3qUYutgWeAzklro_0m4WJQ,91
mozilla_version-4.1.0.dist-info/top_level.txt,sha256=K1r8SXa4ny0i7OTfimG0Ct33oHkXtLjuU1E5_aHBe94,16
mozilla_version-4.1.0.dist-info/RECORD,,
