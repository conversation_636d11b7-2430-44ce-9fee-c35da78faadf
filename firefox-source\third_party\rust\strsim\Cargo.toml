# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
rust-version = "1.56"
name = "strsim"
version = "0.11.1"
authors = [
    "<PERSON> <<EMAIL>>",
    "max<PERSON><PERSON> <<EMAIL>>",
]
exclude = [
    "/.github",
    "/dev",
]
description = """
Implementations of string similarity metrics. Includes Hamming, Levenshtein,
OSA, Damerau-Levenshtein, Jaro, Jaro-<PERSON>, and <PERSON><PERSON><PERSON><PERSON>-<PERSON>.
"""
homepage = "https://github.com/rapidfuzz/strsim-rs"
documentation = "https://docs.rs/strsim/"
readme = "README.md"
keywords = [
    "string",
    "similarity",
    "Hamming",
    "Levenshtein",
    "Jaro",
]
categories = ["text-processing"]
license = "MIT"
repository = "https://github.com/rapidfuzz/strsim-rs"
