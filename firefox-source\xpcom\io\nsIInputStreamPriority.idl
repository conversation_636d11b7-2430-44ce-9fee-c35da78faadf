/* -*- Mode: C++; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 4 -*- */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#include "nsISupports.idl"
#include "nsIRunnable.idl"

[scriptable, uuid(daa45b24-98ee-4eb2-9cec-aad0bc023e9d)]
interface nsIInputStreamPriority : nsISupports
{
    /**
     * An input stream implementing this interface will dispatch runnable
     * events with this priority. See nsIRunnablePriority.
     */
    attribute unsigned long priority;
};
