# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
name = "context_id"
version = "0.1.0"
build = false
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
readme = "README.md"
license = "MPL-2.0"

[lib]
name = "context_id"
path = "src/lib.rs"

[dependencies]
chrono = "0.4"
parking_lot = "0.12"
serde = "1"
serde_json = "1"
thiserror = "1.0"
url = "2"

[dependencies.error-support]
path = "../support/error"

[dependencies.lazy_static]
version = "1.4"

[dependencies.uniffi]
version = "0.29.0"

[dependencies.uuid]
version = "1.3"
features = ["v4"]

[dependencies.viaduct]
path = "../viaduct"

[dev-dependencies]
mockito = "0.31"

[dev-dependencies.viaduct-reqwest]
path = "../support/viaduct-reqwest"

[build-dependencies.uniffi]
version = "0.29.0"
features = ["build"]
