<!DOCTYPE html>
<meta charset=utf-8>
<title>Test for logical properties of input type=number in the UA stylesheet</title>
<style>
.v-rl { writing-mode: vertical-rl; }
.ltr, .rtl, .v-rl { border: 1px solid blue; }
div { padding: 10px; }
.a { -moz-appearance:textfield; }
.b { -moz-appearance:none; }
</style>
<div class=ltr>
  <input type=number value=123><br><br>
  <input type=number value=456 class=a><br><br>
  <input type=number value=7890 class=b>
</div>

<div class=rtl dir=rtl>
  <input type=number value=123><br><br>
  <input type=number value=456 class=a><br><br>
  <input type=number value=7890 class=b>
</div>

<div class=v-rl>
  <input type=number value=123><br><br>
  <input type=number value=456 class=a><br><br>
  <input type=number value=7890 class=b>
</div>
