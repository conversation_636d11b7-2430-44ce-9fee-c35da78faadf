# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies
#
# If you believe there's an error in this file please file an
# issue against the rust-lang/cargo repository. If you're
# editing this file be aware that the upstream Cargo.toml
# will likely look very different (and much more reasonable)

[package]
name = "circular"
version = "0.3.0"
authors = ["<PERSON><PERSON> <<EMAIL>>"]
include = ["LICENSE", "README.md", ".gitignore", ".travis.yml", "Cargo.toml", "src/*.rs"]
description = "A stream abstraction designed for use with nom"
readme = "README.md"
license = "MIT"
repository = "https://github.com/sozu-proxy/circular"

[dependencies]
