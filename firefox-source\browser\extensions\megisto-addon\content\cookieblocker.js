/**
 * Meg<PERSON><PERSON>rowser - <PERSON><PERSON> Blocker
 * Content script that automatically dismisses cookie banners and consent modals
 */

class CookieBannerBlocker {
  constructor() {
    this.rules = null;
    this.observer = null;
    this.processed = new Set();
    this.commonSelectors = [
      // Common cookie banner selectors
      '[id*="cookie"]',
      '[class*="cookie"]',
      '[id*="consent"]',
      '[class*="consent"]',
      '[id*="gdpr"]',
      '[class*="gdpr"]',
      '[id*="privacy"]',
      '[class*="privacy"]',
      '[class*="banner"]',
      '[class*="modal"]',
      '[class*="overlay"]',
      '[class*="popup"]',
      // Specific known selectors
      '#cookieChoiceInfo',
      '.cookie-banner',
      '.consent-banner',
      '.gdpr-banner',
      '.privacy-notice',
      '.cookie-notice',
      '.consent-modal',
      '.cmp-modal',
      '.fc-consent-root',
      '.qc-cmp2-container'
    ];
    
    this.init();
  }
  
  async init() {
    console.log('Megisto Cookie Blocker: Initializing...');
    
    // Get rules from background script
    await this.loadRules();
    
    // Start blocking immediately
    this.blockExistingBanners();
    
    // Set up mutation observer for dynamic content
    this.setupObserver();
    
    // Block popups
    this.blockPopups();
    
    console.log('Megisto Cookie Blocker: Initialized');
  }
  
  async loadRules() {
    try {
      const response = await browser.runtime.sendMessage({ type: 'GET_RULES' });
      this.rules = response.rule;
      console.log('Megisto Cookie Blocker: Rules loaded for domain');
    } catch (error) {
      console.error('Megisto Cookie Blocker: Failed to load rules:', error);
    }
  }
  
  blockExistingBanners() {
    // Use domain-specific rules if available
    if (this.rules && this.rules.selectors) {
      this.rules.selectors.forEach(selector => {
        this.processElements(document.querySelectorAll(selector));
      });
    }
    
    // Use common selectors as fallback
    this.commonSelectors.forEach(selector => {
      try {
        this.processElements(document.querySelectorAll(selector));
      } catch (error) {
        // Ignore invalid selectors
      }
    });
  }
  
  processElements(elements) {
    elements.forEach(element => {
      if (this.processed.has(element)) return;
      
      if (this.isCookieBanner(element)) {
        this.handleCookieBanner(element);
        this.processed.add(element);
      }
    });
  }
  
  isCookieBanner(element) {
    const text = element.textContent.toLowerCase();
    const className = element.className.toLowerCase();
    const id = element.id.toLowerCase();
    
    // Check for cookie/consent related keywords
    const keywords = [
      'cookie', 'consent', 'gdpr', 'privacy', 'accept', 'reject',
      'agree', 'policy', 'tracking', 'analytics', 'personalization'
    ];
    
    return keywords.some(keyword => 
      text.includes(keyword) || 
      className.includes(keyword) || 
      id.includes(keyword)
    ) && (
      element.offsetHeight > 50 || // Visible element
      getComputedStyle(element).position === 'fixed' ||
      getComputedStyle(element).position === 'absolute'
    );
  }
  
  handleCookieBanner(element) {
    console.log('Megisto Cookie Blocker: Found cookie banner:', element);
    
    // Try to find and click reject/decline buttons first
    const rejectButton = this.findRejectButton(element);
    if (rejectButton) {
      console.log('Megisto Cookie Blocker: Clicking reject button');
      rejectButton.click();
      return;
    }
    
    // If no reject button, try to find accept button and modify behavior
    const acceptButton = this.findAcceptButton(element);
    if (acceptButton) {
      console.log('Megisto Cookie Blocker: Found accept button, removing banner instead');
    }
    
    // Remove the banner entirely
    this.removeBanner(element);
  }
  
  findRejectButton(container) {
    const rejectSelectors = [
      '[id*="reject"]',
      '[class*="reject"]',
      '[id*="decline"]',
      '[class*="decline"]',
      '[id*="deny"]',
      '[class*="deny"]',
      '[data-testid*="reject"]',
      '[data-testid*="decline"]',
      '[aria-label*="reject"]',
      '[aria-label*="decline"]'
    ];

    for (const selector of rejectSelectors) {
      try {
        const button = container.querySelector(selector);
        if (button && button.offsetParent !== null) {
          return button;
        }
      } catch (error) {
        // Ignore invalid selectors
      }
    }

    // Look for buttons with reject-like text in multiple languages
    const buttons = container.querySelectorAll('button, a, [role="button"], input[type="button"]');
    for (const button of buttons) {
      const text = button.textContent.toLowerCase().trim();
      const ariaLabel = (button.getAttribute('aria-label') || '').toLowerCase();
      const title = (button.getAttribute('title') || '').toLowerCase();
      const value = (button.getAttribute('value') || '').toLowerCase();

      const rejectTerms = [
        // English
        'reject', 'decline', 'deny', 'refuse', 'disagree', 'no thanks', 'not now',
        'reject all', 'decline all', 'necessary only', 'essential only',
        // German
        'ablehnen', 'verweigern', 'nein', 'nicht zustimmen',
        // French
        'refuser', 'rejeter', 'non', 'pas maintenant',
        // Spanish
        'rechazar', 'denegar', 'no', 'no gracias',
        // Italian
        'rifiuta', 'nega', 'no', 'non ora',
        // Dutch
        'weigeren', 'afwijzen', 'nee', 'niet nu'
      ];

      const allText = `${text} ${ariaLabel} ${title} ${value}`;
      if (rejectTerms.some(term => allText.includes(term))) {
        return button;
      }
    }

    return null;
  }
  
  findAcceptButton(container) {
    const acceptSelectors = [
      '[id*="accept"]',
      '[class*="accept"]',
      '[id*="agree"]',
      '[class*="agree"]',
      'button:contains("Accept")',
      'button:contains("Agree")',
      'a:contains("Accept")',
      'a:contains("Agree")'
    ];
    
    for (const selector of acceptSelectors) {
      try {
        const button = container.querySelector(selector);
        if (button && button.offsetParent !== null) {
          return button;
        }
      } catch (error) {
        // Ignore invalid selectors
      }
    }
    
    return null;
  }
  
  removeBanner(element) {
    console.log('Megisto Cookie Blocker: Removing banner');
    
    // Remove the element
    element.remove();
    
    // Also remove any backdrop/overlay
    const overlays = document.querySelectorAll('[class*="overlay"], [class*="backdrop"], [class*="modal-backdrop"]');
    overlays.forEach(overlay => {
      if (overlay.style.zIndex && parseInt(overlay.style.zIndex) > 1000) {
        overlay.remove();
      }
    });
    
    // Restore body scroll if it was disabled
    document.body.style.overflow = '';
    document.documentElement.style.overflow = '';
  }
  
  setupObserver() {
    this.observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check the added node itself
            if (this.isCookieBanner(node)) {
              this.handleCookieBanner(node);
            }
            
            // Check children of the added node
            this.commonSelectors.forEach(selector => {
              try {
                this.processElements(node.querySelectorAll(selector));
              } catch (error) {
                // Ignore invalid selectors
              }
            });
          }
        });
      });
    });
    
    this.observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }
  
  blockPopups() {
    // Override window.open
    const originalOpen = window.open;
    window.open = function(...args) {
      console.log('Megisto Cookie Blocker: Blocked popup:', args[0]);
      browser.runtime.sendMessage({
        type: 'BLOCK_POPUP',
        url: args[0]
      });
      return null;
    };
    
    // Block target="_blank" on suspicious links
    document.addEventListener('click', (event) => {
      const link = event.target.closest('a');
      if (link && link.target === '_blank') {
        const href = link.href.toLowerCase();
        if (href.includes('popup') || href.includes('ad') || href.includes('promo')) {
          console.log('Megisto Cookie Blocker: Blocked suspicious link:', link.href);
          event.preventDefault();
          event.stopPropagation();
        }
      }
    }, true);
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new CookieBannerBlocker();
  });
} else {
  new CookieBannerBlocker();
}
