// |reftest| shell-option(--enable-upsert) skip-if(!WeakMap.prototype.getOrInsert)
// Copyright (C) 2015 the V8 project authors. All rights reserved.
// Copyright (C) 2025 <PERSON>, <PERSON><PERSON>. All rights reserved.
// This code is governed by the BSD license found in the LICENSE file.
/*---
esid: proposal-upsert
description: >
  Throws a TypeError if `this` is not an Object.
info: |
  WeakMap.prototype.getOrInsert ( key , value )

  1. Let M be the this value
  2. Perform ? RequireInternalSlot(M, [[WeakMapData]])
  ...
features: [Symbol]
---*/

var m = new WeakMap();

assertThrowsInstanceOf(function () {
    m.getOrInsert.call(false, {}, 1);
}, TypeError);

assertThrowsInstanceOf(function () {
    m.getOrInsert.call(1, {}, 1);
}, TypeError);

assertThrowsInstanceOf(function () {
    m.getOrInsert.call("", {}, 1);
}, TypeError);

assertThrowsInstanceOf(function () {
    m.getOrInsert.call(undefined, {}, 1);
}, TypeError);

assertThrowsInstanceOf(function () {
    m.getOrInsert.call(null, {}, 1);
}, TypeError);

assertThrowsInstanceOf(function () {
    m.getOrInsert.call(Symbol(), {}, 1);
}, TypeError);

assertThrowsInstanceOf(function () {
    m.getOrInsert.call('', {}, 1);
}, TypeError);

reportCompare(0, 0);
