// |reftest| shell-option(--enable-temporal) skip-if(!this.hasOwnProperty('Temporal')||!xulRuntime.shell) -- Temporal is not enabled unconditionally, requires shell-options
// Copyright (C) 2021 Igalia, S.L. All rights reserved.
// This code is governed by the BSD license found in the LICENSE file.

/*---
esid: sec-temporal.duration.prototype.round
description: The relativeTo option accepts a ZonedDateTime-like ISO 8601 string
features: [Temporal]
includes: [temporalHelpers.js]
---*/

[
  '2000-01-01[UTC]',
  '2000-01-01T00:00[UTC]',
  '2000-01-01T00:00+00:00[UTC]',
  '2000-01-01T00:00+00:00[UTC][u-ca=iso8601]',
].forEach((relativeTo) => {
  const duration = new Temporal.Duration(0, 0, 0, 31);
  const result = duration.round({ largestUnit: "months", relativeTo });
  TemporalHelpers.assertDuration(result, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0);
});

reportCompare(0, 0);
