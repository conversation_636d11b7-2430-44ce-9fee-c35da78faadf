/* -*- Mode: C++; tab-width: 8; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* vim: set ts=8 sts=2 et sw=2 tw=80: */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#ifndef mozilla_Dafsa_h
#define mozilla_Dafsa_h

#include "stdint.h"

#include "mozilla/Span.h"
#include "nsStringFwd.h"

namespace mozilla {

/**
 * A deterministic acyclic finite state automaton suitable for storing static
 * dictionaries of tagged ascii strings. Consider using this if you have a very
 * large set of strings that need an associated enum value.
 *
 * Currently the string tag is limited by `make_dafsa.py` to a value of [0-4].
 * In theory [0-15] can easily be supported.
 *
 * See `make_dafsa.py` for more details.
 */
class Dafsa {
 public:
  using Graph = Span<const uint8_t>;

  /**
   * Initializes the DAFSA with a binary encoding generated by `make_dafsa.py`.
   */
  explicit Dafsa(const Graph& aData) : mData(aData) {}

  ~Dafsa() = default;

  /**
   * Searches for the given string in the DAFSA.
   *
   * @param aKey The string to search for.
   * @returns kKeyNotFound if not found, otherwise the associated tag.
   */
  int Lookup(const nsACString& aKey) const;

  static const int kKeyNotFound;

 private:
  const Graph mData;
};

}  // namespace mozilla

#endif  // mozilla_Dafsa_h
