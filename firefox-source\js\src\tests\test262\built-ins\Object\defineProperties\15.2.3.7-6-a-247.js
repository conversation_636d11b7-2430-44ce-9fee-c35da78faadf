// Copyright (c) 2012 Ecma International.  All rights reserved.
// This code is governed by the BSD license found in the LICENSE file.

/*---
es5id: ********-6-a-247
description: >
    Object.defineProperties - 'O' is an Array, 'P' is an array index
    named property that already exists on 'O' is data property and
    'desc' is data descriptor, test updating the [[Value]] attribute
    value of 'P'  (******** step 4.c)
includes: [propertyHelper.js]
---*/


var arr = [12];

Object.defineProperties(arr, {
  "0": {
    value: 36
  }
});

verifyProperty(arr, "0", {
  value: 36,
  writable: true,
  enumerable: true,
  configurable: true,
});

reportCompare(0, 0);
