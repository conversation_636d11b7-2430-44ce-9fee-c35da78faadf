# Megisto Browser Build Configuration

# Build Firefox with Megisto branding
ac_add_options --enable-application=browser

# Optimization settings
ac_add_options --enable-optimize
ac_add_options --disable-debug
ac_add_options --disable-debug-symbols

# Megisto-specific options
ac_add_options --with-app-name=megisto
ac_add_options --with-app-basename=Megisto
ac_add_options --with-branding=browser/branding/megisto

# Enable system add-on support
ac_add_options --enable-extensions

# Disable telemetry and data collection
ac_add_options --disable-telemetry
ac_add_options --disable-crashreporter
ac_add_options --disable-updater

# Performance optimizations
ac_add_options --enable-release
ac_add_options --enable-strip
ac_add_options --enable-install-strip

# Windows-specific options
ac_add_options --target=x86_64-pc-mingw32
ac_add_options --host=x86_64-pc-mingw32

# Output directory
mk_add_options MOZ_OBJDIR=@TOPSRCDIR@/obj-megisto
