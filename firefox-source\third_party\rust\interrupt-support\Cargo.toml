# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
name = "interrupt-support"
version = "0.1.0"
authors = ["Sync Team <<EMAIL>>"]
build = "build.rs"
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
readme = "README.md"
license = "MPL-2.0"

[lib]
name = "interrupt_support"
path = "src/lib.rs"

[dependencies]
lazy_static = "1.4"
parking_lot = ">=0.11,<=0.12"

[dependencies.rusqlite]
version = "0.33.0"
features = [
    "functions",
    "limits",
    "bundled",
    "unlock_notify",
]

[dependencies.uniffi]
version = "0.29.0"

[build-dependencies.uniffi]
version = "0.29.0"
features = ["build"]
