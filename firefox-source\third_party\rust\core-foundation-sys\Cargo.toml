# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "core-foundation-sys"
version = "0.8.7"
authors = ["The Servo Project Developers"]
build = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "Bindings to Core Foundation for macOS"
homepage = "https://github.com/servo/core-foundation-rs"
readme = false
license = "MIT OR Apache-2.0"
repository = "https://github.com/servo/core-foundation-rs"

[package.metadata.docs.rs]
all-features = true
default-target = "x86_64-apple-darwin"

[lib]
name = "core_foundation_sys"
path = "src/lib.rs"

[dependencies]

[features]
default = ["link"]
link = []
mac_os_10_7_support = []
mac_os_10_8_features = []
