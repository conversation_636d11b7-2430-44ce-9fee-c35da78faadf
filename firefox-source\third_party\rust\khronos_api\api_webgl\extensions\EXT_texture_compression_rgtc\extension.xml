<?xml version="1.0"?>

<draft href="EXT_texture_compression_rgtc/">
  <name>EXT_texture_compression_rgtc</name>
  <contact>
    <a href="https://www.khronos.org/webgl/public-mailing-list/">WebGL working group</a> (public_webgl 'at' khronos.org)
  </contact>
  <contributors>
    <contributor><PERSON><PERSON>, NVIDIA</contributor>
    <contributor>Members of the WebGL working group</contributor>
  </contributors>
  <number>39</number>
  <depends>
    <api version="1.0"/>
  </depends>
  <overview>
    <p>
      This extension exposes the compressed texture format defined in the
      <a href="https://www.khronos.org/registry/OpenGL/extensions/EXT/EXT_texture_compression_rgtc.txt">
      EXT_texture_compression_rgtc</a> OpenGL extension to WebGL. Consult that extension
      specification for behavioral definitions, including error behaviors.
    </p>
    <p>
      Updates of partial tiles detailed in the "Implementation Note" section of the
      EXT_texture_compression_rgtc specification must be supported in an implementation of this
      WebGL extension.
    </p>
    <features>
      <feature>
        Compression formats <code>COMPRESSED_RED_RGTC1_EXT</code>,
        <code>COMPRESSED_SIGNED_RED_RGTC1_EXT</code>,
        <code>COMPRESSED_RED_GREEN_RGTC2_EXT</code>,
        and <code>COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT</code> may be passed to
        the <code>compressedTexImage2D</code> and <code>compressedTexSubImage2D</code> entry points.
      </feature>
      <feature>
        Calling <code>getParameter</code> with the argument <code>COMPRESSED_TEXTURE_FORMATS</code>
        will include the formats from this specification.
      </feature>
      <feature>
        <p>The following format-specific restrictions must be enforced:</p>
        <dl>
          <dt>COMPRESSED_RED_RGTC1_EXT</dt>
          <dt>COMPRESSED_SIGNED_RED_RGTC1_EXT</dt>
          <dd><p>The <code>byteLength</code> of the ArrayBufferView, <code>pixels</code>, passed to
          <code>compressedTexImage2D</code> or <code>compressedTexSubImage2D</code> must be equal to the following number of bytes:</p>
          <blockquote><code>
            ceil(width / 4) * ceil(height / 4) * 8
          </code></blockquote>
          <p>If it is not, an <code>INVALID_VALUE</code> error is generated.</p>
          </dd>
          <dt>COMPRESSED_RED_GREEN_RGTC2_EXT</dt>
          <dt>COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT</dt>
          <dd><p>The <code>byteLength</code> of the ArrayBufferView, <code>pixels</code>, passed to
          <code>compressedTexImage2D</code> or <code>compressedTexSubImage2D</code> must be equal to the following number of bytes:</p>
          <blockquote><code>
            ceil(width / 4) * ceil(height / 4) * 16
          </code></blockquote>
          <p>If it is not, an <code>INVALID_VALUE</code> error is generated.</p>
          </dd>
          </dl>
      </feature>
    </features>
  </overview>
  <idl xml:space="preserve">
[NoInterfaceObject]
interface EXT_texture_compression_rgtc {
    const GLenum COMPRESSED_RED_RGTC1_EXT = 0x8DBB;
    const GLenum COMPRESSED_SIGNED_RED_RGTC1_EXT = 0x8DBC;
    const GLenum COMPRESSED_RED_GREEN_RGTC2_EXT = 0x8DBD;
    const GLenum COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT = 0x8DBE;
};
  </idl>

  <newtok>
    <function name="compressedTexImage2D">
      <param name="internalformat" type="GLenum"/>
      Accepted by the <code>internalformat</code> parameter:
      <code>COMPRESSED_RED_RGTC1_EXT</code>,
      <code>COMPRESSED_SIGNED_RED_RGTC1_EXT</code>,
      <code>COMPRESSED_RED_GREEN_RGTC2_EXT</code>,
      <code>COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT</code>
      <br/>
    </function>

    <function name="compressedTexSubImage2D">
      <param name="internalformat" type="GLenum"/>
      Accepted by the <code>internalformat</code> parameter:
      <code>COMPRESSED_RED_RGTC1_EXT</code>,
      <code>COMPRESSED_SIGNED_RED_RGTC1_EXT</code>,
      <code>COMPRESSED_RED_GREEN_RGTC2_EXT</code>,
      <code>COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT</code>
      <br/>
    </function>
  </newtok>

  <errors>
    <error enum="INVALID_VALUE">
      The error <code>INVALID_VALUE</code> is generated by <code>compressedTexImage2D</code> and
      <code>compressedTexSubImage2D</code> if the <code>internalformat</code> parameter is
      <code>COMPRESSED_RED_RGTC1_EXT</code> or <code>COMPRESSED_SIGNED_RED_RGTC1_EXT</code>
      and the byteLength of the ArrayBufferView is not:
      <blockquote><code>
        ceil(width / 4) * ceil(height / 4) * 8
      </code></blockquote>
    </error>
    <error enum="INVALID_VALUE">
      The error <code>INVALID_VALUE</code> is generated by <code>compressedTexImage2D</code> and
      <code>compressedTexSubImage2D</code> if the <code>internalformat</code> parameter is
      <code>COMPRESSED_RED_GREEN_RGTC2_EXT</code> or <code>COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT</code>
      and the byteLength of the ArrayBufferView is not:
      <blockquote><code>
        ceil(width / 4) * ceil(height / 4) * 16
      </code></blockquote>
    </error>
  </errors>

  <history>
    <revision date="2018/09/26">
      <change>Initial revision.</change>
    </revision>
    <revision date="2018/09/26">
      <change>Rename to EXT_texture_compression_rgtc from WEBGL_compressed_texture_rgtc</change>
    </revision>
  </history>
</draft>
