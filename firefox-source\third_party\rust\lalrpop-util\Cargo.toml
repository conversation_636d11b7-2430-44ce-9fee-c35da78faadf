# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "lalrpop-util"
version = "0.19.12"
authors = ["<PERSON><PERSON> <<EMAIL>>"]
description = "Runtime library for parsers generated by LALRPOP"
license = "Apache-2.0 OR MIT"
repository = "https://github.com/lalrpop/lalrpop"

[package.metadata.docs.rs]
features = ["lexer"]

[dependencies.regex]
version = "1"
optional = true

[features]
default = ["std"]
lexer = [
    "regex/std",
    "std",
]
std = []
