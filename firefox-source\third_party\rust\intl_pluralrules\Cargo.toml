# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "intl_pluralrules"
version = "7.0.2"
authors = [
    "<PERSON>ko<PERSON> Riggin <<EMAIL>>",
    "Zibi Braniecki <<EMAIL>>",
]
include = [
    "src/**/*",
    "benches/*.rs",
    "Cargo.toml",
    "README.md",
]
description = "Unicode Plural Rules categorizer for numeric input."
readme = "README.md"
keywords = [
    "localization",
    "l10n",
    "i18n",
    "intl",
    "internationalization",
]
categories = [
    "localization",
    "internationalization",
]
license = "Apache-2.0/MIT"
repository = "https://github.com/zbraniecki/pluralrules"

[[bench]]
name = "pluralrules"
harness = false

[dependencies.unic-langid]
version = "0.9"

[dev-dependencies.criterion]
version = "0.3"

[dev-dependencies.unic-langid]
version = "0.9"
features = ["macros"]

[badges.coveralls]
branch = "master"
repository = "zbraniecki/pluralrules"
service = "github"

[badges.maintenance]
status = "actively-developed"

[badges.travis-ci]
branch = "master"
repository = "zbraniecki/pluralrules"
