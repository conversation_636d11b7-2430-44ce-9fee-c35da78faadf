# Copyright (c) 2018 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../webrtc.gni")

rtc_library("bitrate_settings") {
  visibility = [ "*" ]
  sources = [
    "bitrate_settings.cc",
    "bitrate_settings.h",
  ]
  deps = [ "../../rtc_base/system:rtc_export" ]
}

rtc_source_set("bandwidth_usage") {
  visibility = [ "*" ]
  sources = [ "bandwidth_usage.h" ]
}

rtc_library("bandwidth_estimation_settings") {
  visibility = [ "*" ]
  sources = [ "bandwidth_estimation_settings.h" ]
  deps = [ "../../rtc_base/system:rtc_export" ]
}

rtc_source_set("ecn_marking") {
  visibility = [ "*" ]
  sources = [ "ecn_marking.h" ]
}

rtc_source_set("enums") {
  visibility = [ "*" ]
  sources = [ "enums.h" ]
}

rtc_library("network_control") {
  visibility = [ "*" ]
  sources = [
    "network_control.h",
    "network_types.cc",
    "network_types.h",
  ]

  deps = [
    ":ecn_marking",
    "../../api:field_trials_view",
    "../../rtc_base/system:rtc_export",
    "../environment",
    "../rtc_event_log",
    "../units:data_rate",
    "../units:data_size",
    "../units:time_delta",
    "../units:timestamp",
    "//third_party/abseil-cpp/absl/base:core_headers",
  ]
}

rtc_library("field_trial_based_config") {
  visibility = [ "*" ]
  sources = [
    "field_trial_based_config.cc",
    "field_trial_based_config.h",
  ]
  deps = [
    "../../api:field_trials_registry",
    "../../rtc_base/system:rtc_export",
    "../../system_wrappers:field_trial",
    "//third_party/abseil-cpp/absl/strings:string_view",
  ]
}

rtc_source_set("datagram_transport_interface") {
  visibility = [ "*" ]
  sources = [ "data_channel_transport_interface.h" ]
  deps = [
    "..:array_view",
    "..:priority",
    "..:rtc_error",
    "../../rtc_base:copy_on_write_buffer",
  ]
}

rtc_library("goog_cc") {
  visibility = [ "*" ]
  sources = [
    "goog_cc_factory.cc",
    "goog_cc_factory.h",
  ]
  deps = [
    ":network_control",
    "..:network_state_predictor_api",
    "../../api/units:time_delta",
    "../../modules/congestion_controller/goog_cc",
    "../../rtc_base/system:rtc_export",
  ]
}

rtc_source_set("sctp_transport_factory_interface") {
  visibility = [ "*" ]
  sources = [ "sctp_transport_factory_interface.h" ]
  deps = [ "../../api/environment" ]
}

rtc_source_set("stun_types") {
if (!build_with_mozilla) {
  visibility = [ "*" ]
  sources = [
    "stun.cc",
    "stun.h",
  ]

  deps = [
    "../../api:array_view",
    "../../rtc_base:byte_buffer",
    "../../rtc_base:byte_order",
    "../../rtc_base:checks",
    "../../rtc_base:crc32",
    "../../rtc_base:crypto_random",
    "../../rtc_base:digest",
    "../../rtc_base:ip_address",
    "../../rtc_base:logging",
    "../../rtc_base:net_helpers",
    "../../rtc_base:socket_address",
    "../../system_wrappers:metrics",
    "//third_party/abseil-cpp/absl/strings:string_view",
  ]
}
}

if (rtc_include_tests) {
  rtc_source_set("test_feedback_generator_interface") {
    testonly = true
    sources = [ "test/feedback_generator_interface.h" ]
    visibility = [ "*" ]
    deps = [
      ":network_control",
      "..:simulated_network_api",
      "../units:data_rate",
      "../units:data_size",
      "../units:time_delta",
      "../units:timestamp",
    ]
  }
  rtc_library("test_feedback_generator") {
    testonly = true
    sources = [
      "test/create_feedback_generator.cc",
      "test/create_feedback_generator.h",
    ]
    visibility = [ "*" ]
    deps = [
      ":network_control",
      ":test_feedback_generator_interface",
      "../../test/network:feedback_generator",
    ]
  }
}

if (rtc_include_tests) {
  rtc_source_set("stun_unittest") {
    visibility = [ "*" ]
    testonly = true
    sources = [ "stun_unittest.cc" ]
    deps = [
      ":stun_types",
      "..:array_view",
      "../../rtc_base:byte_buffer",
      "../../rtc_base:byte_order",
      "../../rtc_base:ip_address",
      "../../rtc_base:macromagic",
      "../../rtc_base:socket_address",
      "../../system_wrappers:metrics",
      "../../test:test_support",
      "//testing/gtest",
    ]
  }
}

if (rtc_include_tests) {
  rtc_source_set("mock_network_control") {
    visibility = [ "*" ]
    testonly = true
    sources = [ "test/mock_network_control.h" ]
    deps = [
      ":network_control",
      "../../test:test_support",
    ]
  }
}
