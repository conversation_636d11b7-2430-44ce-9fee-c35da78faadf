# Itertools

Extra iterator adaptors, functions and macros.

Please read the [API documentation here](https://docs.rs/itertools/).

How to use with Cargo:

```toml
[dependencies]
itertools = "0.14.0"
```

How to use in your crate:

```rust
use itertools::Itertools;
```

## How to contribute
If you're not sure what to work on, try checking the [help wanted](https://github.com/rust-itertools/itertools/issues?q=is%3Aopen+is%3Aissue+label%3A%22help+wanted%22) label.

See our [CONTRIBUTING.md](https://github.com/rust-itertools/itertools/blob/master/CONTRIBUTING.md) for a detailed guide.

## License

Dual-licensed to be compatible with the Rust project.

Licensed under the Apache License, Version 2.0
https://www.apache.org/licenses/LICENSE-2.0 or the MIT license
https://opensource.org/licenses/MIT, at your
option. This file may not be copied, modified, or distributed
except according to those terms.
