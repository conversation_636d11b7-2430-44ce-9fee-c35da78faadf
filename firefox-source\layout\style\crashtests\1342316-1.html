<!doctype html>
<html class="reftest-wait">
<title>calc() in translate3d as base style of transform animation</title>
<style>
#target {
  width: 100px; height: 100px;
  background: blue;
  animation: anim 1s;
  transform: translate3d(100px, calc(10px + 30%), 10px);
}
@keyframes anim {
  to { transform: translate3d(0px, 0px, 0px); }
}
</style>
<div id="target"></div>
<script>
document.getElementById("target").addEventListener("animationstart", () => {
  document.documentElement.classList.remove("reftest-wait");
});
</script>
