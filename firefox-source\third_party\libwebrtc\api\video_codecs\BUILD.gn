# Copyright (c) 2017 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../webrtc.gni")
if (is_android) {
  import("//chromium/build/config/android/config.gni")
  import("//chromium/build/config/android/rules.gni")
}

rtc_source_set("scalability_mode") {
  visibility = [ "*" ]
  sources = [
    "scalability_mode.cc",
    "scalability_mode.h",
  ]
  deps = [
    "../../rtc_base:checks",
    "../../rtc_base/system:rtc_export",
    "//third_party/abseil-cpp/absl/strings:string_view",
  ]
}

rtc_source_set("scalability_mode_helper") {
  visibility = [ "*" ]
  sources = [
    "scalability_mode_helper.cc",
    "scalability_mode_helper.h",
  ]
  deps = [
    ":scalability_mode",
    "../../modules/video_coding/svc:scalability_mode_util",
    "../../rtc_base/system:rtc_export",
    "//third_party/abseil-cpp/absl/strings:string_view",
  ]
}

rtc_library("video_codecs_api") {
  visibility = [ "*" ]
  sources = [
    "av1_profile.cc",
    "av1_profile.h",
    "h264_profile_level_id.cc",
    "h264_profile_level_id.h",
    "sdp_video_format.cc",
    "sdp_video_format.h",
    "simulcast_stream.cc",
    "simulcast_stream.h",
    "spatial_layer.h",
    "video_codec.cc",
    "video_codec.h",
    "video_decoder.cc",
    "video_decoder.h",
    "video_decoder_factory.cc",
    "video_decoder_factory.h",
    "video_encoder.cc",
    "video_encoder.h",
    "video_encoder_factory.h",
    "vp8_frame_buffer_controller.h",
    "vp8_frame_config.cc",
    "vp8_frame_config.h",
    "vp8_temporal_layers.cc",
    "vp8_temporal_layers.h",
    "vp9_profile.cc",
    "vp9_profile.h",
  ]

  if (rtc_use_h265) {
    sources += [
      "h265_profile_tier_level.cc",
      "h265_profile_tier_level.h",
    ]
  }

  deps = [
    ":scalability_mode",
    "..:fec_controller_api",
    "..:scoped_refptr",
    "../../api:array_view",
    "../../api:rtp_parameters",
    "../../media:media_constants",
    "../../modules/video_coding:codec_globals_headers",
    "../../rtc_base:checks",
    "../../rtc_base:logging",
    "../../rtc_base:macromagic",
    "../../rtc_base:refcount",
    "../../rtc_base:stringutils",
    "../../rtc_base/system:rtc_export",
    "../environment",
    "../units:data_rate",
    "../video:encoded_image",
    "../video:render_resolution",
    "../video:resolution",
    "../video:video_bitrate_allocation",
    "../video:video_codec_constants",
    "../video:video_frame",
    "../video:video_frame_type",
    "../video:video_rtp_headers",
    "//third_party/abseil-cpp/absl/algorithm:container",
    "//third_party/abseil-cpp/absl/container:inlined_vector",
    "//third_party/abseil-cpp/absl/strings",
    "//third_party/abseil-cpp/absl/strings:string_view",
  ]
}

rtc_source_set("bitstream_parser_api") {
  visibility = [ "*" ]
  sources = [ "bitstream_parser.h" ]
  deps = [ "..:array_view" ]
}

rtc_library("builtin_video_decoder_factory") {
  visibility = [ "*" ]
  allow_poison = [
    "audio_codecs",  # TODO(bugs.webrtc.org/8396): Remove.
    "software_video_codecs",
  ]
  sources = [
    "builtin_video_decoder_factory.cc",
    "builtin_video_decoder_factory.h",
  ]

  deps = [
    ":video_codecs_api",
    "../../api:scoped_refptr",
    "../../media:rtc_internal_video_codecs",
    "../../rtc_base/system:rtc_export",
  ]
}

rtc_library("builtin_video_encoder_factory") {
  visibility = [ "*" ]
  allow_poison = [ "software_video_codecs" ]
  sources = [
    "builtin_video_encoder_factory.cc",
    "builtin_video_encoder_factory.h",
  ]

  deps = [
    ":video_codecs_api",
    "../../media:rtc_internal_video_codecs",
    "../../media:rtc_simulcast_encoder_adapter",
    "../../rtc_base/system:rtc_export",
    "../environment",
  ]
}

rtc_source_set("video_encoder_factory_template") {
  visibility = [ "*" ]
  allow_poison = [ "software_video_codecs" ]
  public = [ "video_encoder_factory_template.h" ]

  deps = [
    ":scalability_mode",
    ":video_codecs_api",
    "..:array_view",
    "../../modules/video_coding/svc:scalability_mode_util",
    "../environment",
    "//third_party/abseil-cpp/absl/algorithm:container",
  ]
}

rtc_source_set("video_encoder_factory_template_libvpx_vp8_adapter") {
  visibility = [ "*" ]
  allow_poison = [ "software_video_codecs" ]
  public = [ "video_encoder_factory_template_libvpx_vp8_adapter.h" ]

  deps = [
    ":scalability_mode",
    ":video_codecs_api",
    "../../modules/video_coding:webrtc_vp8",
    "../../modules/video_coding:webrtc_vp8_scalability",
    "../environment",
    "//third_party/abseil-cpp/absl/container:inlined_vector",
  ]
}

rtc_source_set("video_encoder_factory_template_libvpx_vp9_adapter") {
  visibility = [ "*" ]
  allow_poison = [ "software_video_codecs" ]
  public = [ "video_encoder_factory_template_libvpx_vp9_adapter.h" ]

  deps = [
    ":scalability_mode",
    ":video_codecs_api",
    "../../modules/video_coding:webrtc_vp9",
    "../environment",
  ]
}

rtc_source_set("video_encoder_factory_template_open_h264_adapter") {
  visibility = [ "*" ]
  allow_poison = [ "software_video_codecs" ]
  public = [ "video_encoder_factory_template_open_h264_adapter.h" ]

  deps = [
    ":scalability_mode",
    ":video_codecs_api",
    "../../modules/video_coding:webrtc_h264",
    "../environment",
  ]
}

rtc_source_set("video_encoder_factory_template_libaom_av1_adapter") {
  visibility = [ "*" ]
  allow_poison = [ "software_video_codecs" ]
  public = [ "video_encoder_factory_template_libaom_av1_adapter.h" ]

  deps = [
    ":scalability_mode",
    ":video_codecs_api",
    "../../modules/video_coding/codecs/av1:av1_svc_config",
    "../../modules/video_coding/codecs/av1:libaom_av1_encoder",
    "../../modules/video_coding/svc:scalability_mode_util",
    "../environment",
    "//third_party/abseil-cpp/absl/container:inlined_vector",
  ]
}

rtc_source_set("video_decoder_factory_template") {
  visibility = [ "*" ]
  allow_poison = [ "software_video_codecs" ]
  public = [ "video_decoder_factory_template.h" ]

  deps = [
    ":video_codecs_api",
    "..:array_view",
    "../environment",
    "//third_party/abseil-cpp/absl/algorithm:container",
  ]
}

rtc_source_set("video_decoder_factory_template_libvpx_vp8_adapter") {
  visibility = [ "*" ]
  allow_poison = [ "software_video_codecs" ]
  public = [ "video_decoder_factory_template_libvpx_vp8_adapter.h" ]

  deps = [
    ":video_codecs_api",
    "../../modules/video_coding:webrtc_vp8",
    "../environment",
  ]
}

rtc_source_set("video_decoder_factory_template_libvpx_vp9_adapter") {
  visibility = [ "*" ]
  allow_poison = [ "software_video_codecs" ]
  public = [ "video_decoder_factory_template_libvpx_vp9_adapter.h" ]

  deps = [
    ":video_codecs_api",
    "../../modules/video_coding:webrtc_vp9",
  ]
}

rtc_source_set("video_decoder_factory_template_open_h264_adapter") {
  visibility = [ "*" ]
  allow_poison = [ "software_video_codecs" ]
  public = [ "video_decoder_factory_template_open_h264_adapter.h" ]

  deps = [
    ":video_codecs_api",
    "../../modules/video_coding:webrtc_h264",
  ]
}

rtc_source_set("video_decoder_factory_template_dav1d_adapter") {
  visibility = [ "*" ]
  allow_poison = [ "software_video_codecs" ]
  public = [ "video_decoder_factory_template_dav1d_adapter.h" ]

  deps = [
    ":video_codecs_api",
    "../../modules/video_coding/codecs/av1:dav1d_decoder",
  ]
}

rtc_source_set("video_encoding_general") {
  public = [ "video_encoding_general.h" ]
}

rtc_source_set("video_encoder_interface") {
  public = [ "video_encoder_interface.h" ]

  deps = [
    ":video_encoding_general",
    "..:array_view",
    "..:scoped_refptr",
    "../../api/units:data_rate",
    "../../api/units:data_size",
    "../../api/units:time_delta",
    "../../api/units:timestamp",
    "../../api/video:encoded_image",
    "../../api/video:resolution",
    "../../api/video:video_frame",
    "../../api/video_codecs:video_codecs_api",
    "../../rtc_base:rtc_numerics",
    "//third_party/abseil-cpp/absl/functional:any_invocable",
  ]
}

rtc_source_set("video_encoder_factory_interface") {
  public = [ "video_encoder_factory_interface.h" ]

  deps = [
    ":video_encoder_interface",
    ":video_encoding_general",
    "../../api/units:time_delta",
    "../../api/video:resolution",
    "../../rtc_base:rtc_numerics",
    "../video:video_frame",
  ]
}

rtc_library("simple_encoder_wrapper") {
  sources = [
    "simple_encoder_wrapper.cc",
    "simple_encoder_wrapper.h",
  ]

  deps = [
    ":video_encoder_factory_interface",
    ":video_encoder_interface",
    "..:array_view",
    "..:scoped_refptr",
    "../../api/units:data_rate",
    "../../api/video_codecs:scalability_mode",
    "../../api/video_codecs:scalability_mode_helper",
    "../../common_video/generic_frame_descriptor:generic_frame_descriptor",
    "../../modules/video_coding/svc:scalability_structures",
    "../../modules/video_coding/svc:scalable_video_controller",
    "../../rtc_base:checks",
    "../../rtc_base:logging",
    "../../rtc_base:rtc_numerics",
    "../transport/rtp:dependency_descriptor",
    "../units:data_size",
    "../units:frequency",
    "../units:timestamp",
    "../video:video_frame",
    "//third_party/abseil-cpp/absl/algorithm:container",
    "//third_party/abseil-cpp/absl/functional:any_invocable",
    "//third_party/abseil-cpp/absl/strings:string_view",
  ]
}

rtc_library("simple_encoder_wrapper_unittests") {
  testonly = true

  sources = [ "simple_encoder_wrapper_unittests.cc" ]

  deps = [
    ":simple_encoder_wrapper",
    ":video_encoder_factory_interface",
    ":video_encoder_interface",
    ":video_encoding_general",
    "../../api/video:video_frame",
    "../../api/video_codecs:libaom_av1_encoder_factory",
    "../../test:fileutils",
    "../../test:test_support",
    "../../test:video_test_support",
  ]
}

rtc_library("libaom_av1_encoder_factory") {
  sources = [
    "libaom_av1_encoder_factory.cc",
    "libaom_av1_encoder_factory.h",
  ]

  deps = [
    ":video_codecs_api",
    ":video_encoder_factory_interface",
    ":video_encoder_interface",
    ":video_encoding_general",
    "..:array_view",
    "..:scoped_refptr",
    "../../api/units:time_delta",
    "../../rtc_base:checks",
    "../../rtc_base:logging",
    "../../rtc_base:rtc_numerics",
    "../../rtc_base:stringutils",
    "../units:data_rate",
    "../units:data_size",
    "../video:resolution",
    "../video:video_frame",
    "//third_party/abseil-cpp/absl/algorithm:container",
    "//third_party/abseil-cpp/absl/cleanup",
    "//third_party/libaom",
  ]
}

rtc_library("libaom_av1_encoder_factory_test") {
  testonly = true
  sources = [ "libaom_av1_encoder_factory_test.cc" ]
  data = [ "../../resources/reference_video_640x360_30fps.y4m" ]

  deps = [
    ":libaom_av1_encoder_factory",
    ":video_encoder_factory_interface",
    ":video_encoder_interface",
    ":video_encoding_general",
    "..:array_view",
    "..:scoped_refptr",
    "../../api/video:video_frame",
    "../../api/video_codecs:video_codecs_api",
    "../../common_video:common_video",
    "../../modules/video_coding/codecs/av1:dav1d_decoder",
    "../../rtc_base:checks",
    "../../rtc_base:logging",
    "../../test:fileutils",
    "../../test:test_support",
    "../../test:video_frame_writer",
    "../../test:video_test_support",
    "../units:data_rate",
    "../units:data_size",
    "../units:time_delta",
    "../units:timestamp",
    "../video:encoded_image",
  ]
}

rtc_library("vp8_temporal_layers_factory") {
  visibility = [ "*" ]
  allow_poison = [ "software_video_codecs" ]
  sources = [
    "vp8_temporal_layers_factory.cc",
    "vp8_temporal_layers_factory.h",
  ]

  deps = [
    ":video_codecs_api",
    "../:fec_controller_api",
    "../../modules/video_coding:video_coding_utility",
    "../../modules/video_coding:webrtc_vp8_temporal_layers",
    "../../rtc_base:checks",
  ]
}

rtc_library("rtc_software_fallback_wrappers") {
  visibility = [ "*" ]

  sources = [
    "video_decoder_software_fallback_wrapper.cc",
    "video_decoder_software_fallback_wrapper.h",
    "video_encoder_software_fallback_wrapper.cc",
    "video_encoder_software_fallback_wrapper.h",
  ]

  deps = [
    ":video_codecs_api",
    "..:fec_controller_api",
    "..:scoped_refptr",
    "../../api:field_trials_view",
    "../../api/environment",
    "../../api/video:video_frame",
    "../../media:video_common",
    "../../modules/video_coding:video_codec_interface",
    "../../modules/video_coding:video_coding_utility",
    "../../rtc_base:checks",
    "../../rtc_base:event_tracer",
    "../../rtc_base:logging",
    "../../rtc_base/experiments:field_trial_parser",
    "../../rtc_base/system:rtc_export",
    "../../system_wrappers:metrics",
    "../video:encoded_image",
    "../video:video_bitrate_allocation",
    "../video:video_frame",
    "../video:video_frame_type",
    "../video:video_rtp_headers",
    "//third_party/abseil-cpp/absl/strings",
  ]
}
