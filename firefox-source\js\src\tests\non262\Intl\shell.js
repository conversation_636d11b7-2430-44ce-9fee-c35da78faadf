// Generated by make_intl_data.py. DO NOT EDIT.

// source: CLDR file common/bcp47/number.xml; version CLDR 47.
// https://github.com/unicode-org/cldr/blob/master/common/bcp47/number.xml
// https://github.com/unicode-org/cldr/blob/master/common/supplemental/numberingSystems.xml
const numberingSystems = {
  "adlm": {
    "algorithmic": false,
    "digits": "𞥐𞥑𞥒𞥓𞥔𞥕𞥖𞥗𞥘𞥙"
  },
  "ahom": {
    "algorithmic": false,
    "digits": "𑜰𑜱𑜲𑜳𑜴𑜵𑜶𑜷𑜸𑜹"
  },
  "arab": {
    "algorithmic": false,
    "digits": "٠١٢٣٤٥٦٧٨٩"
  },
  "arabext": {
    "algorithmic": false,
    "digits": "۰۱۲۳۴۵۶۷۸۹"
  },
  "armn": {
    "algorithmic": true
  },
  "armnlow": {
    "algorithmic": true
  },
  "bali": {
    "algorithmic": false,
    "digits": "᭐᭑᭒᭓᭔᭕᭖᭗᭘᭙"
  },
  "beng": {
    "algorithmic": false,
    "digits": "০১২৩৪৫৬৭৮৯"
  },
  "bhks": {
    "algorithmic": false,
    "digits": "𑱐𑱑𑱒𑱓𑱔𑱕𑱖𑱗𑱘𑱙"
  },
  "brah": {
    "algorithmic": false,
    "digits": "𑁦𑁧𑁨𑁩𑁪𑁫𑁬𑁭𑁮𑁯"
  },
  "cakm": {
    "algorithmic": false,
    "digits": "𑄶𑄷𑄸𑄹𑄺𑄻𑄼𑄽𑄾𑄿"
  },
  "cham": {
    "algorithmic": false,
    "digits": "꩐꩑꩒꩓꩔꩕꩖꩗꩘꩙"
  },
  "cyrl": {
    "algorithmic": true
  },
  "deva": {
    "algorithmic": false,
    "digits": "०१२३४५६७८९"
  },
  "diak": {
    "algorithmic": false,
    "digits": "𑥐𑥑𑥒𑥓𑥔𑥕𑥖𑥗𑥘𑥙"
  },
  "ethi": {
    "algorithmic": true
  },
  "fullwide": {
    "algorithmic": false,
    "digits": "０１２３４５６７８９"
  },
  "gara": {
    "algorithmic": false,
    "digits": "𐵀𐵁𐵂𐵃𐵄𐵅𐵆𐵇𐵈𐵉"
  },
  "geor": {
    "algorithmic": true
  },
  "gong": {
    "algorithmic": false,
    "digits": "𑶠𑶡𑶢𑶣𑶤𑶥𑶦𑶧𑶨𑶩"
  },
  "gonm": {
    "algorithmic": false,
    "digits": "𑵐𑵑𑵒𑵓𑵔𑵕𑵖𑵗𑵘𑵙"
  },
  "grek": {
    "algorithmic": true
  },
  "greklow": {
    "algorithmic": true
  },
  "gujr": {
    "algorithmic": false,
    "digits": "૦૧૨૩૪૫૬૭૮૯"
  },
  "gukh": {
    "algorithmic": false,
    "digits": "𖄰𖄱𖄲𖄳𖄴𖄵𖄶𖄷𖄸𖄹"
  },
  "guru": {
    "algorithmic": false,
    "digits": "੦੧੨੩੪੫੬੭੮੯"
  },
  "hanidays": {
    "algorithmic": true
  },
  "hanidec": {
    "algorithmic": false,
    "digits": "〇一二三四五六七八九"
  },
  "hans": {
    "algorithmic": true
  },
  "hansfin": {
    "algorithmic": true
  },
  "hant": {
    "algorithmic": true
  },
  "hantfin": {
    "algorithmic": true
  },
  "hebr": {
    "algorithmic": true
  },
  "hmng": {
    "algorithmic": false,
    "digits": "𖭐𖭑𖭒𖭓𖭔𖭕𖭖𖭗𖭘𖭙"
  },
  "hmnp": {
    "algorithmic": false,
    "digits": "𞅀𞅁𞅂𞅃𞅄𞅅𞅆𞅇𞅈𞅉"
  },
  "java": {
    "algorithmic": false,
    "digits": "꧐꧑꧒꧓꧔꧕꧖꧗꧘꧙"
  },
  "jpan": {
    "algorithmic": true
  },
  "jpanfin": {
    "algorithmic": true
  },
  "jpanyear": {
    "algorithmic": true
  },
  "kali": {
    "algorithmic": false,
    "digits": "꤀꤁꤂꤃꤄꤅꤆꤇꤈꤉"
  },
  "kawi": {
    "algorithmic": false,
    "digits": "𑽐𑽑𑽒𑽓𑽔𑽕𑽖𑽗𑽘𑽙"
  },
  "khmr": {
    "algorithmic": false,
    "digits": "០១២៣៤៥៦៧៨៩"
  },
  "knda": {
    "algorithmic": false,
    "digits": "೦೧೨೩೪೫೬೭೮೯"
  },
  "krai": {
    "algorithmic": false,
    "digits": "𖵰𖵱𖵲𖵳𖵴𖵵𖵶𖵷𖵸𖵹"
  },
  "lana": {
    "algorithmic": false,
    "digits": "᪀᪁᪂᪃᪄᪅᪆᪇᪈᪉"
  },
  "lanatham": {
    "algorithmic": false,
    "digits": "᪐᪑᪒᪓᪔᪕᪖᪗᪘᪙"
  },
  "laoo": {
    "algorithmic": false,
    "digits": "໐໑໒໓໔໕໖໗໘໙"
  },
  "latn": {
    "algorithmic": false,
    "digits": "0123456789"
  },
  "lepc": {
    "algorithmic": false,
    "digits": "᱀᱁᱂᱃᱄᱅᱆᱇᱈᱉"
  },
  "limb": {
    "algorithmic": false,
    "digits": "᥆᥇᥈᥉᥊᥋᥌᥍᥎᥏"
  },
  "mathbold": {
    "algorithmic": false,
    "digits": "𝟎𝟏𝟐𝟑𝟒𝟓𝟔𝟕𝟖𝟗"
  },
  "mathdbl": {
    "algorithmic": false,
    "digits": "𝟘𝟙𝟚𝟛𝟜𝟝𝟞𝟟𝟠𝟡"
  },
  "mathmono": {
    "algorithmic": false,
    "digits": "𝟶𝟷𝟸𝟹𝟺𝟻𝟼𝟽𝟾𝟿"
  },
  "mathsanb": {
    "algorithmic": false,
    "digits": "𝟬𝟭𝟮𝟯𝟰𝟱𝟲𝟳𝟴𝟵"
  },
  "mathsans": {
    "algorithmic": false,
    "digits": "𝟢𝟣𝟤𝟥𝟦𝟧𝟨𝟩𝟪𝟫"
  },
  "mlym": {
    "algorithmic": false,
    "digits": "൦൧൨൩൪൫൬൭൮൯"
  },
  "modi": {
    "algorithmic": false,
    "digits": "𑙐𑙑𑙒𑙓𑙔𑙕𑙖𑙗𑙘𑙙"
  },
  "mong": {
    "algorithmic": false,
    "digits": "᠐᠑᠒᠓᠔᠕᠖᠗᠘᠙"
  },
  "mroo": {
    "algorithmic": false,
    "digits": "𖩠𖩡𖩢𖩣𖩤𖩥𖩦𖩧𖩨𖩩"
  },
  "mtei": {
    "algorithmic": false,
    "digits": "꯰꯱꯲꯳꯴꯵꯶꯷꯸꯹"
  },
  "mymr": {
    "algorithmic": false,
    "digits": "၀၁၂၃၄၅၆၇၈၉"
  },
  "mymrepka": {
    "algorithmic": false,
    "digits": "𑛚𑛛𑛜𑛝𑛞𑛟𑛠𑛡𑛢𑛣"
  },
  "mymrpao": {
    "algorithmic": false,
    "digits": "𑛐𑛑𑛒𑛓𑛔𑛕𑛖𑛗𑛘𑛙"
  },
  "mymrshan": {
    "algorithmic": false,
    "digits": "႐႑႒႓႔႕႖႗႘႙"
  },
  "mymrtlng": {
    "algorithmic": false,
    "digits": "꧰꧱꧲꧳꧴꧵꧶꧷꧸꧹"
  },
  "nagm": {
    "algorithmic": false,
    "digits": "𞓰𞓱𞓲𞓳𞓴𞓵𞓶𞓷𞓸𞓹"
  },
  "newa": {
    "algorithmic": false,
    "digits": "𑑐𑑑𑑒𑑓𑑔𑑕𑑖𑑗𑑘𑑙"
  },
  "nkoo": {
    "algorithmic": false,
    "digits": "߀߁߂߃߄߅߆߇߈߉"
  },
  "olck": {
    "algorithmic": false,
    "digits": "᱐᱑᱒᱓᱔᱕᱖᱗᱘᱙"
  },
  "onao": {
    "algorithmic": false,
    "digits": "𞗱𞗲𞗳𞗴𞗵𞗶𞗷𞗸𞗹𞗺"
  },
  "orya": {
    "algorithmic": false,
    "digits": "୦୧୨୩୪୫୬୭୮୯"
  },
  "osma": {
    "algorithmic": false,
    "digits": "𐒠𐒡𐒢𐒣𐒤𐒥𐒦𐒧𐒨𐒩"
  },
  "outlined": {
    "algorithmic": false,
    "digits": "𜳰𜳱𜳲𜳳𜳴𜳵𜳶𜳷𜳸𜳹"
  },
  "rohg": {
    "algorithmic": false,
    "digits": "𐴰𐴱𐴲𐴳𐴴𐴵𐴶𐴷𐴸𐴹"
  },
  "roman": {
    "algorithmic": true
  },
  "romanlow": {
    "algorithmic": true
  },
  "saur": {
    "algorithmic": false,
    "digits": "꣐꣑꣒꣓꣔꣕꣖꣗꣘꣙"
  },
  "segment": {
    "algorithmic": false,
    "digits": "🯰🯱🯲🯳🯴🯵🯶🯷🯸🯹"
  },
  "shrd": {
    "algorithmic": false,
    "digits": "𑇐𑇑𑇒𑇓𑇔𑇕𑇖𑇗𑇘𑇙"
  },
  "sind": {
    "algorithmic": false,
    "digits": "𑋰𑋱𑋲𑋳𑋴𑋵𑋶𑋷𑋸𑋹"
  },
  "sinh": {
    "algorithmic": false,
    "digits": "෦෧෨෩෪෫෬෭෮෯"
  },
  "sora": {
    "algorithmic": false,
    "digits": "𑃰𑃱𑃲𑃳𑃴𑃵𑃶𑃷𑃸𑃹"
  },
  "sund": {
    "algorithmic": false,
    "digits": "᮰᮱᮲᮳᮴᮵᮶᮷᮸᮹"
  },
  "sunu": {
    "algorithmic": false,
    "digits": "𑯰𑯱𑯲𑯳𑯴𑯵𑯶𑯷𑯸𑯹"
  },
  "takr": {
    "algorithmic": false,
    "digits": "𑛀𑛁𑛂𑛃𑛄𑛅𑛆𑛇𑛈𑛉"
  },
  "talu": {
    "algorithmic": false,
    "digits": "᧐᧑᧒᧓᧔᧕᧖᧗᧘᧙"
  },
  "taml": {
    "algorithmic": true
  },
  "tamldec": {
    "algorithmic": false,
    "digits": "௦௧௨௩௪௫௬௭௮௯"
  },
  "telu": {
    "algorithmic": false,
    "digits": "౦౧౨౩౪౫౬౭౮౯"
  },
  "thai": {
    "algorithmic": false,
    "digits": "๐๑๒๓๔๕๖๗๘๙"
  },
  "tibt": {
    "algorithmic": false,
    "digits": "༠༡༢༣༤༥༦༧༨༩"
  },
  "tirh": {
    "algorithmic": false,
    "digits": "𑓐𑓑𑓒𑓓𑓔𑓕𑓖𑓗𑓘𑓙"
  },
  "tnsa": {
    "algorithmic": false,
    "digits": "𖫀𖫁𖫂𖫃𖫄𖫅𖫆𖫇𖫈𖫉"
  },
  "vaii": {
    "algorithmic": false,
    "digits": "꘠꘡꘢꘣꘤꘥꘦꘧꘨꘩"
  },
  "wara": {
    "algorithmic": false,
    "digits": "𑣠𑣡𑣢𑣣𑣤𑣥𑣦𑣧𑣨𑣩"
  },
  "wcho": {
    "algorithmic": false,
    "digits": "𞋰𞋱𞋲𞋳𞋴𞋵𞋶𞋷𞋸𞋹"
  }
};
