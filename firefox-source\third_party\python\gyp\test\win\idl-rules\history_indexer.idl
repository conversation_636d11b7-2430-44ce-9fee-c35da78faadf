// Copyright (c) 2012 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import "oaidl.idl";
import "ocidl.idl";

[
  object,
  uuid(9C1100DD-51D4-4827-AE9F-3B8FAC4AED72),
  oleautomation,
  nonextensible,
  pointer_default(unique)
]
interface IChromeHistoryIndexer : IUnknown {
  HRESULT SomeFunction([in] VARIANT begin_time, [in] VARIANT end_time);
};
