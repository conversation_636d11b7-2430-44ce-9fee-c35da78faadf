/* -*- indent-tabs-mode: nil; js-indent-level: 2 -*- */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */


/**
 *  File Name:          RegExp/octal-001.js
 *  ECMA Section:       15.7.1
 *  Description:        Based on ECMA 2 Draft 7 February 1999
 *  Simple test cases for matching OctalEscapeSequences.
 *  Author:             <EMAIL>
 *  Date:               19 February 1999
 */
var SECTION = "RegExp/octal-001.js";
var TITLE   = "RegExp patterns that contain OctalEscapeSequences";
var BUGNUMBER="http://scopus/bugsplat/show_bug.cgi?id=346196";

printBugNumber(BUGNUMBER);


// backreference
AddRegExpCases(
  /(.)\1/,
  "/(.)\\1/",
  "HI!!",
  "HI!",
  2,
  ["!!", "!"] );

test();

function AddRegExpCases(
  regexp, str_regexp, pattern, str_pattern, index, matches_array ) {

  // prevent a runtime error

  if ( regexp.exec(pattern) == null || matches_array == null ) {
    AddTestCase(
      regexp + ".exec(" + str_pattern +")",
      matches_array,
      regexp.exec(pattern) );

    return;
  }
  AddTestCase(
    str_regexp + ".exec(" + str_pattern +").length",
    matches_array.length,
    regexp.exec(pattern).length );

  AddTestCase(
    str_regexp + ".exec(" + str_pattern +").index",
    index,
    regexp.exec(pattern).index );

  AddTestCase(
    str_regexp + ".exec(" + str_pattern +").input",
    pattern,
    regexp.exec(pattern).input );

  AddTestCase(
    str_regexp + ".exec(" + str_pattern +").toString()",
    matches_array.toString(),
    regexp.exec(pattern).toString() );
/*
  var limit = matches_array.length > regexp.exec(pattern).length
  ? matches_array.length
  : regexp.exec(pattern).length;

  for ( var matches = 0; matches < limit; matches++ ) {
  AddTestCase(
  str_regexp + ".exec(" + str_pattern +")[" + matches +"]",
  matches_array[matches],
  regexp.exec(pattern)[matches] );
  }
*/
}
