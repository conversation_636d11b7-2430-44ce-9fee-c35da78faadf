[{"description": "These tests are copied from https://github.com/mathiasbynens/punycode.js/blob/main/tests/tests.js , used under the MIT license.", "decoded": "", "encoded": ""}, {"description": "a single basic code point", "decoded": "Bach", "encoded": "Bach-"}, {"description": "a single non-ASCII character", "decoded": "ü", "encoded": "tda"}, {"description": "multiple non-ASCII characters", "decoded": "üëäö♥", "encoded": "4can8av2009b"}, {"description": "mix of ASCII and non-ASCII characters", "decoded": "<PERSON><PERSON><PERSON>", "encoded": "bcher-kva"}, {"description": "long string with both ASCII and non-ASCII characters", "decoded": "Willst du die Blüthe des frühen, die Früchte des späteren Jahres", "encoded": "Willst du die Blthe des frhen, die Frchte des spteren Jahres-x9e96lkal"}, {"description": "Arabic (Egyptian)", "decoded": "ليهمابتكلموشعربي؟", "encoded": "egbpdaj6bu4bxfgehfvwxn"}, {"description": "Chinese (simplified)", "decoded": "他们为什么不说中文", "encoded": "ihqwcrb4cv8a8dqg056pqjye"}, {"description": "Chinese (traditional)", "decoded": "他們爲什麽不說中文", "encoded": "ihqwctvzc91f659drss3x8bo0yb"}, {"description": "Czech", "decoded": "Pročprostěnemluv<PERSON>č<PERSON>ky", "encoded": "Proprostnemluvesky-uyb24dma41a"}, {"description": "Hebrew", "decoded": "למההםפשוטלאמדבריםעברית", "encoded": "4dbcagdahymbxekheh6e0a7fei0b"}, {"description": "Hindi (Devanagari)", "decoded": "यहलोगहिन्दीक्योंनहींबोलसकतेहैं", "encoded": "i1baa7eci9glrd9b2ae1bj0hfcgg6iyaf8o0a1dig0cd"}, {"description": "Japanese (kanji and hiragana)", "decoded": "なぜみんな日本語を話してくれないのか", "encoded": "n8jok5ay5dzabd5bym9f0cm5685rrjetr6pdxa"}, {"description": "Korean (Hangul syllables)", "decoded": "세계의모든사람들이한국어를이해한다면얼마나좋을까", "encoded": "989aomsvi5e83db1d2a355cv1e0vak1dwrv93d5xbh15a0dt30a5jpsd879ccm6fea98c"}, {"description": "Russian (Cyrillic)", "decoded": "почемужеонинеговорятпорусски", "encoded": "b1abfaaepdrnnbgefbadotcwatmq2g4l"}, {"description": "Spanish", "decoded": "PorquénopuedensimplementehablarenEspañol", "encoded": "PorqunopuedensimplementehablarenEspaol-fmd56a"}, {"description": "Vietnamese", "decoded": "TạisaohọkhôngthểchỉnóitiếngViệt", "encoded": "TisaohkhngthchnitingVit-kjcr8268qyxafd2f1b9g"}, {"decoded": "3年B組金八先生", "encoded": "3B-ww4c5e180e575a65lsy2b"}, {"decoded": "安室奈美恵-with-SUPER-MONKEYS", "encoded": "-with-SUPER-MONKEYS-pc58ag80a8qai00g7n9n"}, {"decoded": "Hello-Another-Way-それぞれの場所", "encoded": "Hello-Another-Way--fc4qua05auwb3674vfr0b"}, {"decoded": "ひとつ屋根の下2", "encoded": "2-u9tlzr9756bt3uc0v"}, {"decoded": "MajiでKoiする5秒前", "encoded": "MajiKoi5-783gue6qz075azm5e"}, {"decoded": "パフィーdeルンバ", "encoded": "de-jg4avhby1noc0d"}, {"decoded": "そのスピードで", "encoded": "d9juau41awczczp"}, {"description": "ASCII string that breaks the existing rules for host-name labels (It's not a realistic example for IDNA, because IDNA never encodes pure ASCII labels.)", "decoded": "-> $1.00 <-", "encoded": "-> $1.00 <--"}]