# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.56.0"
name = "bitflags"
version = "2.9.0"
authors = ["The Rust Project Developers"]
build = false
exclude = [
    "/tests",
    "/.github",
]
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = """
A macro to generate structures which behave like bitflags.
"""
homepage = "https://github.com/bitflags/bitflags"
documentation = "https://docs.rs/bitflags"
readme = "README.md"
keywords = [
    "bit",
    "bitmask",
    "bitflags",
    "flags",
]
categories = ["no-std"]
license = "MIT OR Apache-2.0"
repository = "https://github.com/bitflags/bitflags"

[package.metadata.docs.rs]
features = ["example_generated"]

[features]
example_generated = []
rustc-dep-of-std = [
    "core",
    "compiler_builtins",
]
std = []

[lib]
name = "bitflags"
path = "src/lib.rs"

[[example]]
name = "custom_bits_type"
path = "examples/custom_bits_type.rs"

[[example]]
name = "custom_derive"
path = "examples/custom_derive.rs"

[[example]]
name = "fmt"
path = "examples/fmt.rs"

[[example]]
name = "macro_free"
path = "examples/macro_free.rs"

[[example]]
name = "serde"
path = "examples/serde.rs"

[[bench]]
name = "parse"
path = "benches/parse.rs"

[dependencies.arbitrary]
version = "1.0"
optional = true

[dependencies.bytemuck]
version = "1.12"
optional = true

[dependencies.compiler_builtins]
version = "0.1.2"
optional = true

[dependencies.core]
version = "1.0.0"
optional = true
package = "rustc-std-workspace-core"

[dependencies.serde]
version = "1.0.103"
optional = true
default-features = false

[dev-dependencies.arbitrary]
version = "1.0"
features = ["derive"]

[dev-dependencies.bytemuck]
version = "1.12.2"
features = ["derive"]

[dev-dependencies.rustversion]
version = "1.0"

[dev-dependencies.serde_derive]
version = "1.0.103"

[dev-dependencies.serde_json]
version = "1.0"

[dev-dependencies.serde_test]
version = "1.0.19"

[dev-dependencies.trybuild]
version = "1.0.18"

[dev-dependencies.zerocopy]
version = "0.8"
features = ["derive"]
