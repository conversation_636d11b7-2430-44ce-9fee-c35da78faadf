/* -*- Mode: C++; tab-width: 8; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* vim: set ts=8 sts=2 et sw=2 tw=80: */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

CSS_FONT_DESC(font-family, Family)
CSS_FONT_DESC(font-style, Style)
CSS_FONT_DESC(font-weight, Weight)
CSS_FONT_DESC(font-stretch, Stretch)
CSS_FONT_DESC(src, Src)
CSS_FONT_DESC(unicode-range, UnicodeRange)
CSS_FONT_DESC(font-feature-settings, FontFeatureSettings)
CSS_FONT_DESC(font-variation-settings, FontVariationSettings)
CSS_FONT_DESC(font-language-override, FontLanguageOverride)
CSS_FONT_DESC(font-display, Display)
CSS_FONT_DESC(ascent-override, AscentOverride)
CSS_FONT_DESC(descent-override, DescentOverride)
CSS_FONT_DESC(line-gap-override, LineGapOverride)
CSS_FONT_DESC(size-adjust, SizeAdjust)
