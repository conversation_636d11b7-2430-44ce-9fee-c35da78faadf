# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.61"
name = "syn"
version = "2.0.87"
authors = ["<PERSON> <<EMAIL>>"]
build = false
include = [
    "/benches/**",
    "/Cargo.toml",
    "/LICENSE-APACHE",
    "/LICENSE-MIT",
    "/README.md",
    "/src/**",
    "/tests/**",
]
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "Parser for Rust source code"
documentation = "https://docs.rs/syn"
readme = "README.md"
keywords = [
    "macros",
    "syn",
]
categories = [
    "development-tools::procedural-macro-helpers",
    "parser-implementations",
]
license = "MIT OR Apache-2.0"
repository = "https://github.com/dtolnay/syn"

[package.metadata.docs.rs]
all-features = true
rustdoc-args = [
    "--generate-link-to-definition",
    "--extend-css=src/gen/token.css",
]
targets = ["x86_64-unknown-linux-gnu"]

[package.metadata.playground]
features = [
    "full",
    "visit",
    "visit-mut",
    "fold",
    "extra-traits",
]

[lib]
name = "syn"
path = "src/lib.rs"
doc-scrape-examples = false

[[test]]
name = "regression"
path = "tests/regression.rs"

[[test]]
name = "test_asyncness"
path = "tests/test_asyncness.rs"

[[test]]
name = "test_attribute"
path = "tests/test_attribute.rs"

[[test]]
name = "test_derive_input"
path = "tests/test_derive_input.rs"

[[test]]
name = "test_expr"
path = "tests/test_expr.rs"

[[test]]
name = "test_generics"
path = "tests/test_generics.rs"

[[test]]
name = "test_grouping"
path = "tests/test_grouping.rs"

[[test]]
name = "test_ident"
path = "tests/test_ident.rs"

[[test]]
name = "test_item"
path = "tests/test_item.rs"

[[test]]
name = "test_iterators"
path = "tests/test_iterators.rs"

[[test]]
name = "test_lit"
path = "tests/test_lit.rs"

[[test]]
name = "test_meta"
path = "tests/test_meta.rs"

[[test]]
name = "test_parse_buffer"
path = "tests/test_parse_buffer.rs"

[[test]]
name = "test_parse_quote"
path = "tests/test_parse_quote.rs"

[[test]]
name = "test_parse_stream"
path = "tests/test_parse_stream.rs"

[[test]]
name = "test_pat"
path = "tests/test_pat.rs"

[[test]]
name = "test_path"
path = "tests/test_path.rs"

[[test]]
name = "test_precedence"
path = "tests/test_precedence.rs"

[[test]]
name = "test_receiver"
path = "tests/test_receiver.rs"

[[test]]
name = "test_round_trip"
path = "tests/test_round_trip.rs"

[[test]]
name = "test_shebang"
path = "tests/test_shebang.rs"

[[test]]
name = "test_size"
path = "tests/test_size.rs"

[[test]]
name = "test_stmt"
path = "tests/test_stmt.rs"

[[test]]
name = "test_token_trees"
path = "tests/test_token_trees.rs"

[[test]]
name = "test_ty"
path = "tests/test_ty.rs"

[[test]]
name = "test_unparenthesize"
path = "tests/test_unparenthesize.rs"

[[test]]
name = "test_visibility"
path = "tests/test_visibility.rs"

[[test]]
name = "zzz_stable"
path = "tests/zzz_stable.rs"

[[bench]]
name = "file"
path = "benches/file.rs"
required-features = [
    "full",
    "parsing",
]

[[bench]]
name = "rust"
path = "benches/rust.rs"
harness = false
required-features = [
    "full",
    "parsing",
]

[dependencies.proc-macro2]
version = "1.0.83"
default-features = false

[dependencies.quote]
version = "1.0.35"
optional = true
default-features = false

[dependencies.unicode-ident]
version = "1"

[dev-dependencies.anyhow]
version = "1"

[dev-dependencies.automod]
version = "1"

[dev-dependencies.insta]
version = "1"

[dev-dependencies.ref-cast]
version = "1"

[dev-dependencies.rustversion]
version = "1"

[dev-dependencies.syn-test-suite]
version = "0"

[dev-dependencies.termcolor]
version = "1"

[features]
clone-impls = []
default = [
    "derive",
    "parsing",
    "printing",
    "clone-impls",
    "proc-macro",
]
derive = []
extra-traits = []
fold = []
full = []
parsing = []
printing = ["dep:quote"]
proc-macro = [
    "proc-macro2/proc-macro",
    "quote?/proc-macro",
]
test = ["syn-test-suite/all-features"]
visit = []
visit-mut = []

[target."cfg(not(miri))".dev-dependencies.flate2]
version = "1"

[target."cfg(not(miri))".dev-dependencies.rayon]
version = "1"

[target."cfg(not(miri))".dev-dependencies.reqwest]
version = "0.12"
features = ["blocking"]

[target."cfg(not(miri))".dev-dependencies.tar]
version = "0.4.16"

[target."cfg(not(miri))".dev-dependencies.walkdir]
version = "2.3.2"
