<?xml version="1.0" encoding="utf-8"?>
<!-- This Source Code Form is subject to the terms of the Mozilla Public
   - License, v. 2.0. If a copy of the MPL was not distributed with this
   - file, You can obtain one at http://mozilla.org/MPL/2.0/. -->
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:layout_height="wrap_content"
    tools:layout_width="wrap_content">

    <androidx.cardview.widget.CardView
        android:id="@+id/search_selector"
        android:layout_width="40dp"
        android:layout_height="28dp"
        android:layout_marginHorizontal="4dp"
        android:layout_marginVertical="10dp"
        app:cardBackgroundColor="@color/mozac_compose_browse_toolbar_search_selector_background_color"
        app:cardCornerRadius="4dp"
        app:cardElevation="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/tab_item"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginVertical="2dp"
            android:layout_marginStart="2dp"
            android:layout_marginEnd="4dp">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                app:shapeAppearanceOverlay="@style/SearchSelectorIconStyle"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@drawable/mozac_ic_search_24" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/arrow"
                android:layout_width="6dp"
                android:layout_height="6dp"
                android:importantForAccessibility="no"
                app:srcCompat="@drawable/mozac_compose_browser_toolbar_chevron_down_6"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>
</merge>
