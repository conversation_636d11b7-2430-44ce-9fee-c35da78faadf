/**
 * <PERSON><PERSON><PERSON> Browser - Test Helpers
 * Utility functions for Playwright tests
 */

/**
 * Create a test page with cookie banner
 */
function createCookieBannerPage(bannerType = 'basic') {
  const bannerConfigs = {
    basic: {
      html: `
        <div id="cookie-banner" style="position:fixed;top:0;width:100%;background:#333;color:white;padding:10px;z-index:9999;">
          <p>We use cookies to improve your experience.</p>
          <button id="accept-cookies">Accept</button>
          <button id="reject-cookies">Reject</button>
        </div>
      `,
      selector: '#cookie-banner'
    },
    modal: {
      html: `
        <div class="cookie-modal-overlay" style="position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:9999;">
          <div class="cookie-modal" style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:white;padding:20px;border-radius:8px;">
            <h2><PERSON>ie <PERSON>sent</h2>
            <p>This website uses cookies for analytics and personalization.</p>
            <div>
              <button class="accept-all">Accept All</button>
              <button class="reject-all">Reject All</button>
              <button class="customize">Customize</button>
            </div>
          </div>
        </div>
      `,
      selector: '.cookie-modal-overlay'
    },
    gdpr: {
      html: `
        <div class="gdpr-banner" style="position:fixed;bottom:0;width:100%;background:#2196F3;color:white;padding:15px;z-index:9999;">
          <div style="max-width:1200px;margin:0 auto;display:flex;align-items:center;justify-content:space-between;">
            <div>
              <strong>GDPR Compliance</strong>
              <p style="margin:5px 0;">We process your data in accordance with GDPR regulations.</p>
            </div>
            <div>
              <button id="gdpr-accept" style="background:white;color:#2196F3;border:none;padding:8px 16px;margin:0 5px;border-radius:4px;">Accept</button>
              <button id="gdpr-decline" style="background:transparent;color:white;border:1px solid white;padding:8px 16px;margin:0 5px;border-radius:4px;">Decline</button>
            </div>
          </div>
        </div>
      `,
      selector: '.gdpr-banner'
    },
    complex: {
      html: `
        <div id="complex-consent-manager" style="position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.8);z-index:9999;">
          <div style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:white;width:80%;max-width:600px;border-radius:12px;overflow:hidden;">
            <div style="background:#1976D2;color:white;padding:20px;">
              <h2>Manage Your Privacy</h2>
            </div>
            <div style="padding:20px;">
              <div class="consent-category">
                <h3>Essential Cookies</h3>
                <p>Required for basic site functionality</p>
                <input type="checkbox" checked disabled>
              </div>
              <div class="consent-category">
                <h3>Analytics Cookies</h3>
                <p>Help us understand how visitors use our site</p>
                <input type="checkbox" id="analytics-consent">
              </div>
              <div class="consent-category">
                <h3>Marketing Cookies</h3>
                <p>Used to deliver personalized advertisements</p>
                <input type="checkbox" id="marketing-consent">
              </div>
            </div>
            <div style="padding:20px;border-top:1px solid #eee;text-align:right;">
              <button id="save-preferences" style="background:#1976D2;color:white;border:none;padding:10px 20px;margin:0 5px;border-radius:4px;">Save Preferences</button>
              <button id="accept-all-complex" style="background:#4CAF50;color:white;border:none;padding:10px 20px;margin:0 5px;border-radius:4px;">Accept All</button>
              <button id="reject-all-complex" style="background:#f44336;color:white;border:none;padding:10px 20px;margin:0 5px;border-radius:4px;">Reject All</button>
            </div>
          </div>
        </div>
      `,
      selector: '#complex-consent-manager'
    }
  };
  
  const config = bannerConfigs[bannerType] || bannerConfigs.basic;
  
  return {
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Cookie Banner Test - ${bannerType}</title>
          <meta charset="utf-8">
        </head>
        <body>
          <h1>Test Page with ${bannerType} Cookie Banner</h1>
          <p>This is the main content of the page.</p>
          <p>The cookie banner should be blocked by Megisto Browser.</p>
          ${config.html}
        </body>
      </html>
    `,
    selector: config.selector
  };
}

/**
 * Create a test page with YouTube embed
 */
function createYouTubeEmbedPage(embedType = 'basic', videoId = 'dQw4w9WgXcQ') {
  const embedConfigs = {
    basic: `<iframe width="560" height="315" src="https://www.youtube.com/embed/${videoId}" frameborder="0" allowfullscreen></iframe>`,
    
    withParams: `<iframe width="560" height="315" src="https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0&modestbranding=1" frameborder="0" allowfullscreen></iframe>`,
    
    multiple: `
      <iframe width="560" height="315" src="https://www.youtube.com/embed/${videoId}" frameborder="0" allowfullscreen></iframe>
      <iframe width="560" height="315" src="https://www.youtube.com/embed/jNQXAC9IVRw" frameborder="0" allowfullscreen></iframe>
    `,
    
    responsive: `
      <div style="position: relative; padding-bottom: 56.25%; height: 0; overflow: hidden;">
        <iframe src="https://www.youtube.com/embed/${videoId}" 
                style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;" 
                frameborder="0" allowfullscreen></iframe>
      </div>
    `,
    
    playlist: `<iframe width="560" height="315" src="https://www.youtube.com/embed/videoseries?list=PLrAXtmRdnEQy6nuLMHjMZOz59Oq8HmPME" frameborder="0" allowfullscreen></iframe>`
  };
  
  const embedHtml = embedConfigs[embedType] || embedConfigs.basic;
  
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <title>YouTube Embed Test - ${embedType}</title>
        <meta charset="utf-8">
      </head>
      <body>
        <h1>YouTube Embed Test - ${embedType}</h1>
        <p>This page contains YouTube embeds that should be enhanced by Megisto Browser.</p>
        <div class="video-container">
          ${embedHtml}
        </div>
        <p>Content after the video embed.</p>
      </body>
    </html>
  `;
}

/**
 * Create a test page with popup triggers
 */
function createPopupTestPage() {
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <title>Popup Test Page</title>
        <meta charset="utf-8">
      </head>
      <body>
        <h1>Popup Test Page</h1>
        <p>This page contains various popup triggers that should be blocked.</p>
        
        <button id="window-open-popup" onclick="window.open('about:blank', 'popup', 'width=400,height=300')">
          Open Window Popup
        </button>
        
        <button id="target-blank-popup" onclick="document.getElementById('hidden-link').click()">
          Target Blank Popup
        </button>
        
        <a id="hidden-link" href="javascript:void(0)" target="_blank" style="display:none;">Hidden Link</a>
        
        <button id="delayed-popup" onclick="setTimeout(() => window.open('about:blank'), 1000)">
          Delayed Popup
        </button>
        
        <button id="multiple-popups" onclick="for(let i=0; i<3; i++) window.open('about:blank')">
          Multiple Popups
        </button>
        
        <script>
          // Automatic popup on load (should be blocked)
          setTimeout(() => {
            window.open('about:blank', 'auto-popup');
          }, 2000);
          
          // Popup on click anywhere (should be blocked)
          document.addEventListener('click', (e) => {
            if (e.target.id === 'trigger-popup-click') {
              window.open('about:blank', 'click-popup');
            }
          });
        </script>
        
        <div id="trigger-popup-click" style="width:100px;height:100px;background:red;margin:20px;">
          Click me for popup
        </div>
      </body>
    </html>
  `;
}

/**
 * Wait for element to be removed (useful for testing blocking)
 */
async function waitForElementRemoval(page, selector, timeout = 5000) {
  try {
    await page.waitForSelector(selector, { state: 'detached', timeout });
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Wait for Video.js player to be initialized
 */
async function waitForVideoJSPlayer(page, timeout = 10000) {
  try {
    await page.waitForFunction(
      () => window.videojs && document.querySelector('.video-js'),
      { timeout }
    );
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Check if element is actually visible (not just present in DOM)
 */
async function isElementVisible(page, selector) {
  try {
    const element = await page.locator(selector);
    const count = await element.count();
    
    if (count === 0) return false;
    
    const isVisible = await element.first().isVisible();
    const boundingBox = await element.first().boundingBox();
    
    return isVisible && boundingBox && boundingBox.width > 0 && boundingBox.height > 0;
  } catch (error) {
    return false;
  }
}

/**
 * Get browser console logs
 */
async function getConsoleLogs(page) {
  const logs = [];
  
  page.on('console', msg => {
    logs.push({
      type: msg.type(),
      text: msg.text(),
      timestamp: new Date().toISOString()
    });
  });
  
  return logs;
}

/**
 * Simulate user interaction delay
 */
async function humanDelay(min = 100, max = 500) {
  const delay = Math.floor(Math.random() * (max - min + 1)) + min;
  await new Promise(resolve => setTimeout(resolve, delay));
}

/**
 * Take screenshot with timestamp
 */
async function takeTimestampedScreenshot(page, name) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `${name}-${timestamp}.png`;
  await page.screenshot({ path: `test-results/screenshots/${filename}` });
  return filename;
}

/**
 * Check network requests for blocked content
 */
async function monitorBlockedRequests(page) {
  const blockedRequests = [];
  
  page.on('requestfailed', request => {
    blockedRequests.push({
      url: request.url(),
      method: request.method(),
      failure: request.failure()?.errorText,
      timestamp: new Date().toISOString()
    });
  });
  
  return blockedRequests;
}

/**
 * Validate Megisto extension is loaded
 */
async function validateMegistoExtension(page) {
  try {
    // Check for Megisto-specific global variables or functions
    const megistoLoaded = await page.evaluate(() => {
      return !!(window.megistoInjected || 
                document.querySelector('[data-megisto]') ||
                window.MegistoCore);
    });
    
    return megistoLoaded;
  } catch (error) {
    console.warn('Could not validate Megisto extension:', error);
    return false;
  }
}

module.exports = {
  createCookieBannerPage,
  createYouTubeEmbedPage,
  createPopupTestPage,
  waitForElementRemoval,
  waitForVideoJSPlayer,
  isElementVisible,
  getConsoleLogs,
  humanDelay,
  takeTimestampedScreenshot,
  monitorBlockedRequests,
  validateMegistoExtension
};
