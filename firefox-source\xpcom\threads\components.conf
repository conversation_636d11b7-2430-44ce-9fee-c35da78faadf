# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

Classes = [
    {
        'cid': '{03d68f92-9513-4e25-9be9-7cb239874172}',
        'contract_ids': ['@mozilla.org/process/environment;1'],
        'legacy_constructor': 'nsEnvironment::Create',
        'headers': ['/xpcom/threads/nsEnvironment.h'],
        'js_name': 'env',
        'interfaces': ['nsIEnvironment'],
    },
    {
        'cid': '{5ff24248-1dd2-11b2-8427-fbab44f29bc8}',
        'contract_ids': ['@mozilla.org/timer;1'],
        'legacy_constructor': 'nsTimer::XPCOMConstructor',
        'headers': ['/xpcom/threads/nsTimerImpl.h'],
        'processes': ProcessSelector.ALLOW_IN_GPU_RDD_SOCKET_AND_UTILITY_PROCESS,
    },
    {
        'cid': '{d39a8904-2e09-4a3a-a273-c3bec7db2bfe}',
        'contract_ids': ['@mozilla.org/timer-manager;1'],
        'headers': ['/xpcom/threads/nsTimerImpl.h'],
        'type': 'nsTimerManager',
    },
]
