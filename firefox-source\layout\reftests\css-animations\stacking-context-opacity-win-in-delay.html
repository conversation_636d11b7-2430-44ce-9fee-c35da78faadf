<!DOCTYPE html>
<title>
Opacity animation winning over another opacity animation in delay phase
creates a stacking context
</title>
<style>
span {
  height: 100px;
  width: 100px;
  position: fixed;
  background: green;
  top: 50px;
}
@keyframes Opacity1 {
  from, to { opacity: 1; }
}
@keyframes Opacity0 {
  from, to { opacity: 0; }
}
#test {
  width: 100px; height: 100px;
  background: blue;
  animation: Opacity0 100s 100s, Opacity1 100s;
}
</style>
<span></span>
<div id="test"></div>
