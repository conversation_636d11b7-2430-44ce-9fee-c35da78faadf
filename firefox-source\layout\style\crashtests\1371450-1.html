<!DOCTYPE html>
<html class="reftest-wait">
<title></title>
<style>
@keyframes anim {
  to { transform: rotate(360deg); }
}
#animation {
  animation: anim 3s infinite;
}
.red {
  color: red;
}
</style>
<div><span id="target">text</span></div>
<div id="animation">animation</div>
<script>
window.addEventListener('load', () => {
  var target = document.getElementById('target');
  target.classList.add('red');
  setTimeout(() => {
    target.classList.remove('red');
    SpecialPowers.getDOMWindowUtils(window)
                 .sendMouseEvent("mousemove", 100, 100, 1,
                                 0, 1, 0);
    setTimeout(() => {
      document.documentElement.classList.remove("reftest-wait");
    }, 0);
  }, 0);
  SpecialPowers.getDOMWindowUtils(window)
               .sendMouseEvent("mousemove", 100, 100, 1,
                               0, 1, 0);
});
</script>
