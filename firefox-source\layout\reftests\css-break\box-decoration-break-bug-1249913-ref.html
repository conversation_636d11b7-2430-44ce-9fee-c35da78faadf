<!DOCTYPE html>
<!--
     Any copyright is dedicated to the Public Domain.
     http://creativecommons.org/publicdomain/zero/1.0/
-->
<html><head>
  <meta charset="utf-8">
  <title>Reference: 'box-decoration-break' with child overflow</title>
  <link rel="author" title="<PERSON><PERSON>" href="https://bugzilla.mozilla.org/show_bug.cgi?id=1249913">
  <style type="text/css">
body,html { color:black; background:white; font-size:16px; padding:0; margin:0; }

p { height: 27px; width: 100px; margin:0; background:lime; }
.border { border:3px solid black; }
.outline { outline:3px solid brown; }
.shadow { box-shadow: 2px 2px 0 0 blue; }

.columns {
      -ms-columns: 2;
  -webkit-columns: 2;
          columns: 2;
      -ms-column-fill: auto;
  -webkit-column-fill: auto;
          column-fill: auto;
  width:500px;
  height:30px;
  border:solid silver;
  margin: 2px 0;
}

.columns div { height: 10px; }

x { height: 27px; width: 100px; margin:0; background:lime; display:block; }
y { height: 23px; width: 100px; margin:0; background:lime; display:block; }

</style>
</head>
<body>

<div class="columns"><div class="border"><p></p><y></y></div></div>
<div class="columns"><div class="outline"><p style="height:30px"></p><y style="height:20px"></y></div></div>
<div class="columns"><div class="shadow"><p style="height:30px"></p><y style="height:20px"></y></div></div>
<div class="columns"><div class="border outline"><p></p><y></y></div></div>
<div class="columns"><div class="border shadow"><p></p><y></y></div></div>

<div class="columns"><div class="border"><x></x><y></y></div></div>
<div class="columns"><div class="outline"><x></x><y></y></div></div>
<div class="columns"><div class="shadow"><x></x><y></y></div></div>
<div class="columns"><div class="border outline"><x></x><y></y></div></div>
<div class="columns"><div class="border shadow"><x></x><y></y></div></div>

</body>
</html>
