# Cookie

[![CI Status](https://github.com/SergioBenitez/cookie-rs/workflows/CI/badge.svg)](https://github.com/SergioBenitez/cookie-rs/actions)
[![Current Crates.io Version](https://img.shields.io/crates/v/cookie.svg)](https://crates.io/crates/cookie)
[![Documentation](https://docs.rs/cookie/badge.svg)](https://docs.rs/cookie)

A Rust library for parsing HTTP cookies and managing cookie jars.

# Usage

Add the following to your `Cargo.toml`:

```toml
[dependencies]
cookie = "0.16"
```

See the [documentation](http://docs.rs/cookie) for detailed usage information.

# MSRV

The minimum supported `rustc` version for cookie `0.16` is `1.53`.

The minimum supported `rustc` version for cookie `0.15` is `1.41`.

# License

This project is licensed under either of

 * Apache License, Version 2.0, ([LICENSE-APACHE](LICENSE-APACHE) or
   http://www.apache.org/licenses/LICENSE-2.0)
 * MIT license ([LICENSE-MIT](LICENSE-MIT) or
   http://opensource.org/licenses/MIT)

at your option.

### Contribution

Unless you explicitly state otherwise, any contribution intentionally submitted
for inclusion in `cookie-rs` by you, as defined in the Apache-2.0 license, shall
be dual licensed as above, without any additional terms or conditions.
