/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#include "nsISupports.idl"

interface nsIFile;
interface nsIURL;

/**
 * Provides access to crash reporting functionality.
 *
 * @status UNSTABLE - This interface is not frozen and will probably change in
 *                    future releases.
 */

[scriptable, uuid(4b74c39a-cf69-4a8a-8e63-169d81ad1ecf)]
interface nsICrashReporter : nsISupports
{
  /**
   * Get the enabled status of the crash reporter.
   */
  readonly attribute boolean crashReporterEnabled;

  /**
   * Enable or disable crash reporting at runtime. Not available to script
   * because the JS engine relies on proper exception handler chaining.
   */
  [noscript]
  void setEnabled(in boolean enabled);

  /**
   * Get or set the URL to which crash reports will be submitted.
   * Only https and http URLs are allowed, as the submission is handled
   * by OS-native networking libraries.
   *
   * @throw NS_ERROR_NOT_INITIALIZED if crash reporting is not initialized
   * @throw NS_ERROR_INVALID_ARG on set if a non-http(s) URL is assigned
   * @throw NS_ERROR_FAILURE on get if no URL is set
   */
  attribute nsIURL serverURL;

  /**
   * Get or set the path on the local system to which minidumps will be
   * written when a crash happens.
   *
   * @throw NS_ERROR_NOT_INITIALIZED if crash reporting is not initialized
   */
  attribute nsIFile minidumpPath;

  /**
   * Get the minidump file corresponding to the specified ID.
   *
   * @param id
   *        ID of the crash. Likely a UUID.
   *
   * @return The minidump file associated with the ID.
   *
   * @throw NS_ERROR_FILE_NOT_FOUND if the minidump could not be found
   */
  nsIFile getMinidumpForID(in AString id);

  /**
   * Get the extra file corresponding to the specified ID.
   *
   * @param id
   *        ID of the crash. Likely a UUID.
   *
   * @return The extra file associated with the ID.
   *
   * @throw NS_ERROR_FILE_NOT_FOUND if the extra file could not be found
   */
  nsIFile getExtraFileForID(in AString id);

  /**
   * Add some extra data to be submitted with a crash report.
   *
   * @param key
   *        Name of a known crash annotation constant.
   * @param data
   *        Data to be added. This can be a string, a 32-bit integer or a
   *        boolean. The type must match the type of the annotation as
   *        specified in CrashAnnotations.yaml.
   *
   * @throw NS_ERROR_NOT_INITIALIZED if crash reporting not initialized
   * @throw NS_ERROR_INVALID_ARG if key contains an invalid value, data is a
   *                             string and contains invalid characters, or
   *                             data does not match the annotation type
   * @throw NS_ERROR_OUT_OF_MEMORY if there was insufficient memory to
   *                               convert a JavaScript string to UTF-8
   */
  [implicit_jscontext]
  void annotateCrashReport(in AUTF8String key, in jsval data);

  /**
   * Remove a crash report annotation.
   *
   * @param key
   *        Name of a known crash annotation constant.
   *
   * @throw NS_ERROR_NOT_INITIALIZED if crash reporting not initialized
   * @throw NS_ERROR_INVALID_ARG if key contains an invalid value.
   */
  void removeCrashReportAnnotation(in AUTF8String key);

  /**
   * Checks if an annotation is valid.
   *
   * @param key
   *        Name of a crash annotation constant.
   *
   * @return True if the specified value is a valid annotation.
   */
  boolean isAnnotationValid(in ACString value);

  /**
   * Checks if an annotation is allowed for inclusion in the crash ping.
   *
   * @param key
   *        Name of a known crash annotation constant.
   *
   * @return True if the specified value is a valid annotation and can be
             included in the crash ping, false otherwise.
   * @throw NS_ERROR_INVALID_ARG if key contains an invalid value.
   */
  boolean isAnnotationAllowedForPing(in ACString value);

  /**
   * Checks if an annotation is allowed for inclusion in a crash report.
   *
   * @param key
   *        Name of a known crash annotation constant.
   *
   * @return True if the specified value is a valid annotation and can be
             included in a crash report, false otherwise.
   * @throw NS_ERROR_INVALID_ARG if key contains an invalid value.
   */
  boolean isAnnotationAllowedForReport(in ACString value);

  /**
   * Append some data to the "Notes" field, to be submitted with a crash report.
   * Unlike annotateCrashReport, this method will append to existing data.
   *
   * @param data
   *        Data to be added.
   *
   * @throw NS_ERROR_NOT_INITIALIZED if crash reporting not initialized
   * @throw NS_ERROR_INVALID_ARG if data contains invalid characters.
   *                             The only invalid character is '\0'.
   */
  void appendAppNotesToCrashReport(in ACString data);

  /**
   * Register a given memory range to be included in the crash report.
   *
   * @param ptr
   *        The starting address for the bytes.
   * @param size
   *        The number of bytes to include.
   *
   * @throw NS_ERROR_NOT_INITIALIZED if crash reporting not initialized
   * @throw NS_ERROR_NOT_IMPLEMENTED if unavailable on the current OS
   */
  void registerAppMemory(in unsigned long long ptr, in unsigned long long size);

  /**
   * Write a minidump immediately, with the user-supplied exception
   * information. This is implemented on Windows only, because
   * SEH (structured exception handling) exists on Windows only.
   *
   * @param aExceptionInfo  EXCEPTION_INFO* provided by Window's SEH
   */
  [noscript] void writeMinidumpForException(in voidPtr aExceptionInfo);

  /**
   * Append note containing an Obj-C exception's info.
   *
   * @param aException  NSException object to append note for
   */
  [noscript] void appendObjCExceptionInfoToAppNotes(in voidPtr aException);

  /**
   * User preference for submitting crash reports.
   */
  attribute boolean submitReports;

  /**
   * Cause the crash reporter to re-evaluate where crash events should go.
   *
   * This should be called during application startup and whenever profiles
   * change.
   */
  void UpdateCrashEventsDir();

  /**
   * Save an anonymized memory report file for inclusion in a future crash
   * report in this session.
   *
   * @throws NS_ERROR_NOT_INITIALIZED if crash reporting is disabled.
   */
  void saveMemoryReport();
};
