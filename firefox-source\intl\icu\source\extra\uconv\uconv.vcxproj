﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Label="Globals">
    <ProjectGuid>{DBA4088D-F6F9-4F8F-8820-082A4765C16C}</ProjectGuid>
  </PropertyGroup>
  <PropertyGroup Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <!-- The following import will include the 'default' configuration options for VS projects. -->
  <Import Project="..\..\allinone\Build.Windows.ProjectConfiguration.props" />
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.30319.1</_ProjectFileVersion>
    <OutDir>.\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>.\$(Platform)\$(Configuration)\</IntDir>
    <MakeCFG>$(Platform)\$(Configuration)</MakeCFG>
    <!-- The ICU projects use "Win32" to mean "x86", so we need to special case it. -->
    <OutDir Condition="'$(Platform)'=='Win32'">.\x86\$(Configuration)\</OutDir>
    <IntDir Condition="'$(Platform)'=='Win32'">.\x86\$(Configuration)\</IntDir>
    <MakeCFG Condition="'$(Platform)'=='Win32'">x86\$(Configuration)</MakeCFG>
    <!-- Disable Incremental Linking for Release builds as it prevents Link-time Code Generation -->
    <LinkIncremental Condition="'$(Configuration)'=='Debug'">true</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)'=='Release'">false</LinkIncremental>
  </PropertyGroup>
  <!-- Options that are common to *all* configurations -->
  <ItemDefinitionGroup>
    <Midl>
      <TypeLibraryName>$(OutDir)\uconv.tlb</TypeLibraryName>
    </Midl>
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <CompileAs>Default</CompileAs>
      <AdditionalIncludeDirectories>..\..\..\include;..\..\common;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>UCONVMSG_LINK;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PrecompiledHeaderOutputFile>$(OutDir)\uconv.pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>$(OutDir)/</AssemblerListingLocation>
      <ObjectFileName>$(OutDir)/</ObjectFileName>
      <ProgramDataBaseFileName>$(OutDir)\uconv.pdb</ProgramDataBaseFileName>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <OutputFile>$(OutDir)\uconv.exe</OutputFile>
      <AdditionalDependencies>uconvmsg.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(OutDir);..\..\..\$(IcuLibOutputDir);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Link>
    <CustomBuildStep>
      <Command>copy "$(TargetPath)" ..\..\..\$(IcuBinOutputDir)</Command>
      <Outputs>..\..\..\$(IcuBinOutputDir)\$(TargetFileName);%(Outputs)</Outputs>
    </CustomBuildStep>
  </ItemDefinitionGroup>
  <!-- Options that are common to all 'Debug' project configurations -->
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Debug'">
    <ClCompile>
      <BrowseInformation>true</BrowseInformation>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <AdditionalDependencies>icuucd.lib;icuind.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <!-- Options that are common to all 'Release' project configurations -->
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Release'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
    </ClCompile>
    <Link>
      <AdditionalDependencies>icuuc.lib;icuin.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="uconv.cpp" />
    <ClCompile Include="uwmsg.c" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="unicode\uwmsg.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="resources\fr.txt" />
    <None Include="resources\root.txt" />
    <CustomBuild Include="makedata.mak">
      <Command>nmake /nologo /f %(Filename).mak icup="$(ProjectDir)..\..\.." CFG=$(MakeCFG)</Command>
      <Outputs>$(MakeCFG)\uconvmsg.lib;%(Outputs)</Outputs>
    </CustomBuild>
    <None Include="resfiles.mk" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>