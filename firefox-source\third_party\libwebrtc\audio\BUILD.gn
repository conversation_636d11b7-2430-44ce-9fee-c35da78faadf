# Copyright (c) 2015 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../webrtc.gni")
if (is_android) {
  import("//chromium/build/config/android/config.gni")
  import("//chromium/build/config/android/rules.gni")
}

rtc_library("audio") {
  sources = [
    "audio_level.cc",
    "audio_level.h",
    "audio_receive_stream.cc",
    "audio_receive_stream.h",
    "audio_send_stream.cc",
    "audio_send_stream.h",
    "audio_state.cc",
    "audio_state.h",
    "audio_transport_impl.cc",
    "audio_transport_impl.h",
    "channel_receive.cc",
    "channel_receive.h",
    "channel_receive_frame_transformer_delegate.cc",
    "channel_receive_frame_transformer_delegate.h",
    "channel_send.cc",
    "channel_send.h",
    "channel_send_frame_transformer_delegate.cc",
    "channel_send_frame_transformer_delegate.h",
    "conversion.h",
    "remix_resample.cc",
    "remix_resample.h",
  ]

  deps = [
    "../api:array_view",
    "../api:bitrate_allocation",
    "../api:call_api",
    "../api:field_trials_view",
    "../api:frame_transformer_interface",
    "../api:function_view",
    "../api:make_ref_counted",
    "../api:rtp_headers",
    "../api:rtp_packet_info",
    "../api:rtp_parameters",
    "../api:scoped_refptr",
    "../api:sequence_checker",
    "../api:transport_api",
    "../api/audio:aec3_factory",
    "../api/audio:audio_device",
    "../api/audio:audio_frame_api",
    "../api/audio:audio_frame_processor",
    "../api/audio:audio_mixer_api",
    "../api/audio:audio_processing",
    "../api/audio_codecs:audio_codecs_api",
    "../api/crypto:frame_decryptor_interface",
    "../api/crypto:frame_encryptor_interface",
    "../api/crypto:options",
    "../api/environment",
    "../api/neteq:default_neteq_factory",
    "../api/neteq:neteq_api",
    "../api/rtc_event_log",
    "../api/task_queue",
    "../api/task_queue:pending_task_safety_flag",
    "../api/transport/rtp:rtp_source",
    "../api/units:data_rate",
    "../api/units:data_size",
    "../api/units:time_delta",
    "../api/units:timestamp",
    "../call:audio_sender_interface",
    "../call:bitrate_allocator",
    "../call:call_interfaces",
    "../call:rtp_interfaces",
    "../common_audio",
    "../common_audio:common_audio_c",
    "../logging:rtc_event_audio",
    "../logging:rtc_stream_config",
    "../media:media_channel",
    "../media:media_channel_impl",
    "../modules/async_audio_processing",
    "../modules/audio_coding",
    "../modules/audio_coding:audio_coding_module_typedefs",
    "../modules/audio_coding:audio_encoder_cng",
    "../modules/audio_coding:audio_network_adaptor_config",
    "../modules/audio_coding:red",
    "../modules/audio_device",
    "../modules/audio_processing",
    "../modules/audio_processing:audio_frame_proxies",
    "../modules/audio_processing:rms_level",
    "../modules/pacing",
    "../modules/rtp_rtcp",
    "../modules/rtp_rtcp:rtp_rtcp_format",
    "../rtc_base:audio_format_to_string",
    "../rtc_base:buffer",
    "../rtc_base:checks",
    "../rtc_base:event_tracer",
    "../rtc_base:logging",
    "../rtc_base:macromagic",
    "../rtc_base:race_checker",
    "../rtc_base:rate_limiter",
    "../rtc_base:refcount",
    "../rtc_base:rtc_event",
    "../rtc_base:rtc_numerics",
    "../rtc_base:safe_conversions",
    "../rtc_base:safe_minmax",
    "../rtc_base:stringutils",
    "../rtc_base:threading",
    "../rtc_base:timeutils",
    "../rtc_base/containers:flat_set",
    "../rtc_base/experiments:field_trial_parser",
    "../rtc_base/synchronization:mutex",
    "../rtc_base/system:no_unique_address",
    "../rtc_base/task_utils:repeating_task",
    "../system_wrappers",
    "../system_wrappers:metrics",
    "utility:audio_frame_operations",
    "//third_party/abseil-cpp/absl/functional:any_invocable",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/strings:string_view",
  ]
}
if (rtc_include_tests) {
  rtc_library("audio_end_to_end_test") {
    testonly = true

    sources = [
      "test/audio_end_to_end_test.cc",
      "test/audio_end_to_end_test.h",
    ]
    deps = [
      ":audio",
      "../api:simulated_network_api",
      "../api/audio:audio_device",
      "../api/task_queue",
      "../call:fake_network",
      "../modules/audio_device:test_audio_device_module",
      "../system_wrappers",
      "../test:test_common",
      "../test:test_support",
      "../test:video_test_constants",
    ]
  }

  rtc_library("audio_tests") {
    testonly = true

    sources = [
      "audio_receive_stream_unittest.cc",
      "audio_send_stream_tests.cc",
      "audio_send_stream_unittest.cc",
      "audio_state_unittest.cc",
      "channel_receive_frame_transformer_delegate_unittest.cc",
      "channel_send_frame_transformer_delegate_unittest.cc",
      "channel_send_unittest.cc",
      "mock_voe_channel_proxy.h",
      "remix_resample_unittest.cc",
      "test/audio_stats_test.cc",
      "test/nack_test.cc",
      "test/non_sender_rtt_test.cc",
    ]
    deps = [
      ":audio",
      ":audio_end_to_end_test",
      ":channel_receive_unittest",
      "../api:array_view",
      "../api:bitrate_allocation",
      "../api:call_api",
      "../api:frame_transformer_factory",
      "../api:frame_transformer_interface",
      "../api:function_view",
      "../api:libjingle_peerconnection_api",
      "../api:make_ref_counted",
      "../api:mock_audio_mixer",
      "../api:mock_frame_decryptor",
      "../api:mock_frame_encryptor",
      "../api:mock_frame_transformer",
      "../api:mock_transformable_audio_frame",
      "../api:rtc_error_matchers",
      "../api:rtp_headers",
      "../api:rtp_parameters",
      "../api:scoped_refptr",
      "../api:simulated_network_api",
      "../api:transport_api",
      "../api/audio:audio_frame_api",
      "../api/audio:audio_mixer_api",
      "../api/audio:audio_processing_statistics",
      "../api/audio_codecs:audio_codecs_api",
      "../api/audio_codecs:builtin_audio_encoder_factory",
      "../api/audio_codecs/opus:audio_decoder_opus",
      "../api/audio_codecs/opus:audio_encoder_opus",
      "../api/crypto:frame_decryptor_interface",
      "../api/crypto:frame_encryptor_interface",
      "../api/crypto:options",
      "../api/environment",
      "../api/environment:environment_factory",
      "../api/task_queue",
      "../api/task_queue:default_task_queue_factory",
      "../api/task_queue/test:mock_task_queue_base",
      "../api/transport:bitrate_settings",
      "../api/transport:network_control",
      "../api/transport/rtp:rtp_source",
      "../api/units:data_rate",
      "../api/units:data_size",
      "../api/units:time_delta",
      "../api/units:timestamp",
      "../call:bitrate_allocator",
      "../call:call_interfaces",
      "../call:mock_bitrate_allocator",
      "../call:mock_call_interfaces",
      "../call:mock_rtp_interfaces",
      "../call:rtp_interfaces",
      "../call:rtp_receiver",
      "../call:rtp_sender",
      "../common_audio",
      "../modules/audio_coding:audio_coding_module_typedefs",
      "../modules/audio_device:audio_device_impl",  # For TestAudioDeviceModule
      "../modules/audio_device:mock_audio_device",
      "../modules/audio_mixer:audio_mixer_impl",
      "../modules/audio_mixer:audio_mixer_test_utils",
      "../modules/audio_processing:mocks",
      "../modules/pacing",
      "../modules/rtp_rtcp:mock_rtp_rtcp",
      "../modules/rtp_rtcp:rtp_rtcp",
      "../modules/rtp_rtcp:rtp_rtcp_format",
      "../rtc_base:checks",
      "../rtc_base:gunit_helpers",
      "../rtc_base:macromagic",
      "../rtc_base:refcount",
      "../rtc_base:rtc_base_tests_utils",
      "../rtc_base:safe_compare",
      "../rtc_base:task_queue_for_test",
      "../rtc_base:threading",
      "../rtc_base:timeutils",
      "../system_wrappers",
      "../test:audio_codec_mocks",
      "../test:field_trial",
      "../test:mock_transport",
      "../test:rtp_test_utils",
      "../test:run_loop",
      "../test:scoped_key_value_config",
      "../test:test_common",
      "../test:test_support",
      "../test:video_test_constants",
      "../test:wait_until",
      "../test/time_controller:time_controller",
      "utility:utility_tests",
      "//testing/gmock",
      "//testing/gtest",
      "//third_party/abseil-cpp/absl/memory",
      "//third_party/abseil-cpp/absl/strings:string_view",
    ]
  }

  rtc_library("channel_receive_unittest") {
    testonly = true
    sources = [ "channel_receive_unittest.cc" ]
    deps = [
      ":audio",
      "../api:mock_frame_transformer",
      "../api/audio:audio_device",
      "../api/audio_codecs:builtin_audio_decoder_factory",
      "../api/crypto:frame_decryptor_interface",
      "../api/environment:environment_factory",
      "../api/task_queue:default_task_queue_factory",
      "../logging:mocks",
      "../modules/audio_device:mock_audio_device",
      "../modules/rtp_rtcp",
      "../modules/rtp_rtcp:ntp_time_util",
      "../modules/rtp_rtcp:rtp_rtcp_format",
      "../rtc_base:logging",
      "../rtc_base:threading",
      "../test:audio_codec_mocks",
      "../test:mock_transport",
      "../test:test_support",
      "../test/time_controller",
      "//third_party/abseil-cpp/absl/strings",
    ]
  }
}
