# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
rust-version = "1.57"
name = "idna"
version = "1.0.3"
authors = ["The rust-url developers"]
build = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "IDNA (Internationalizing Domain Names in Applications) and Punycode."
readme = "README.md"
keywords = [
    "no_std",
    "web",
    "http",
]
license = "MIT OR Apache-2.0"
repository = "https://github.com/servo/rust-url/"

[package.metadata.docs.rs]
rustdoc-args = ["--generate-link-to-definition"]

[lib]
name = "idna"
path = "src/lib.rs"
doctest = false

[[test]]
name = "tests"
path = "tests/tests.rs"
harness = false

[[test]]
name = "unit"
path = "tests/unit.rs"

[[test]]
name = "unitbis"
path = "tests/unitbis.rs"

[[bench]]
name = "all"
path = "benches/all.rs"
harness = false

[dependencies.idna_adapter]
version = "1"

[dependencies.smallvec]
version = "1.13.1"
features = ["const_generics"]

[dependencies.utf8_iter]
version = "1.0.4"

[dev-dependencies.assert_matches]
version = "1.3"

[dev-dependencies.bencher]
version = "0.1"

[dev-dependencies.serde_json]
version = "1.0"

[dev-dependencies.tester]
version = "0.9"

[features]
alloc = []
compiled_data = ["idna_adapter/compiled_data"]
default = [
    "std",
    "compiled_data",
]
std = ["alloc"]
