# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies
#
# If you believe there's an error in this file please file an
# issue against the rust-lang/cargo repository. If you're
# editing this file be aware that the upstream Cargo.toml
# will likely look very different (and much more reasonable)

[package]
name = "lazy_static"
version = "1.4.0"
authors = ["<PERSON> <<EMAIL>>"]
exclude = ["/.travis.yml", "/appveyor.yml"]
description = "A macro for declaring lazily evaluated statics in Rust."
documentation = "https://docs.rs/lazy_static"
readme = "README.md"
keywords = ["macro", "lazy", "static"]
categories = ["no-std", "rust-patterns", "memory-management"]
license = "MIT/Apache-2.0"
repository = "https://github.com/rust-lang-nursery/lazy-static.rs"
[dependencies.spin]
version = "0.5.0"
optional = true
[dev-dependencies.doc-comment]
version = "0.3.1"

[features]
spin_no_std = ["spin"]
[badges.appveyor]
repository = "rust-lang-nursery/lazy-static.rs"

[badges.is-it-maintained-issue-resolution]
repository = "rust-lang-nursery/lazy-static.rs"

[badges.is-it-maintained-open-issues]
repository = "rust-lang-nursery/lazy-static.rs"

[badges.maintenance]
status = "passively-maintained"

[badges.travis-ci]
repository = "rust-lang-nursery/lazy-static.rs"
