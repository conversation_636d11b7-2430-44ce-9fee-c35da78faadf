# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.74"
name = "clap"
version = "4.5.16"
build = false
include = [
    "build.rs",
    "src/**/*",
    "Cargo.toml",
    "LICENSE*",
    "README.md",
    "benches/**/*",
    "examples/**/*",
]
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "A simple to use, efficient, and full-featured Command Line Argument Parser"
readme = "README.md"
keywords = [
    "argument",
    "cli",
    "arg",
    "parser",
    "parse",
]
categories = ["command-line-interface"]
license = "MIT OR Apache-2.0"
repository = "https://github.com/clap-rs/clap"

[package.metadata.docs.rs]
cargo-args = [
    "-Zunstable-options",
    "-Zrustdoc-scrape-examples",
]
features = ["unstable-doc"]
rustdoc-args = [
    "--cfg",
    "docsrs",
    "--generate-link-to-definition",
]

[package.metadata.playground]
features = ["unstable-doc"]

[package.metadata.release]
shared-version = true
tag-name = "v{{version}}"

[[package.metadata.release.pre-release-replacements]]
file = "CHANGELOG.md"
min = 1
replace = "{{version}}"
search = "Unreleased"

[[package.metadata.release.pre-release-replacements]]
exactly = 1
file = "CHANGELOG.md"
replace = "...{{tag_name}}"
search = '\.\.\.HEAD'

[[package.metadata.release.pre-release-replacements]]
file = "CHANGELOG.md"
min = 1
replace = "{{date}}"
search = "ReleaseDate"

[[package.metadata.release.pre-release-replacements]]
exactly = 1
file = "CHANGELOG.md"
replace = """
<!-- next-header -->
## [Unreleased] - ReleaseDate
"""
search = "<!-- next-header -->"

[[package.metadata.release.pre-release-replacements]]
exactly = 1
file = "CHANGELOG.md"
replace = """
<!-- next-url -->
[Unreleased]: https://github.com/clap-rs/clap/compare/{{tag_name}}...HEAD"""
search = "<!-- next-url -->"

[[package.metadata.release.pre-release-replacements]]
file = "CITATION.cff"
replace = "date-released: {{date}}"
search = "^date-released: ....-..-.."

[[package.metadata.release.pre-release-replacements]]
file = "CITATION.cff"
replace = "version: {{version}}"
search = '^version: .+\..+\..+'

[[package.metadata.release.pre-release-replacements]]
exactly = 1
file = "src/lib.rs"
replace = "blob/v{{version}}/CHANGELOG.md"
search = 'blob/v.+\..+\..+/CHANGELOG.md'

[profile.bench]
lto = true
codegen-units = 1

[profile.test]
opt-level = 1

[lib]
name = "clap"
path = "src/lib.rs"
bench = false

[[bin]]
name = "stdio-fixture"
path = "src/bin/stdio-fixture.rs"

[[example]]
name = "01_quick"
path = "examples/tutorial_builder/01_quick.rs"
required-features = ["cargo"]

[[example]]
name = "01_quick_derive"
path = "examples/tutorial_derive/01_quick.rs"
required-features = ["derive"]

[[example]]
name = "02_app_settings"
path = "examples/tutorial_builder/02_app_settings.rs"
required-features = ["cargo"]

[[example]]
name = "02_app_settings_derive"
path = "examples/tutorial_derive/02_app_settings.rs"
required-features = ["derive"]

[[example]]
name = "02_apps"
path = "examples/tutorial_builder/02_apps.rs"

[[example]]
name = "02_apps_derive"
path = "examples/tutorial_derive/02_apps.rs"
required-features = ["derive"]

[[example]]
name = "02_crate"
path = "examples/tutorial_builder/02_crate.rs"
required-features = ["cargo"]

[[example]]
name = "02_crate_derive"
path = "examples/tutorial_derive/02_crate.rs"
required-features = ["derive"]

[[example]]
name = "03_01_flag_bool"
path = "examples/tutorial_builder/03_01_flag_bool.rs"
required-features = ["cargo"]

[[example]]
name = "03_01_flag_bool_derive"
path = "examples/tutorial_derive/03_01_flag_bool.rs"
required-features = ["derive"]

[[example]]
name = "03_01_flag_count"
path = "examples/tutorial_builder/03_01_flag_count.rs"
required-features = ["cargo"]

[[example]]
name = "03_01_flag_count_derive"
path = "examples/tutorial_derive/03_01_flag_count.rs"
required-features = ["derive"]

[[example]]
name = "03_02_option"
path = "examples/tutorial_builder/03_02_option.rs"
required-features = ["cargo"]

[[example]]
name = "03_02_option_derive"
path = "examples/tutorial_derive/03_02_option.rs"
required-features = ["derive"]

[[example]]
name = "03_02_option_mult"
path = "examples/tutorial_builder/03_02_option_mult.rs"
required-features = ["cargo"]

[[example]]
name = "03_02_option_mult_derive"
path = "examples/tutorial_derive/03_02_option_mult.rs"
required-features = ["derive"]

[[example]]
name = "03_03_positional"
path = "examples/tutorial_builder/03_03_positional.rs"
required-features = ["cargo"]

[[example]]
name = "03_03_positional_derive"
path = "examples/tutorial_derive/03_03_positional.rs"
required-features = ["derive"]

[[example]]
name = "03_03_positional_mult"
path = "examples/tutorial_builder/03_03_positional_mult.rs"
required-features = ["cargo"]

[[example]]
name = "03_03_positional_mult_derive"
path = "examples/tutorial_derive/03_03_positional_mult.rs"
required-features = ["derive"]

[[example]]
name = "03_04_subcommands"
path = "examples/tutorial_builder/03_04_subcommands.rs"
required-features = ["cargo"]

[[example]]
name = "03_04_subcommands_alt_derive"
path = "examples/tutorial_derive/03_04_subcommands_alt.rs"
required-features = ["derive"]

[[example]]
name = "03_04_subcommands_derive"
path = "examples/tutorial_derive/03_04_subcommands.rs"
required-features = ["derive"]

[[example]]
name = "03_05_default_values"
path = "examples/tutorial_builder/03_05_default_values.rs"
required-features = ["cargo"]

[[example]]
name = "03_05_default_values_derive"
path = "examples/tutorial_derive/03_05_default_values.rs"
required-features = ["derive"]

[[example]]
name = "04_01_enum"
path = "examples/tutorial_builder/04_01_enum.rs"
required-features = ["cargo"]

[[example]]
name = "04_01_enum_derive"
path = "examples/tutorial_derive/04_01_enum.rs"
required-features = ["derive"]

[[example]]
name = "04_01_possible"
path = "examples/tutorial_builder/04_01_possible.rs"
required-features = ["cargo"]

[[example]]
name = "04_02_parse"
path = "examples/tutorial_builder/04_02_parse.rs"
required-features = ["cargo"]

[[example]]
name = "04_02_parse_derive"
path = "examples/tutorial_derive/04_02_parse.rs"
required-features = ["derive"]

[[example]]
name = "04_02_validate"
path = "examples/tutorial_builder/04_02_validate.rs"
required-features = ["cargo"]

[[example]]
name = "04_02_validate_derive"
path = "examples/tutorial_derive/04_02_validate.rs"
required-features = ["derive"]

[[example]]
name = "04_03_relations"
path = "examples/tutorial_builder/04_03_relations.rs"
required-features = ["cargo"]

[[example]]
name = "04_03_relations_derive"
path = "examples/tutorial_derive/04_03_relations.rs"
required-features = ["derive"]

[[example]]
name = "04_04_custom"
path = "examples/tutorial_builder/04_04_custom.rs"
required-features = ["cargo"]

[[example]]
name = "04_04_custom_derive"
path = "examples/tutorial_derive/04_04_custom.rs"
required-features = ["derive"]

[[example]]
name = "05_01_assert"
path = "examples/tutorial_builder/05_01_assert.rs"
test = true
required-features = ["cargo"]

[[example]]
name = "05_01_assert_derive"
path = "examples/tutorial_derive/05_01_assert.rs"
test = true
required-features = ["derive"]

[[example]]
name = "busybox"
path = "examples/multicall-busybox.rs"

[[example]]
name = "cargo-example"
path = "examples/cargo-example.rs"
required-features = [
    "cargo",
    "color",
]

[[example]]
name = "cargo-example-derive"
path = "examples/cargo-example-derive.rs"
required-features = [
    "derive",
    "color",
]

[[example]]
name = "demo"
path = "examples/demo.rs"
required-features = ["derive"]

[[example]]
name = "escaped-positional"
path = "examples/escaped-positional.rs"
required-features = ["cargo"]

[[example]]
name = "escaped-positional-derive"
path = "examples/escaped-positional-derive.rs"
required-features = ["derive"]

[[example]]
name = "find"
path = "examples/find.rs"
required-features = ["cargo"]

[[example]]
name = "git"
path = "examples/git.rs"

[[example]]
name = "git-derive"
path = "examples/git-derive.rs"
required-features = ["derive"]

[[example]]
name = "hostname"
path = "examples/multicall-hostname.rs"

[[example]]
name = "interop_augment_args"
path = "examples/derive_ref/augment_args.rs"
required-features = ["derive"]

[[example]]
name = "interop_augment_subcommands"
path = "examples/derive_ref/augment_subcommands.rs"
required-features = ["derive"]

[[example]]
name = "interop_flatten_hand_args"
path = "examples/derive_ref/flatten_hand_args.rs"
required-features = ["derive"]

[[example]]
name = "interop_hand_subcommand"
path = "examples/derive_ref/hand_subcommand.rs"
required-features = ["derive"]

[[example]]
name = "pacman"
path = "examples/pacman.rs"

[[example]]
name = "repl"
path = "examples/repl.rs"
required-features = ["help"]

[[example]]
name = "repl-derive"
path = "examples/repl-derive.rs"
required-features = ["derive"]

[[example]]
name = "typed-derive"
path = "examples/typed-derive.rs"
required-features = ["derive"]

[dependencies.clap_builder]
version = "=4.5.15"
default-features = false

[dependencies.clap_derive]
version = "=4.5.13"
optional = true

[dev-dependencies.automod]
version = "1.0.14"

[dev-dependencies.clap-cargo]
version = "0.14.1"
default-features = false

[dev-dependencies.humantime]
version = "2.1.0"

[dev-dependencies.rustversion]
version = "1.0.15"

[dev-dependencies.shlex]
version = "1.3.0"

[dev-dependencies.snapbox]
version = "0.6.16"

[dev-dependencies.trybuild]
version = "1.0.91"

[dev-dependencies.trycmd]
version = "0.15.3"
features = [
    "color-auto",
    "diff",
    "examples",
]
default-features = false

[features]
cargo = ["clap_builder/cargo"]
color = ["clap_builder/color"]
debug = [
    "clap_builder/debug",
    "clap_derive?/debug",
]
default = [
    "std",
    "color",
    "help",
    "usage",
    "error-context",
    "suggestions",
]
deprecated = [
    "clap_builder/deprecated",
    "clap_derive?/deprecated",
]
derive = ["dep:clap_derive"]
env = ["clap_builder/env"]
error-context = ["clap_builder/error-context"]
help = ["clap_builder/help"]
std = ["clap_builder/std"]
string = ["clap_builder/string"]
suggestions = ["clap_builder/suggestions"]
unicode = ["clap_builder/unicode"]
unstable-doc = [
    "clap_builder/unstable-doc",
    "derive",
]
unstable-ext = ["clap_builder/unstable-ext"]
unstable-styles = ["clap_builder/unstable-styles"]
unstable-v5 = [
    "clap_builder/unstable-v5",
    "clap_derive?/unstable-v5",
    "deprecated",
]
usage = ["clap_builder/usage"]
wrap_help = ["clap_builder/wrap_help"]

[lints.clippy]
assigning_clones = "allow"
blocks_in_conditions = "allow"
bool_assert_comparison = "allow"
branches_sharing_code = "allow"
checked_conversions = "warn"
collapsible_else_if = "allow"
create_dir = "warn"
dbg_macro = "warn"
debug_assert_with_mut_call = "warn"
doc_markdown = "warn"
empty_enum = "warn"
enum_glob_use = "warn"
expl_impl_clone_on_copy = "warn"
explicit_deref_methods = "warn"
explicit_into_iter_loop = "warn"
fallible_impl_from = "warn"
filter_map_next = "warn"
flat_map_option = "warn"
float_cmp_const = "warn"
fn_params_excessive_bools = "warn"
from_iter_instead_of_collect = "warn"
if_same_then_else = "allow"
implicit_clone = "warn"
imprecise_flops = "warn"
inconsistent_struct_constructor = "warn"
inefficient_to_string = "warn"
infinite_loop = "warn"
invalid_upcast_comparisons = "warn"
large_digit_groups = "warn"
large_stack_arrays = "warn"
large_types_passed_by_value = "warn"
let_and_return = "allow"
linkedlist = "warn"
lossy_float_literal = "warn"
macro_use_imports = "warn"
mem_forget = "warn"
multiple_bound_locations = "allow"
mutex_integer = "warn"
needless_continue = "warn"
needless_for_each = "warn"
negative_feature_names = "warn"
path_buf_push_overwrite = "warn"
ptr_as_ptr = "warn"
rc_mutex = "warn"
redundant_feature_names = "warn"
ref_option_ref = "warn"
rest_pat_in_fully_bound_structs = "warn"
same_functions_in_if_condition = "warn"
self_named_module_files = "warn"
semicolon_if_nothing_returned = "warn"
string_add_assign = "warn"
string_lit_as_bytes = "warn"
todo = "warn"
trait_duplication_in_bounds = "warn"
verbose_file_reads = "warn"
zero_sized_map_values = "warn"

[lints.rust]
unreachable_pub = "warn"
unsafe_op_in_unsafe_fn = "warn"
unused_lifetimes = "warn"
unused_macro_rules = "warn"
unused_qualifications = "warn"

[lints.rust.rust_2018_idioms]
level = "warn"
priority = -1
