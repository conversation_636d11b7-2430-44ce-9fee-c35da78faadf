<!-- omit in TOC -->
# clap_lex

> **Minimal, flexible command line parser**

[![Crates.io](https://img.shields.io/crates/v/clap_lex?style=flat-square)](https://crates.io/crates/clap_lex)
[![Crates.io](https://img.shields.io/crates/d/clap_lex?style=flat-square)](https://crates.io/crates/clap_lex)
[![License](https://img.shields.io/badge/license-Apache%202.0-blue?style=flat-square)](https://github.com/clap-rs/clap/blob/clap_lex-v0.7.2/LICENSE-APACHE)
[![License](https://img.shields.io/badge/license-MIT-blue?style=flat-square)](https://github.com/clap-rs/clap/blob/clap_lex-v0.7.2/LICENSE-MIT)

Dual-licensed under [Apache 2.0](LICENSE-APACHE) or [MIT](LICENSE-MIT).

1. [About](#about)
2. [API Reference](https://docs.rs/clap_lex)
3. [Questions & Discussions](https://github.com/clap-rs/clap/discussions)
4. [CONTRIBUTING](https://github.com/clap-rs/clap/blob/clap_lex-v0.7.2/clap_lex/CONTRIBUTING.md)
5. [Sponsors](https://github.com/clap-rs/clap/blob/clap_lex-v0.7.2/README.md#sponsors)

## About
