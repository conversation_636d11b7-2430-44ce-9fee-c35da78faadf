pre-release-commit-message = "Release {{crate_name}} {{version}}"
tag-message = "Release {{crate_name}} {{version}}"
tag-name = "{{crate_name}}-{{version}}"
sign-commit = true
sign-tag = true
publish = false

pre-release-replacements = [
  {file="README.md", search="spirv = .*", replace="{{crate_name}} = \"{{version}}\""},
  {file="../rspirv/Cargo.toml", search="spirv = \\{ version = \".*\", path = \"../spirv\" \\}", replace="{{crate_name}} = { version = \"{{version}}\", path = \"../spirv\" }" },
]
