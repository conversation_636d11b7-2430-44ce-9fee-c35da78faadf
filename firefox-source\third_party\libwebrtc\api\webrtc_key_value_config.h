/*
 *  Copyright 2019 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */
#ifndef API_WEBRTC_KEY_VALUE_CONFIG_H_
#define API_WEBRTC_KEY_VALUE_CONFIG_H_

// TODO(bugs.webrtc.org/10335): Remove once all migrated to
// api/field_trials_view.h
#include "api/field_trials_view.h"  // IWYU pragma: keep

#endif  // API_WEBRTC_KEY_VALUE_CONFIG_H_
