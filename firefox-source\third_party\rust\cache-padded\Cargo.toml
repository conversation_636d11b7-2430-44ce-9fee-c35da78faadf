# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "cache-padded"
version = "1.2.0"
authors = ["Stjepan Glavina <<EMAIL>>"]
description = "Prevent false sharing by padding and aligning to the length of a cache line"
homepage = "https://github.com/smol-rs/cache-padded"
keywords = ["cache", "padding", "lock-free", "atomic"]
categories = ["concurrency", "no-std"]
license = "Apache-2.0 OR MIT"
repository = "https://github.com/smol-rs/cache-padded"
