// |reftest| shell-option(--enable-temporal) skip-if(!this.hasOwnProperty('Temporal')||!xulRuntime.shell) -- Temporal is not enabled unconditionally, requires shell-options
// Copyright (C) 2022 Igalia, S.L. All rights reserved.
// This code is governed by the BSD license found in the LICENSE file.

/*---
esid: sec-temporal.instant.prototype.equals
description: Temporal.Instant string with sub-minute offset
features: [Temporal]
---*/

const instance = new Temporal.Instant(0n);

const str = "1970-01-01T00:19:32.37+00:19:32.37";
const result = instance.equals(str);
assert.sameValue(result, true, "if present, sub-minute offset is accepted exactly");

[
  "2021-08-19T17:30-07:00:01[-07:00:01]",
  "2021-08-19T17:30-07:00:00[-07:00:00]",
  "2021-08-19T17:30-07:00:00.1[-07:00:00.1]",
  "2021-08-19T17:30-07:00:00.0[-07:00:00.0]",
  "2021-08-19T17:30-07:00:00.01[-07:00:00.01]",
  "2021-08-19T17:30-07:00:00.00[-07:00:00.00]",
  "2021-08-19T17:30-07:00:00.001[-07:00:00.001]",
  "2021-08-19T17:30-07:00:00.000[-07:00:00.000]",
  "2021-08-19T17:30-07:00:00.0001[-07:00:00.0001]",
  "2021-08-19T17:30-07:00:00.0000[-07:00:00.0000]",
  "2021-08-19T17:30-07:00:00.00001[-07:00:00.00001]",
  "2021-08-19T17:30-07:00:00.00000[-07:00:00.00000]",
  "2021-08-19T17:30-07:00:00.000001[-07:00:00.000001]",
  "2021-08-19T17:30-07:00:00.000000[-07:00:00.000000]",
  "2021-08-19T17:30-07:00:00.0000001[-07:00:00.0000001]",
  "2021-08-19T17:30-07:00:00.0000000[-07:00:00.0000000]",
  "2021-08-19T17:30-07:00:00.00000001[-07:00:00.00000001]",
  "2021-08-19T17:30-07:00:00.00000000[-07:00:00.00000000]",
  "2021-08-19T17:30-07:00:00.000000001[-07:00:00.000000001]",
  "2021-08-19T17:30-07:00:00.000000000[-07:00:00.000000000]",

  "2021-08-19T17:30-07:00:01[-070001]",
  "2021-08-19T17:30-07:00:00[-070000]",
  "2021-08-19T17:30-07:00:00.1[-070000.1]",
  "2021-08-19T17:30-07:00:00.0[-070000.0]",
  "2021-08-19T17:30-07:00:00.01[-070000.01]",
  "2021-08-19T17:30-07:00:00.00[-070000.00]",
  "2021-08-19T17:30-07:00:00.001[-070000.001]",
  "2021-08-19T17:30-07:00:00.000[-070000.000]",
  "2021-08-19T17:30-07:00:00.0001[-070000.0001]",
  "2021-08-19T17:30-07:00:00.0000[-070000.0000]",
  "2021-08-19T17:30-07:00:00.00001[-070000.00001]",
  "2021-08-19T17:30-07:00:00.00000[-070000.00000]",
  "2021-08-19T17:30-07:00:00.000001[-070000.000001]",
  "2021-08-19T17:30-07:00:00.000000[-070000.000000]",
  "2021-08-19T17:30-07:00:00.0000001[-070000.0000001]",
  "2021-08-19T17:30-07:00:00.0000000[-070000.0000000]",
  "2021-08-19T17:30-07:00:00.00000001[-070000.00000001]",
  "2021-08-19T17:30-07:00:00.00000000[-070000.00000000]",
  "2021-08-19T17:30-07:00:00.000000001[-070000.000000001]",
  "2021-08-19T17:30-07:00:00.000000000[-070000.000000000]"
].forEach((str) => {
  assert.throws(
    RangeError,
    () => instance.equals(str),
    `ISO strings cannot have sub-minute offsets in time zone annotations: ${str}`
  );
});

reportCompare(0, 0);
