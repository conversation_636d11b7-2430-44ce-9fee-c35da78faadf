<!-- Any copyright is dedicated to the Public Domain.
- http://creativecommons.org/publicdomain/zero/1.0/ -->
<!DOCTYPE HTML>
<html>

<head>
  <meta charset="utf-8">
  <title>Test the IOUtils file I/O API</title>
  <script src="chrome://mochikit/content/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" href="chrome://mochikit/content/tests/SimpleTest/test.css" />
  <script src="file_ioutils_test_fixtures.js"></script>
  <script>
    "use strict";

    const { Assert } = ChromeUtils.importESModule(
      "resource://testing-common/Assert.sys.mjs"
    );

    add_task(async function test_computeHexDigest() {
      const tempDir = PathUtils.join(PathUtils.tempDir, "ioutils-test-compute-hex-digest.tmp.d");
      await createDir(tempDir);

      const path = PathUtils.join(tempDir, "file");
      await IOUtils.writeUTF8(path, "hello world\n");

      const DIGESTS = [
        "a948904f2f0f479b8f8197694b30184b0d2ed1c1cd2a1ec0fb85d299a192a447",
        "6b3b69ff0a404f28d75e98a066d3fc64fffd9940870cc68bece28545b9a75086b343d7a1366838083e4b8f3ca6fd3c80",
        "db3974a97f2407b7cae1ae637c0030687a11913274d578492558e39c16c017de84eacdc8c62fe34ee4e12b4b1428817f09b6a2760c3f8a664ceae94d2434a593",
      ];
      const ALGORITHMS = ["sha256", "sha384", "sha512"];

      for (let i = 0; i < ALGORITHMS.length; i++) {
        const alg = ALGORITHMS[i];
        const expected = DIGESTS[i];

        Assert.equal(
          await IOUtils.computeHexDigest(path, alg),
          expected,
          `IOUtils.hashFile() has expected value for ${alg}`);
      }

      await cleanup(tempDir);
    });
  </script>
</head>

<body>
  <p id="display"></p>
  <div id="content" style="display: none"></div>
  <pre id="test"></pre>
</body>

</html>
