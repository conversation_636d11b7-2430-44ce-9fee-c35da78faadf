# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2015"
name = "bit-set"
version = "0.8.0"
authors = ["<PERSON> <<EMAIL>>"]
build = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "A set of bits"
homepage = "https://github.com/contain-rs/bit-set"
documentation = "https://docs.rs/bit-set/"
readme = "README.md"
keywords = [
    "data-structures",
    "bitset",
]
license = "Apache-2.0 OR MIT"
repository = "https://github.com/contain-rs/bit-set"

[lib]
name = "bit_set"
path = "src/lib.rs"

[[bench]]
name = "bench"
path = "benches/bench.rs"

[dependencies.bit-vec]
version = "0.8.0"
default-features = false

[dependencies.serde]
version = "1.0"
features = ["derive"]
optional = true

[dev-dependencies.rand]
version = "0.8"

[dev-dependencies.serde_json]
version = "1.0"

[features]
default = ["std"]
serde = [
    "dep:serde",
    "bit-vec/serde",
]
std = ["bit-vec/std"]
