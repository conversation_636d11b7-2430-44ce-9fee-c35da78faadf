# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

Classes = [
    {
        'name': 'Directory',
        'js_name': 'dirsvc',
        'cid': '{f00152d0-b40b-11d3-8c9c-000064657374}',
        'contract_ids': ['@mozilla.org/file/directory_service;1'],
        'interfaces': ['nsIDirectoryService', 'nsIProperties'],
        'legacy_constructor': 'nsDirectoryService::Create',
        'headers': ['nsDirectoryService.h'],
    },
    {
        'cid': '{565e3a2c-1dd2-11b2-8da1-b4cef17e568d}',
        'contract_ids': ['@mozilla.org/io/multiplex-input-stream;1'],
        'legacy_constructor': 'nsMultiplexInputStreamConstructor',
        'headers': ['nsMultiplexInputStream.h'],
    },
    {
        'cid': '{e4a0ee4e-0775-457b-9118-b3ae97a7c758}',
        'contract_ids': ['@mozilla.org/pipe;1'],
        'legacy_constructor': 'nsPipeConstructor',
        'headers': ['/xpcom/io/nsPipe.h'],
    },
    {
        'cid': '{7225c040-a9bf-11d3-a197-0050041caf44}',
        'contract_ids': ['@mozilla.org/scriptableinputstream;1'],
        'legacy_constructor': 'nsScriptableInputStream::Create',
        'headers': ['nsScriptableInputStream.h'],
    },
    {
        'cid': '{0abb0835-5000-4790-af28-61b3ba17c295}',
        'contract_ids': ['@mozilla.org/io/string-input-stream;1'],
        'legacy_constructor': 'nsStringInputStreamConstructor',
        'headers': ['/xpcom/build/XPCOMModule.h'],
        'processes': ProcessSelector.ALLOW_IN_SOCKET_PROCESS,
    },
]
