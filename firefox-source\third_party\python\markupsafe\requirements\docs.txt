#
# This file is autogenerated by pip-compile with Python 3.13
# by the following command:
#
#    pip-compile docs.in
#
alabaster==1.0.0
    # via sphinx
babel==2.16.0
    # via sphinx
certifi==2024.8.30
    # via requests
charset-normalizer==3.4.0
    # via requests
docutils==0.21.2
    # via sphinx
idna==3.10
    # via requests
imagesize==1.4.1
    # via sphinx
jinja2==3.1.4
    # via sphinx
markupsafe==3.0.1
    # via jinja2
packaging==24.1
    # via
    #   pallets-sphinx-themes
    #   sphinx
pallets-sphinx-themes==2.2.0
    # via -r docs.in
pygments==2.18.0
    # via sphinx
requests==2.32.3
    # via sphinx
snowballstemmer==2.2.0
    # via sphinx
sphinx==8.1.3
    # via
    #   -r docs.in
    #   pallets-sphinx-themes
    #   sphinx-notfound-page
    #   sphinxcontrib-log-cabinet
sphinx-notfound-page==1.0.4
    # via pallets-sphinx-themes
sphinxcontrib-applehelp==2.0.0
    # via sphinx
sphinxcontrib-devhelp==2.0.0
    # via sphinx
sphinxcontrib-htmlhelp==2.1.0
    # via sphinx
sphinxcontrib-jsmath==1.0.1
    # via sphinx
sphinxcontrib-log-cabinet==1.0.1
    # via -r docs.in
sphinxcontrib-qthelp==2.0.0
    # via sphinx
sphinxcontrib-serializinghtml==2.0.0
    # via sphinx
urllib3==2.2.3
    # via requests
