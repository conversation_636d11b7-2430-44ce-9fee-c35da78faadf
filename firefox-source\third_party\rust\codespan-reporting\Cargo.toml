# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.67"
name = "codespan-reporting"
version = "0.12.0"
authors = ["<PERSON> <<EMAIL>>"]
build = false
exclude = ["assets/**"]
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "Beautiful diagnostic reporting for text-based programming languages"
homepage = "https://github.com/brendanzab/codespan"
documentation = "https://docs.rs/codespan-reporting"
readme = "README.md"
license = "Apache-2.0"
repository = "https://github.com/brendanzab/codespan"

[features]
ascii-only = []
default = [
    "std",
    "termcolor",
]
serialization = ["serde"]
std = ["serde?/std"]
termcolor = [
    "std",
    "dep:termcolor",
]

[lib]
name = "codespan_reporting"
path = "src/lib.rs"

[[example]]
name = "custom_files"
path = "examples/custom_files.rs"

[[example]]
name = "peg_calculator"
path = "examples/peg_calculator.rs"

[[example]]
name = "readme_preview"
path = "examples/readme_preview.rs"

[[example]]
name = "reusable_diagnostic"
path = "examples/reusable_diagnostic.rs"

[[example]]
name = "term"
path = "examples/term.rs"

[[test]]
name = "term"
path = "tests/term.rs"

[dependencies.serde]
version = "1"
features = [
    "derive",
    "alloc",
]
optional = true
default-features = false

[dependencies.termcolor]
version = "1.0.4"
optional = true

[dependencies.unicode-width]
version = ">=0.1,<0.3"

[dev-dependencies.anyhow]
version = "1"

[dev-dependencies.insta]
version = "1.6.3"

[dev-dependencies.lazy_static]
version = "1.4"

[dev-dependencies.peg]
version = "0.7"

[dev-dependencies.pico-args]
version = "0.5.0"

[dev-dependencies.rustyline]
version = "6"

[dev-dependencies.unindent]
version = "0.1"

[lints.clippy]
alloc_instead_of_core = "warn"
std_instead_of_alloc = "warn"
std_instead_of_core = "warn"
