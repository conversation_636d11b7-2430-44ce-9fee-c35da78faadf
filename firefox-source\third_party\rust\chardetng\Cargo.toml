# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "chardetng"
version = "0.1.9"
authors = ["<PERSON> <<EMAIL>>"]
build = false
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "A character encoding detector for legacy Web content"
homepage = "https://docs.rs/chardetng/"
documentation = "https://docs.rs/chardetng/"
readme = "README.md"
keywords = [
    "encoding",
    "web",
    "unicode",
    "charset",
]
categories = [
    "text-processing",
    "encoding",
    "web-programming",
    "internationalization",
]
license = "Apache-2.0 OR MIT"
repository = "https://github.com/hsivonen/chardetng"

[features]
testing-only-no-semver-guarantees-do-not-use = []

[lib]
name = "chardetng"
path = "src/lib.rs"

[dependencies]
encoding_rs = "0.8.17"
memchr = "2.2.0"

[dev-dependencies]
detone = "1.0.0"
