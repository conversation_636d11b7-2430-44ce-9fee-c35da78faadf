<!DOCTYPE html>
<html class="reftest-wait">
<title>
Removing CSS animation in delay phase destroys a stacking context
</title>
<style>
span {
  height: 100px;
  width: 100px;
  position: fixed;
  background: green;
  top: 50px;
}
@keyframes Opacity0 {
  from, to { opacity: 0 }
}
#test {
  width: 100px; height: 100px;
  background: blue;
  opacity: 1 ! important;
}
</style>
<span></span>
<div id="test"></div>
<script>
window.addEventListener("load", () => {
  var target = document.getElementById("test");
  target.style.animation = "Opacity0 100s 100s";

  // We need to wait for <PERSON>zAfterPaint instead of requestAnimationFrame to
  // ensure the stacking context has been updated on the compositor.
  window.addEventListener("MozAfterPaint", function() {
    // Here we have CSS animation on 100% opacity style element, so
    // there should be a stacking context.

    target.style.animation = "";

    // This time we don't need to wait for MozAfterPaint because reftest tool
    // will be received MozAferPaint event.
    requestAnimationFrame(() => {
      // Now we have only 100% opacity style, so we should not create any
      // stacking context.
      document.documentElement.classList.remove("reftest-wait");
    });
  }, {once: true});
});
</script>
