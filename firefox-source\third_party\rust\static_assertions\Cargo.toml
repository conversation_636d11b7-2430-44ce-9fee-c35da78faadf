# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies
#
# If you believe there's an error in this file please file an
# issue against the rust-lang/cargo repository. If you're
# editing this file be aware that the upstream Cargo.toml
# will likely look very different (and much more reasonable)

[package]
name = "static_assertions"
version = "1.1.0"
authors = ["<PERSON>"]
include = ["Cargo.toml", "src/**/*.rs", "README.md", "CHANGELOG.md", "LICENSE*"]
description = "Compile-time assertions to ensure that invariants are met."
homepage = "https://github.com/nvzqz/static-assertions-rs"
documentation = "https://docs.rs/static_assertions/"
readme = "README.md"
keywords = ["assert", "static", "testing"]
categories = ["no-std", "rust-patterns", "development-tools::testing"]
license = "MIT OR Apache-2.0"
repository = "https://github.com/nvzqz/static-assertions-rs"

[features]
nightly = []
[badges.is-it-maintained-issue-resolution]
repository = "nvzqz/static-assertions-rs"

[badges.is-it-maintained-open-issues]
repository = "nvzqz/static-assertions-rs"

[badges.maintenance]
status = "passively-maintained"

[badges.travis-ci]
repository = "nvzqz/static-assertions-rs"
