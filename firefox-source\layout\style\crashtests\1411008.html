<!doctype html>
<script>
  try { o1 = document.createElement('c') } catch(e) { }
  try { o2 = document.createElement('p') } catch(e) { }
  try { o3 = document.createElement('progress') } catch(e) { }
  try { o4 = document.createElement('legend') } catch(e) { }
  try { o5 = document.createElement('m') } catch(e) { }
  try { o6 = new Range() } catch(e) { }
  try { o7 = window.getSelection() } catch(e) { }
  try { document.documentElement.appendChild(o1) } catch(e) { }
  try { document.documentElement.appendChild(o2) } catch(e) { }
  try { document.documentElement.appendChild(o3) } catch(e) { }
  try { o1.scrollTopMax } catch(e) { }
  try { o2.outerHTML = '<b contenteditable="">' } catch(e) { }
  try { o7.modify('move', 'right', 'line') } catch(e) { }
  try { o1.prepend(o4) } catch(e) { }
  try { document.documentElement.appendChild(o5) } catch(e) { }
  try { o5.scrollLeft = 8 } catch(e) { }
  try { document.designMode = 'on'; } catch(e) { }
  try { o7.addRange(o6); } catch(e) { }
  try { document.execCommand('inserttext') } catch(e) { }
</script>
