# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

with Files("**"):
    BUG_COMPONENT = ("Core", "XPCOM")

with Files("nsCycleCollect*"):
    BUG_COMPONENT = ("Core", "Cycle Collector")

DIRS += [
    "idl-parser/xpidl",
    "geckoprocesstypes_generator/geckoprocesstypes",
]

DIRS += [
    "string",
    "glue",
    "base",
    "ds",
    "io",
    "components",
    "threads",
    "reflect",
    "system",
    "../chrome",
    "build",
    "ioutils",
]

if CONFIG["OS_ARCH"] == "WINNT" and CONFIG["MOZ_DEBUG"]:
    DIRS += ["windbgdlg"]

TEST_DIRS += [
    "rust/gtest",
    "tests",
]

#  Can't build internal xptcall tests that use symbols which are not exported.
# TEST_DIRS += [
#    'reflect/xptcall/tests,
# ]

SPHINX_TREES["/xpcom"] = "docs"

with Files("docs/**"):
    SCHEDULES.exclusive = ["docs"]
