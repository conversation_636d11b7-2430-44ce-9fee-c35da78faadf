/* -*- Mode: C++; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 4 -*- */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */


/*
 * nsITellableStream
 *
 * This class is separate from nsISeekableStream in order to let streams to
 * implement ::Tell() without implementing the whole nsISeekableStream
 * interface. Callers can QI the stream to know what is implemented. This is
 * mainly done for nsPipeInputStream.
 *
 *
 * Implementing this interface, streams are able to expose the current offset
 * via ::tell().
 */

#include "nsISupports.idl"

[scriptable, uuid(ee942946-4538-45d2-bf05-ffdbf5932621)]
interface nsITellableStream : nsISupports
{
    /**
     *  tell
     *
     *  This method reports the current offset, in bytes, from the start of the
     *  stream.
     *
     *   @throws NS_BASE_STREAM_CLOSED if called on a closed stream.
     */
    long long tell();
};
