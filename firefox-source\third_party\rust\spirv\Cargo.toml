# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "spirv"
version = "0.3.0+sdk-1.3.268.0"
authors = ["<PERSON><PERSON> <<EMAIL>>"]
description = "Rust definition of SPIR-V structs and enums"
documentation = "https://docs.rs/spirv"
readme = "README.md"
keywords = [
    "spirv",
    "definition",
    "struct",
    "enum",
]
license = "Apache-2.0"
repository = "https://github.com/gfx-rs/rspirv"

[lib]
path = "lib.rs"

[dependencies.bitflags]
version = "2.0"

[dependencies.serde]
version = "1"
features = ["derive"]
optional = true

[features]
deserialize = [
    "serde",
    "bitflags/serde",
]
serialize = [
    "serde",
    "bitflags/serde",
]
