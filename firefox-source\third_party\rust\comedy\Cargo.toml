# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies
#
# If you believe there's an error in this file please file an
# issue against the rust-lang/cargo repository. If you're
# editing this file be aware that the upstream Cargo.toml
# will likely look very different (and much more reasonable)

[package]
name = "comedy"
version = "0.2.0"
authors = ["<PERSON> <<EMAIL>>"]
description = "Windows error handling, COM, and handles"
keywords = ["windows", "com", "win32"]
categories = ["api-bindings", "os::windows-apis"]
license = "MIT/Apache-2.0"
repository = "https://github.com/agashlin/comedy-rs"
[package.metadata.docs.rs]
default-target = "x86_64-pc-windows-msvc"
[dependencies.winapi]
version = "0.3.6"
features = ["basetsd", "combaseapi", "errhandlingapi", "handleapi", "impl-default", "minwindef", "objbase", "unknwnbase", "winbase", "winerror", "wtypes", "wtypesbase"]
[dev-dependencies.winapi]
version = "0.3.6"
features = ["bits", "fileapi", "guiddef"]
