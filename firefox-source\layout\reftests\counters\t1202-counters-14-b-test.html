<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
 <head>
  <title>CSS 2.1 Test Suite: content: counters(c, ".", upper-alpha)</title>
  <link rel="help" href="http://www.w3.org/TR/CSS21/generate.html#propdef-content"/>
  <link rel="help" href="http://www.w3.org/TR/CSS21/syndata.html#counter"/>
  <link rel="help" href="http://www.w3.org/TR/CSS21/generate.html#counter-styles"/>
  <style type="text/css">

  body, #test { counter-reset: c; }
  p, #test span { counter-increment: c; }
  #test span:before { content: counters(c, ".", upper-alpha); }

  </style>
 </head>
 <body>

 <p></p>

 <div id="test">
   <span></span>
   <span></span>
   <span></span>
   <span></span>
   <span></span>
   <span></span>
   <span></span>
   <span></span>
   <span></span>
   <span></span>
   <span></span>
   <span></span>
 </div>


 </body>
</html>
