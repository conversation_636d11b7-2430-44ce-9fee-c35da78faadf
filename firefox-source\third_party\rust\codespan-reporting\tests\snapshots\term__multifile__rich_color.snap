---
source: codespan-reporting/tests/term.rs
expression: TEST_DATA.emit_color(&config)
---
{fg:Red bold bright}error{bold bright}: unknown builtin: `NATRAL`{/}
  {fg:Blue}┌─{/} Data/Nat.fun:7:13
  {fg:Blue}│{/}
{fg:Blue}7{/} {fg:Blue}│{/} {-# BUILTIN {fg:Red}NATRAL{/} Nat #-}
  {fg:Blue}│{/}             {fg:Red}^^^^^^{/} {fg:Red}unknown builtin{/}
  {fg:Blue}│{/}
  {fg:Blue}={/} there is a builtin with a similar name: `NATURAL`

{fg:Yellow bold bright}warning{bold bright}: unused parameter pattern: `n₂`{/}
   {fg:Blue}┌─{/} Data/Nat.fun:17:16
   {fg:Blue}│{/}
{fg:Blue}17{/} {fg:Blue}│{/} zero    - succ {fg:Yellow}n₂{/} = zero
   {fg:Blue}│{/}                {fg:Yellow}^^{/} {fg:Yellow}unused parameter{/}
   {fg:Blue}│{/}
   {fg:Blue}={/} consider using a wildcard pattern: `_`

{fg:Red bold bright}error[E0001]{bold bright}: unexpected type in application of `_+_`{/}
   {fg:Blue}┌─{/} Test.fun:4:11
   {fg:Blue}│{/}
{fg:Blue} 4{/} {fg:Blue}│{/} _ = 123 + {fg:Red}"hello"{/}
   {fg:Blue}│{/}           {fg:Red}^^^^^^^{/} {fg:Red}expected `Nat`, found `String`{/}
   {fg:Blue}│{/}
   {fg:Blue}┌─{/} Data/Nat.fun:11:1
   {fg:Blue}│{/}
{fg:Blue}11{/} {fg:Blue}│{/} _+_ : Nat → Nat → Nat
   {fg:Blue}│{/} {fg:Blue}---------------------{/} {fg:Blue}based on the definition of `_+_`{/}
   {fg:Blue}│{/}
   {fg:Blue}={/} expected type `Nat`
        found type `String`


