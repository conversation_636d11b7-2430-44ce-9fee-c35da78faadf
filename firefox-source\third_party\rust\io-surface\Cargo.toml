# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies
#
# If you believe there's an error in this file please file an
# issue against the rust-lang/cargo repository. If you're
# editing this file be aware that the upstream Cargo.toml
# will likely look very different (and much more reasonable)

[package]
name = "io-surface"
version = "0.15.1"
authors = ["The Servo Project Developers"]
description = "Bindings to IO Surface for macOS"
homepage = "https://github.com/servo/core-foundation-rs"
license = "MIT / Apache-2.0"
repository = "https://github.com/servo/core-foundation-rs"
[package.metadata.docs.rs]
default-target = "x86_64-apple-darwin"
[dependencies.cgl]
version = "0.3"

[dependencies.core-foundation]
version = "0.9"

[dependencies.leaky-cow]
version = "0.1.1"

[dependencies.libc]
version = "0.2"
