# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies
#
# If you believe there's an error in this file please file an
# issue against the rust-lang/cargo repository. If you're
# editing this file be aware that the upstream Cargo.toml
# will likely look very different (and much more reasonable)

[package]
name = "cgl"
version = "0.3.2"
authors = ["The Servo Project Developers"]
description = "Rust bindings for CGL on Mac"
license = "MIT / Apache-2.0"
repository = "https://github.com/servo/cgl-rs"
[dependencies.libc]
version = "0.2"
