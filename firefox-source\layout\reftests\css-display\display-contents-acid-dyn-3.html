<!DOCTYPE html>
<!--
     Any copyright is dedicated to the Public Domain.
     http://creativecommons.org/publicdomain/zero/1.0/
-->
<html lang="en-US">
<head>
  <meta charset="UTF-8">
  <title>CSS Test: CSS display:contents</title>
  <link rel="author" title="<PERSON><PERSON>" href="https://bugzilla.mozilla.org/show_bug.cgi?id=907396">
  <link rel="help" href="http://dev.w3.org/csswg/css-display">
<style type="text/css">

        html,body {
            color:black; background-color:white; height:100%; font-size:16px; padding:0; margin:0;
        }

iframe { padding:0;margin:0;border-width:0;width:100%;height:90%; color:red; }

    </style>
<script>
function runTest(iframe) {
  var win = iframe.contentWindow;
  var doc = iframe.contentDocument;
  doc.body.offsetHeight
  var e = doc.querySelectorAll('*');
  var contents = new Array;
  for (i=0; i < e.length; ++i) {
    var elm = e[i];
    if (win.getComputedStyle(elm).display == 'contents') {
      contents.push([ elm, elm.nextSibling, elm.parentNode ]);
      elm.remove();
    }
  }
  doc.body.offsetHeight;
  var i = contents.length;
  while(i--) {
    var arr = contents[i];
    arr[2].insertBefore(arr[0], arr[1]);
  }
  doc.body.offsetHeight;
}
</script>
</head>
<body>

<iframe src="display-contents-acid.html" frameborder=0 onload="runTest(this)"></iframe>

</body>
</html>
