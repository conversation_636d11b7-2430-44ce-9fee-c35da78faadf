<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html>
<head>
   <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
   <meta name="Copyright" content="Copyright (C) 2016 and later: Unicode, Inc. and others. License & terms of use: http://www.unicode.org/copyright.html">
   <!-- meta name="Copyright" content="Copyright (c) 2002, International Business Machines Corporation and others. All Rights Reserved." -->
   <meta name="Author" content="<PERSON>">
   <meta name="GENERATOR" content="Mozilla/4.72 [en] (Windows NT 5.0; U) [Netscape]">
   <title>ScriptRun readme</title>
</head>
<body>

<h2>
What is scrptrun and srtest?</h2>
The ICU LayoutEngine must be called with text in a single script. scrptrun.h
and scrptrun.cpp implement the ScriptRun class, which can be used to find
runs of text that is in a single script. It uses a basic iteration interface.
<p>srtest is a little program that tests ScriptRun. You can use it as an
example of how to use ScriptRun. Here's what the output should look like:
<blockquote><font face="Courier New,Courier"><font size=-1>Script 'DEVANAGARI'
from 0 to 9.</font></font>
<br><font face="Courier New,Courier"><font size=-1>Script 'ARABIC' from
9 to 17.</font></font>
<br><font face="Courier New,Courier"><font size=-1>Script 'CYRILLIC' from
17 to 25.</font></font>
<br><font face="Courier New,Courier"><font size=-1>Script 'LATIN' from
25 to 33.</font></font>
<br><font face="Courier New,Courier"><font size=-1>Script 'HAN' from 33
to 35.</font></font>
<br><font face="Courier New,Courier"><font size=-1>Script 'HIRAGANA' from
35 to 41.</font></font>
<br><font face="Courier New,Courier"><font size=-1>Script 'KATAKANA' from
41 to 45.</font></font>
<br><font face="Courier New,Courier"><font size=-1>Script 'DESERET' from
45 to 53.</font></font></blockquote>

<h2>
How do I build scrptrun and srtest?</h2>
To use the ScriptRun class in a Windows application, just include scrptrun.h
and scrptrun.cpp right out of the &lt;icu>\source\extra\scrptrun directory
into your project. You'll also need to add the &lt;icu>\source]extra\scrptrun
directory to the "Additional include directories" section of the "Preprocessor"
category on the "C/C++" tab in the project settings.
<p>On UNIX systems the simplest thing to do is to just copy scrptrun.h
and scrptrun.cpp into your source directory. If you want to use them from
&lt;icu>/source/extra/scrpturn, it's a bit trickier: the default dependency
rules don't work on source files in a different directory. You need to
add separate dependency rules for scrptrun.o and scrptrun.d. See &lt;icu>/source/samples/layout/Makefile.in
for an example of how to do this. You'll also have to add -I$(top_srcdir)/extra/scrptrun
to your compiler flags so that the compiler can find scrptrun.h. If your
application has to build on multiple UNIX platforms, it might be difficult
to wirte dependency rules that will work correctly on all platforms. In
that case, you're probably better off copying the scrpturn files to your
source directory.
<p>Building srtest is easy, on Windows build the srtest workspace in &lt;icu>\source\extra\scrptrun.
On UNIX, connect to &lt;top-build-dir>/extra/scrptrun and do "make all"
<h2>
Notes</h2>

<ul>
<li>
ScriptRun is based on <a href="http://www.unicode.org/unicode/reports/tr24/">Unicode
Technical Report #24</a> but the implementation is not complete. In particular
the support for paired punctuation is only a prototype. A complete implementation
will be added to ICU in the future.</li>
</ul>

</body>
</html>
