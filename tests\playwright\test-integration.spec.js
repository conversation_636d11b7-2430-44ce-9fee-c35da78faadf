/**
 * Megisto Browser - Integration Tests
 * End-to-end integration tests for Megisto Browser features
 */

const { test, expect } = require('@playwright/test');
const path = require('path');
const {
  createCookieBannerPage,
  createYouTubeEmbedPage,
  createPopupTestPage,
  waitForElementRemoval,
  waitForVideoJSPlayer,
  isElementVisible,
  validateMegistoExtension,
  takeTimestampedScreenshot
} = require('../utils/test-helpers');

// Test configuration
const MEGISTO_BROWSER_PATH = process.env.MEGISTO_BROWSER_PATH || 
  path.join(__dirname, '../../firefox-source/obj-megisto/dist/bin/megisto.exe');

test.describe('Megisto Browser Integration Tests', () => {
  let browser;
  let context;

  test.beforeAll(async ({ playwright }) => {
    browser = await playwright.firefox.launch({
      executablePath: MEGISTO_BROWSER_PATH,
      headless: false,
      args: [
        '--no-first-run',
        '--disable-default-browser-check',
        '--new-instance',
        '--no-remote'
      ]
    });

    context = await browser.newContext({
      viewport: { width: 1280, height: 720 }
    });
  });

  test.afterAll(async () => {
    await context?.close();
    await browser?.close();
  });

  test('should load Megisto extension properly', async () => {
    const page = await context.newPage();
    
    try {
      await page.goto('about:blank');
      await page.waitForTimeout(2000); // Allow extension to load
      
      // Check if Megisto extension is active
      const extensionLoaded = await validateMegistoExtension(page);
      expect(extensionLoaded).toBeTruthy();
      
      console.log('✓ Megisto extension loaded successfully');
      
    } finally {
      await page.close();
    }
  });

  test('should block cookie banners and enhance YouTube simultaneously', async () => {
    const page = await context.newPage();
    
    try {
      // Create a page with both cookie banner and YouTube embed
      const cookieBanner = createCookieBannerPage('modal');
      const youtubeEmbed = createYouTubeEmbedPage('basic');
      
      const combinedPage = `
        <!DOCTYPE html>
        <html>
          <head>
            <title>Combined Test Page</title>
            <meta charset="utf-8">
          </head>
          <body>
            <h1>Combined Cookie Banner and YouTube Test</h1>
            
            <!-- Cookie Banner -->
            <div class="cookie-modal-overlay" style="position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:9999;">
              <div class="cookie-modal" style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:white;padding:20px;border-radius:8px;">
                <h2>Cookie Consent</h2>
                <p>This website uses cookies.</p>
                <button class="accept-all">Accept All</button>
                <button class="reject-all">Reject All</button>
              </div>
            </div>
            
            <!-- YouTube Embed -->
            <div style="margin: 20px 0;">
              <iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>
            </div>
            
            <p>Main page content should be accessible after cookie banner is blocked.</p>
          </body>
        </html>
      `;
      
      await page.goto(`data:text/html,${encodeURIComponent(combinedPage)}`);
      await page.waitForLoadState('networkidle');
      
      // Wait for processing
      await page.waitForTimeout(3000);
      
      // Check cookie banner is blocked
      const bannerVisible = await isElementVisible(page, '.cookie-modal-overlay');
      expect(bannerVisible).toBeFalsy();
      
      // Check YouTube enhancement is working
      const videojsLoaded = await waitForVideoJSPlayer(page);
      const originalIframe = await page.locator('iframe[src*="youtube.com/embed"]').count();
      const megistoPlayer = await page.locator('.megisto-youtube-player').count();
      
      // Either Video.js should be loaded or player should be replaced
      expect(videojsLoaded || megistoPlayer > 0).toBeTruthy();
      
      console.log('✓ Cookie banner blocked and YouTube enhanced simultaneously');
      
    } finally {
      await page.close();
    }
  });

  test('should handle real-world website scenarios', async () => {
    const page = await context.newPage();
    
    try {
      // Test with a real website (using a simple one for reliability)
      await page.goto('https://example.com');
      await page.waitForLoadState('networkidle');
      
      // Inject a cookie banner for testing
      await page.addScriptTag({
        content: `
          const banner = document.createElement('div');
          banner.id = 'test-cookie-banner';
          banner.style.cssText = 'position:fixed;top:0;width:100%;background:red;color:white;padding:10px;z-index:9999;';
          banner.innerHTML = 'Test Cookie Banner <button id="accept">Accept</button>';
          document.body.appendChild(banner);
        `
      });
      
      // Wait for Megisto to process
      await page.waitForTimeout(2000);
      
      // Check if banner was removed
      const bannerExists = await page.locator('#test-cookie-banner').count();
      expect(bannerExists).toBe(0);
      
      console.log('✓ Real-world scenario test passed');
      
    } finally {
      await page.close();
    }
  });

  test('should maintain browser functionality', async () => {
    const page = await context.newPage();
    
    try {
      // Test basic browser functionality
      await page.goto('data:text/html,<html><body><h1 id="title">Test</h1><button onclick="document.getElementById(\'title\').textContent=\'Clicked\'">Click</button></body></html>');
      
      // Test JavaScript execution
      await page.click('button');
      const titleText = await page.textContent('#title');
      expect(titleText).toBe('Clicked');
      
      // Test navigation
      await page.goto('about:blank');
      expect(page.url()).toBe('about:blank');
      
      // Test back/forward
      await page.goBack();
      await page.goForward();
      
      console.log('✓ Basic browser functionality maintained');
      
    } finally {
      await page.close();
    }
  });

  test('should handle multiple tabs correctly', async () => {
    const page1 = await context.newPage();
    const page2 = await context.newPage();
    
    try {
      // Load different content in each tab
      const cookiePage = createCookieBannerPage('basic');
      await page1.goto(`data:text/html,${encodeURIComponent(cookiePage.html)}`);
      
      const youtubePage = createYouTubeEmbedPage('basic');
      await page2.goto(`data:text/html,${encodeURIComponent(youtubePage)}`);
      
      await Promise.all([
        page1.waitForLoadState('networkidle'),
        page2.waitForLoadState('networkidle')
      ]);
      
      // Wait for processing
      await page1.waitForTimeout(2000);
      await page2.waitForTimeout(2000);
      
      // Check both tabs are processed correctly
      const banner1Visible = await isElementVisible(page1, cookiePage.selector);
      const videojs2Loaded = await waitForVideoJSPlayer(page2);
      
      expect(banner1Visible).toBeFalsy();
      expect(videojs2Loaded || await page2.locator('.megisto-youtube-player').count() > 0).toBeTruthy();
      
      console.log('✓ Multiple tabs handled correctly');
      
    } finally {
      await page1.close();
      await page2.close();
    }
  });

  test('should handle dynamic content loading', async () => {
    const page = await context.newPage();
    
    try {
      // Create page with dynamic content loading
      const dynamicPage = `
        <!DOCTYPE html>
        <html>
          <body>
            <h1>Dynamic Content Test</h1>
            <button id="load-banner">Load Cookie Banner</button>
            <button id="load-video">Load YouTube Video</button>
            <div id="content"></div>
            
            <script>
              document.getElementById('load-banner').onclick = function() {
                const banner = document.createElement('div');
                banner.className = 'dynamic-cookie-banner';
                banner.style.cssText = 'position:fixed;top:0;width:100%;background:orange;padding:10px;z-index:9999;';
                banner.innerHTML = 'Dynamic Cookie Banner <button>Accept</button>';
                document.getElementById('content').appendChild(banner);
              };
              
              document.getElementById('load-video').onclick = function() {
                const iframe = document.createElement('iframe');
                iframe.width = '560';
                iframe.height = '315';
                iframe.src = 'https://www.youtube.com/embed/dQw4w9WgXcQ';
                iframe.frameBorder = '0';
                iframe.allowFullscreen = true;
                document.getElementById('content').appendChild(iframe);
              };
            </script>
          </body>
        </html>
      `;
      
      await page.goto(`data:text/html,${encodeURIComponent(dynamicPage)}`);
      await page.waitForLoadState('networkidle');
      
      // Load dynamic cookie banner
      await page.click('#load-banner');
      await page.waitForTimeout(2000);
      
      // Check if dynamic banner is blocked
      const dynamicBannerVisible = await isElementVisible(page, '.dynamic-cookie-banner');
      expect(dynamicBannerVisible).toBeFalsy();
      
      // Load dynamic YouTube video
      await page.click('#load-video');
      await page.waitForTimeout(3000);
      
      // Check if dynamic video is enhanced
      const dynamicVideoProcessed = await waitForVideoJSPlayer(page) || 
                                   await page.locator('.megisto-youtube-player').count() > 0;
      expect(dynamicVideoProcessed).toBeTruthy();
      
      console.log('✓ Dynamic content handled correctly');
      
    } finally {
      await page.close();
    }
  });

  test('should preserve user preferences across sessions', async () => {
    const page = await context.newPage();
    
    try {
      // This test would require implementing preference persistence
      // For now, just verify that preferences can be accessed
      
      await page.goto('about:blank');
      
      // Check if preferences are accessible
      const preferencesAccessible = await page.evaluate(() => {
        // This would check if Megisto preferences are available
        return typeof browser !== 'undefined' && 
               typeof browser.runtime !== 'undefined';
      }).catch(() => false);
      
      // This is a basic check - in a real implementation,
      // we would test actual preference persistence
      console.log('✓ Preference system accessible');
      
    } finally {
      await page.close();
    }
  });

  test('should handle error scenarios gracefully', async () => {
    const page = await context.newPage();
    
    try {
      // Test with malformed content
      const malformedPage = `
        <!DOCTYPE html>
        <html>
          <body>
            <h1>Error Scenario Test</h1>
            <!-- Malformed iframe -->
            <iframe src="invalid-url" width="560" height="315"></iframe>
            
            <!-- Invalid cookie banner -->
            <div class="cookie-banner" style="display:none;">
              <script>throw new Error('Test error');</script>
            </div>
            
            <p>Page should still function despite errors.</p>
          </body>
        </html>
      `;
      
      await page.goto(`data:text/html,${encodeURIComponent(malformedPage)}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Page should still be functional
      const title = await page.textContent('h1');
      expect(title).toBe('Error Scenario Test');
      
      console.log('✓ Error scenarios handled gracefully');
      
    } finally {
      await page.close();
    }
  });
});

// Performance tests
test.describe('Megisto Browser Performance', () => {
  test('should not significantly impact page load performance', async ({ browser }) => {
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
      const startTime = Date.now();
      
      const testPage = createYouTubeEmbedPage('multiple');
      await page.goto(`data:text/html,${encodeURIComponent(testPage)}`);
      await page.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      
      // Load time should be reasonable (less than 15 seconds for complex page)
      expect(loadTime).toBeLessThan(15000);
      
      console.log(`✓ Page load time: ${loadTime}ms`);
      
    } finally {
      await page.close();
      await context.close();
    }
  });
});
