<!DOCTYPE html>
<title>
Opacity animation creates a stacking context even if it has only 100% opacity
in its keyframes
</title>
<style>
span {
  height: 100px;
  width: 100px;
  position: fixed;
  background: green;
  top: 50px;
}
@keyframes Opaque {
  from, to { opacity: 1; }
}
#test {
  width: 100px; height: 100px;
  background: blue;
  animation: Opaque 100s infinite;
}
</style>
<span></span>
<div id="test"></div>
