{"version": "1.0.0", "updated": "2024-01-01", "rules": [{"domain": "*", "selectors": ["#cookieChoiceInfo", ".cookie-banner", ".consent-banner", ".gdpr-banner", ".privacy-notice", ".cookie-notice", ".consent-modal", ".cmp-modal", "[id*='cookie-banner']", "[class*='cookie-banner']", "[id*='consent-modal']", "[class*='consent-modal']"], "actions": ["click:[id*='reject']", "click:[class*='reject']", "click:[id*='decline']", "click:[class*='decline']", "remove:.overlay", "remove:.modal-backdrop"]}, {"domain": "youtube.com", "selectors": [".ytd-consent-bump-v2-lightbox", ".consent-bump-v2-lightbox", "[aria-label*='consent']"], "actions": ["click:button[aria-label*='Reject']", "click:button[aria-label*='I disagree']"]}, {"domain": "google.com", "selectors": ["[jsname='b3VHJd']", ".VfPpkd-Bz112c-LgbsSe", "[role='dialog'][aria-modal='true']"], "actions": ["click:button[id='W0wltc']", "click:button:contains('Reject all')"]}, {"domain": "facebook.com", "selectors": ["[data-testid='cookie-policy-manage-dialog']", "._9s2p", "._4t2a"], "actions": ["click:button[data-testid='cookie-policy-manage-dialog-decline-button']", "click:button:contains('Decline optional cookies')"]}, {"domain": "twitter.com", "selectors": ["[data-testid='BottomBar']", "[data-testid='sheetDialog']"], "actions": ["click:button[data-testid='declineAllButton']"]}, {"domain": "reddit.com", "selectors": ["._1m0iFpls1wkPZJVo38-LSh", ".<PERSON><PERSON>"], "actions": ["click:button:contains('Reject non-essential')", "click:button:contains('Decline')"]}, {"domain": "linkedin.com", "selectors": [".artdeco-global-alert", "[data-test-id='cookie-consent-banner']"], "actions": ["click:button[data-test-id='cookie-consent-banner-decline-all-button']"]}, {"domain": "instagram.com", "selectors": ["._9s2p", "[role='dialog'][aria-label*='cookie']"], "actions": ["click:button:contains('Decline optional cookies')"]}, {"domain": "amazon.com", "selectors": ["#sp-cc", ".sp-cc-container"], "actions": ["click:#sp-cc-rejectall-link"]}, {"domain": "ebay.com", "selectors": ["#gdpr-banner", ".gdpr-banner"], "actions": ["click:button[data-test-id='gdpr-banner-decline']"]}, {"domain": "github.com", "selectors": [".js-cookie-consent-banner"], "actions": ["click:.js-cookie-consent-reject"]}, {"domain": "stackoverflow.com", "selectors": [".js-consent-banner"], "actions": ["click:.js-reject-all-cookies"]}, {"domain": "medium.com", "selectors": ["[data-testid='cookie-banner']"], "actions": ["click:button:contains('Reject all')"]}, {"domain": "cnn.com", "selectors": [".optanon-alert-box-wrapper", "#onetrust-banner-sdk"], "actions": ["click:#onetrust-reject-all-handler"]}, {"domain": "bbc.com", "selectors": ["#bbccookies", ".bb<PERSON><PERSON><PERSON>-banner"], "actions": ["click:button:contains('Reject')"]}]}