# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.


  ### This moz.build was AUTOMATICALLY GENERATED from a GN config,  ###
  ### DO NOT edit it by hand.                                       ###

COMPILE_FLAGS["OS_INCLUDES"] = []
AllowCompilerWarnings()

DEFINES["ABSL_ALLOCATOR_NOTHROW"] = "1"
DEFINES["PROTOBUF_ENABLE_DEBUG_LOGGING_MAY_LEAK_PII"] = "0"
DEFINES["RTC_DAV1D_IN_INTERNAL_DECODER_FACTORY"] = True
DEFINES["RTC_ENABLE_VP9"] = True
DEFINES["WEBRTC_ENABLE_PROTOBUF"] = "0"
DEFINES["WEBRTC_LIBRARY_IMPL"] = True
DEFINES["WEBRTC_MOZILLA_BUILD"] = True
DEFINES["WEBRTC_NON_STATIC_TRACE_EVENT_HANDLERS"] = "0"
DEFINES["WEBRTC_STRICT_FIELD_TRIALS"] = "0"

FINAL_LIBRARY = "xul"


LOCAL_INCLUDES += [
    "!/ipc/ipdl/_ipdlheaders",
    "!/third_party/libwebrtc/gen",
    "/ipc/chromium/src",
    "/third_party/abseil-cpp/",
    "/third_party/libwebrtc/",
    "/tools/profiler/public"
]

if not CONFIG["MOZ_DEBUG"]:

    DEFINES["DYNAMIC_ANNOTATIONS_ENABLED"] = "0"
    DEFINES["NDEBUG"] = True
    DEFINES["NVALGRIND"] = True

if CONFIG["MOZ_DEBUG"] == "1":

    DEFINES["DYNAMIC_ANNOTATIONS_ENABLED"] = "1"

if CONFIG["OS_TARGET"] == "Android":

    DEFINES["ANDROID"] = True
    DEFINES["ANDROID_NDK_VERSION_ROLL"] = "r22_1"
    DEFINES["HAVE_SYS_UIO_H"] = True
    DEFINES["WEBRTC_ANDROID"] = True
    DEFINES["WEBRTC_ANDROID_OPENSLES"] = True
    DEFINES["WEBRTC_LINUX"] = True
    DEFINES["WEBRTC_POSIX"] = True
    DEFINES["_GNU_SOURCE"] = True
    DEFINES["__STDC_CONSTANT_MACROS"] = True
    DEFINES["__STDC_FORMAT_MACROS"] = True

if CONFIG["OS_TARGET"] == "Darwin":

    DEFINES["WEBRTC_MAC"] = True
    DEFINES["WEBRTC_POSIX"] = True
    DEFINES["_LIBCPP_HAS_NO_ALIGNED_ALLOCATION"] = True
    DEFINES["__ASSERT_MACROS_DEFINE_VERSIONS_WITHOUT_UNDERSCORES"] = "0"
    DEFINES["__STDC_CONSTANT_MACROS"] = True
    DEFINES["__STDC_FORMAT_MACROS"] = True

if CONFIG["OS_TARGET"] == "Linux":

    DEFINES["USE_AURA"] = "1"
    DEFINES["USE_GLIB"] = "1"
    DEFINES["USE_NSS_CERTS"] = "1"
    DEFINES["USE_OZONE"] = "1"
    DEFINES["USE_UDEV"] = True
    DEFINES["WEBRTC_LINUX"] = True
    DEFINES["WEBRTC_POSIX"] = True
    DEFINES["_FILE_OFFSET_BITS"] = "64"
    DEFINES["_LARGEFILE64_SOURCE"] = True
    DEFINES["_LARGEFILE_SOURCE"] = True
    DEFINES["__STDC_CONSTANT_MACROS"] = True
    DEFINES["__STDC_FORMAT_MACROS"] = True

if CONFIG["OS_TARGET"] == "OpenBSD":

    DEFINES["USE_GLIB"] = "1"
    DEFINES["USE_OZONE"] = "1"
    DEFINES["USE_X11"] = "1"
    DEFINES["WEBRTC_BSD"] = True
    DEFINES["WEBRTC_POSIX"] = True
    DEFINES["_FILE_OFFSET_BITS"] = "64"
    DEFINES["_LARGEFILE64_SOURCE"] = True
    DEFINES["_LARGEFILE_SOURCE"] = True
    DEFINES["__STDC_CONSTANT_MACROS"] = True
    DEFINES["__STDC_FORMAT_MACROS"] = True

if CONFIG["OS_TARGET"] == "WINNT":

    DEFINES["CERT_CHAIN_PARA_HAS_EXTRA_FIELDS"] = True
    DEFINES["NOMINMAX"] = True
    DEFINES["NTDDI_VERSION"] = "0x0A000000"
    DEFINES["PSAPI_VERSION"] = "2"
    DEFINES["RTC_ENABLE_WIN_WGC"] = True
    DEFINES["UNICODE"] = True
    DEFINES["USE_AURA"] = "1"
    DEFINES["WEBRTC_WIN"] = True
    DEFINES["WIN32"] = True
    DEFINES["WIN32_LEAN_AND_MEAN"] = True
    DEFINES["WINAPI_FAMILY"] = "WINAPI_FAMILY_DESKTOP_APP"
    DEFINES["WINVER"] = "0x0A00"
    DEFINES["_ATL_NO_OPENGL"] = True
    DEFINES["_CRT_RAND_S"] = True
    DEFINES["_CRT_SECURE_NO_DEPRECATE"] = True
    DEFINES["_ENABLE_EXTENDED_ALIGNED_STORAGE"] = True
    DEFINES["_HAS_EXCEPTIONS"] = "0"
    DEFINES["_HAS_NODISCARD"] = True
    DEFINES["_SCL_SECURE_NO_DEPRECATE"] = True
    DEFINES["_SECURE_ATL"] = True
    DEFINES["_UNICODE"] = True
    DEFINES["_WIN32_WINNT"] = "0x0A00"
    DEFINES["_WINDOWS"] = True
    DEFINES["__STD_C"] = True

if CONFIG["TARGET_CPU"] == "aarch64":

    DEFINES["WEBRTC_ARCH_ARM64"] = True
    DEFINES["WEBRTC_HAS_NEON"] = True

if CONFIG["TARGET_CPU"] == "arm":

    DEFINES["WEBRTC_ARCH_ARM"] = True
    DEFINES["WEBRTC_ARCH_ARM_V7"] = True
    DEFINES["WEBRTC_HAS_NEON"] = True

if CONFIG["TARGET_CPU"] == "loongarch64":

    DEFINES["_GNU_SOURCE"] = True

if CONFIG["TARGET_CPU"] == "mips32":

    DEFINES["MIPS32_LE"] = True
    DEFINES["MIPS_FPU_LE"] = True
    DEFINES["_GNU_SOURCE"] = True

if CONFIG["TARGET_CPU"] == "mips64":

    DEFINES["_GNU_SOURCE"] = True

if CONFIG["TARGET_CPU"] == "x86":

    DEFINES["WEBRTC_ENABLE_AVX2"] = True

if CONFIG["TARGET_CPU"] == "x86_64":

    DEFINES["WEBRTC_ENABLE_AVX2"] = True

if CONFIG["MOZ_DEBUG"] == "1" and CONFIG["OS_TARGET"] == "Android":

    DEFINES["_DEBUG"] = True

if CONFIG["MOZ_DEBUG"] == "1" and CONFIG["OS_TARGET"] == "Darwin":

    DEFINES["_DEBUG"] = True

if CONFIG["MOZ_DEBUG"] == "1" and CONFIG["OS_TARGET"] == "Linux":

    DEFINES["_DEBUG"] = True

if CONFIG["MOZ_DEBUG"] == "1" and CONFIG["OS_TARGET"] == "OpenBSD":

    DEFINES["_DEBUG"] = True

if CONFIG["MOZ_DEBUG"] == "1" and CONFIG["OS_TARGET"] == "WINNT":

    DEFINES["_HAS_ITERATOR_DEBUGGING"] = "0"

if CONFIG["MOZ_X11"] == "1" and CONFIG["OS_TARGET"] == "Linux":

    DEFINES["USE_X11"] = "1"

if CONFIG["OS_TARGET"] == "Android" and CONFIG["TARGET_CPU"] == "arm":

    OS_LIBS += [
        "unwind"
    ]

if CONFIG["OS_TARGET"] == "Linux" and CONFIG["TARGET_CPU"] == "aarch64":

    DEFINES["_GNU_SOURCE"] = True

if CONFIG["OS_TARGET"] == "Linux" and CONFIG["TARGET_CPU"] == "arm":

    DEFINES["_GNU_SOURCE"] = True

if CONFIG["OS_TARGET"] == "Linux" and CONFIG["TARGET_CPU"] == "x86":

    DEFINES["_GNU_SOURCE"] = True

if CONFIG["OS_TARGET"] == "Linux" and CONFIG["TARGET_CPU"] == "x86_64":

    DEFINES["_GNU_SOURCE"] = True

Library("video_track_source_constraints_gn")
