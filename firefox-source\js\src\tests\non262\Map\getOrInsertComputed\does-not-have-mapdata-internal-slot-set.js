// |reftest| shell-option(--enable-upsert) skip-if(!Map.prototype.getOrInsertComputed)
// Copyright (C) 2015 the V8 project authors. All rights reserved.
// Copyright (C) 2024 <PERSON><PERSON>, <PERSON>. All rights reserved.
// This code is governed by the BSD license found in the LICENSE file.
/*---
esid: proposal-upsert
description: >
  Throws a TypeError if `this` is a Set Object
info: |
  Map.prototype.getOrInsertComputed ( key , callbackfn )

  ...
  1. Let M be the this value.
  2. Perform ? RequireInternalSlot(M, [[MapData]])
  ...
features: [Set, arrow-function]
---*/

assertThrowsInstanceOf(function () {
  Map.prototype.getOrInsertComputed.call(new Set(), 1, () => 1);
}, TypeError);

assertThrowsInstanceOf(function () {
  var map = new Map();
  map.getOrInsertComputed.call(new Set(), 1, () => 1);
}, TypeError);

reportCompare(0, 0);
