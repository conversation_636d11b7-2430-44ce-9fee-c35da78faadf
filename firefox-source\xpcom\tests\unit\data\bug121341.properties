# simple check
1=abc
# test whitespace trimming in key and value
  2	=   xy	
# test parsing of escaped values
3 = \u1234\t\r\n\uAB\
\u1\n
# test multiline properties
4 = this is \
multiline property
5 = this is \
	   another multiline property
# property with DOS EOL
6 = test\u0036
# test multiline property with with DOS EOL
7 = yet another multi\
    line propery
# trimming should not trim escaped whitespaces
8 =	\ttest5\u0020	
# another variant of #8
9 =     \ test6\t	    
# test UTF-8 encoded property/value
10aሴb = c췯d
# next property should test unicode escaping at the boundary of parsing buffer
# buffer size is expected to be 4096 so add comments to get to this offset
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
################################################################################
###############################################################################
11 = \uABCD
