/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

plugins {
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
}

apply plugin: 'com.android.library'

android {
    defaultConfig {
        minSdkVersion = config.minSdkVersion
        compileSdk = config.compileSdkVersion
        targetSdkVersion = config.targetSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled = false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    buildFeatures {
        compose = true
    }

    namespace = 'mozilla.components.compose.engine'
}

dependencies {
    implementation project(":components:browser-state")
    implementation project(":components:concept-engine")
    implementation project(":components:support-ktx")

    implementation libs.androidx.compose.ui
    implementation libs.androidx.compose.ui.tooling.preview
    implementation libs.androidx.compose.foundation
    implementation libs.androidx.compose.material3

    debugImplementation libs.androidx.compose.ui.tooling

    testImplementation project(':components:support-test')
    testImplementation libs.androidx.compose.ui.test
    testImplementation libs.androidx.test.core
    testImplementation libs.androidx.test.junit
    testImplementation libs.testing.robolectric

    androidTestImplementation libs.androidx.test.junit
    androidTestImplementation libs.androidx.compose.ui.test.manifest
    androidTestImplementation libs.androidx.compose.ui.test
    androidTestImplementation libs.testing.mockito
}

apply from: '../../../android-lint.gradle'
apply from: '../../../publish.gradle'
ext.configurePublish(config.componentsGroupId, project.name, project.ext.description)
