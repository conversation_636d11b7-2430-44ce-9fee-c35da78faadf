// Copyright (c) 2012 Ecma International.  All rights reserved.
// This code is governed by the BSD license found in the LICENSE file.

/*---
es5id: ********-3-124
description: >
    Object.defineProperty - 'configurable' property in 'Attributes' is
    treated as true when it is a string (value is 'false')  (8.10.5
    step 4.b)
---*/

var obj = {};

var attr = {
  configurable: "false"
};

Object.defineProperty(obj, "property", attr);

var beforeDeleted = obj.hasOwnProperty("property");

delete obj.property;

var afterDeleted = obj.hasOwnProperty("property");

assert.sameValue(beforeDeleted, true, 'beforeDeleted');
assert.sameValue(afterDeleted, false, 'afterDeleted');

reportCompare(0, 0);
