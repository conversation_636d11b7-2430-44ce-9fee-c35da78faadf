<!DOCTYPE html>
<title>
Transform animation creates a stacking context even though it's paused on
a 100% opacity keyframe
</title>
<style>
span {
  height: 100px;
  width: 100px;
  position: fixed;
  background: green;
  top: 50px;
}
@keyframes Opaque {
  from, to { opacity: 1 }
}
#test {
  width: 100px; height: 100px;
  background: blue;
  animation: Opaque 100s paused;
}
</style>
<span></span>
<div id="test"></div>
