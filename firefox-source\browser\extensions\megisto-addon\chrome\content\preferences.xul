<?xml version="1.0"?>
<!DOCTYPE prefwindow SYSTEM "chrome://megisto/locale/preferences.dtd">

<prefwindow id="megistoPreferences"
            xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
            title="&megisto.preferences.title;"
            buttons="accept,cancel"
            ondialogaccept="return MegistoPreferences.onAccept();"
            ondialogcancel="return MegistoPreferences.onCancel();"
            style="width: 600px; height: 500px;">

  <prefpane id="cookieBlockingPane" 
            label="&megisto.preferences.cookieBlocking.label;"
            image="chrome://megisto/skin/cookie-icon.png">
    
    <preferences>
      <preference id="cookieBlockingEnabled" 
                  name="megisto.cookieBlocking.enabled" 
                  type="bool"/>
      <preference id="cookieBlockingAggressive" 
                  name="megisto.cookieBlocking.aggressive" 
                  type="bool"/>
      <preference id="cookieBlockingWhitelist" 
                  name="megisto.cookieBlocking.whitelist" 
                  type="string"/>
    </preferences>

    <groupbox>
      <caption label="&megisto.preferences.cookieBlocking.settings;"/>
      
      <checkbox id="enableCookieBlocking"
                label="&megisto.preferences.cookieBlocking.enable;"
                preference="cookieBlockingEnabled"/>
      
      <checkbox id="aggressiveBlocking"
                label="&megisto.preferences.cookieBlocking.aggressive;"
                preference="cookieBlockingAggressive"/>
      
      <separator/>
      
      <label value="&megisto.preferences.cookieBlocking.whitelist.label;"/>
      <textbox id="whitelistTextbox"
               preference="cookieBlockingWhitelist"
               multiline="true"
               rows="4"
               placeholder="&megisto.preferences.cookieBlocking.whitelist.placeholder;"/>
      
      <hbox>
        <button label="&megisto.preferences.cookieBlocking.updateRules;"
                oncommand="MegistoPreferences.updateBlockingRules();"/>
        <button label="&megisto.preferences.cookieBlocking.resetRules;"
                oncommand="MegistoPreferences.resetBlockingRules();"/>
      </hbox>
    </groupbox>
    
    <groupbox>
      <caption label="&megisto.preferences.cookieBlocking.statistics;"/>
      
      <hbox>
        <label value="&megisto.preferences.cookieBlocking.blocked.today;"/>
        <label id="blockedTodayCount" value="0"/>
      </hbox>
      
      <hbox>
        <label value="&megisto.preferences.cookieBlocking.blocked.total;"/>
        <label id="blockedTotalCount" value="0"/>
      </hbox>
      
      <button label="&megisto.preferences.cookieBlocking.clearStats;"
              oncommand="MegistoPreferences.clearStatistics();"/>
    </groupbox>
  </prefpane>

  <prefpane id="youtubePane" 
            label="&megisto.preferences.youtube.label;"
            image="chrome://megisto/skin/youtube-icon.png">
    
    <preferences>
      <preference id="youtubeEnhancementEnabled" 
                  name="megisto.youtube.enhancement.enabled" 
                  type="bool"/>
      <preference id="youtubeAutoplay" 
                  name="megisto.youtube.autoplay" 
                  type="bool"/>
      <preference id="youtubeDefaultQuality" 
                  name="megisto.youtube.defaultQuality" 
                  type="string"/>
      <preference id="youtubeBlockRelated" 
                  name="megisto.youtube.blockRelated" 
                  type="bool"/>
    </preferences>

    <groupbox>
      <caption label="&megisto.preferences.youtube.enhancement;"/>
      
      <checkbox id="enableYouTubeEnhancement"
                label="&megisto.preferences.youtube.enable;"
                preference="youtubeEnhancementEnabled"/>
      
      <separator/>
      
      <hbox align="center">
        <checkbox id="youtubeAutoplayCheckbox"
                  label="&megisto.preferences.youtube.autoplay;"
                  preference="youtubeAutoplay"/>
      </hbox>
      
      <hbox align="center">
        <label value="&megisto.preferences.youtube.defaultQuality.label;"/>
        <menulist id="qualityMenulist" preference="youtubeDefaultQuality">
          <menupopup>
            <menuitem label="Auto" value="auto"/>
            <menuitem label="144p" value="144p"/>
            <menuitem label="240p" value="240p"/>
            <menuitem label="360p" value="360p"/>
            <menuitem label="480p" value="480p"/>
            <menuitem label="720p" value="720p"/>
            <menuitem label="1080p" value="1080p"/>
            <menuitem label="1440p" value="1440p"/>
            <menuitem label="2160p" value="2160p"/>
          </menupopup>
        </menulist>
      </hbox>
      
      <checkbox id="blockRelatedVideos"
                label="&megisto.preferences.youtube.blockRelated;"
                preference="youtubeBlockRelated"/>
    </groupbox>
    
    <groupbox>
      <caption label="&megisto.preferences.youtube.advanced;"/>
      
      <checkbox id="replaceEmbeds"
                label="&megisto.preferences.youtube.replaceEmbeds;"
                checked="true"
                disabled="true"/>
      
      <checkbox id="customControls"
                label="&megisto.preferences.youtube.customControls;"
                checked="true"/>
      
      <hbox>
        <button label="&megisto.preferences.youtube.testPlayer;"
                oncommand="MegistoPreferences.testVideoPlayer();"/>
        <button label="&megisto.preferences.youtube.resetPlayer;"
                oncommand="MegistoPreferences.resetVideoPlayer();"/>
      </hbox>
    </groupbox>
  </prefpane>

  <prefpane id="generalPane" 
            label="&megisto.preferences.general.label;"
            image="chrome://megisto/skin/general-icon.png">
    
    <groupbox>
      <caption label="&megisto.preferences.general.privacy;"/>
      
      <checkbox id="enableTrackingProtection"
                label="&megisto.preferences.general.trackingProtection;"
                checked="true"/>
      
      <checkbox id="enableDoNotTrack"
                label="&megisto.preferences.general.doNotTrack;"
                checked="true"/>
      
      <checkbox id="disableTelemetry"
                label="&megisto.preferences.general.disableTelemetry;"
                checked="true"
                disabled="true"/>
    </groupbox>
    
    <groupbox>
      <caption label="&megisto.preferences.general.updates;"/>
      
      <radiogroup id="updateMode">
        <radio label="&megisto.preferences.general.updates.auto;" value="auto"/>
        <radio label="&megisto.preferences.general.updates.manual;" value="manual" selected="true"/>
        <radio label="&megisto.preferences.general.updates.disabled;" value="disabled"/>
      </radiogroup>
      
      <hbox>
        <button label="&megisto.preferences.general.updates.check;"
                oncommand="MegistoPreferences.checkForUpdates();"/>
        <button label="&megisto.preferences.general.updates.history;"
                oncommand="MegistoPreferences.showUpdateHistory();"/>
      </hbox>
    </groupbox>
    
    <groupbox>
      <caption label="&megisto.preferences.general.about;"/>
      
      <description>&megisto.preferences.general.description;</description>
      
      <hbox>
        <button label="&megisto.preferences.general.homepage;"
                oncommand="MegistoPreferences.openHomepage();"/>
        <button label="&megisto.preferences.general.support;"
                oncommand="MegistoPreferences.openSupport();"/>
      </hbox>
    </groupbox>
  </prefpane>

  <script type="application/javascript" src="chrome://megisto/content/preferences.js"/>
</prefwindow>
