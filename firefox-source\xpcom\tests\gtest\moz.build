# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

UNIFIED_SOURCES += [
    "Helpers.cpp",
    "MozPromiseExamples.cpp",
    "TestArenaAllocator.cpp",
    "TestArrayAlgorithm.cpp",
    "TestAtoms.cpp",
    "TestAutoOwningEventTarget.cpp",
    "TestAutoRefCnt.cpp",
    "TestBase64.cpp",
    "TestCallTemplates.cpp",
    "TestCloneInputStream.cpp",
    "TestCOMPtrEq.cpp",
    "TestCRT.cpp",
    "TestDafsa.cpp",
    "TestDelayedRunnable.cpp",
    "TestEncoding.cpp",
    "TestEscape.cpp",
    "TestEventPriorities.cpp",
    "TestEventTargetQI.cpp",
    "TestFile.cpp",
    "TestGCPostBarriers.cpp",
    "TestID.cpp",
    "TestIDUtils.cpp",
    "TestINIParser.cpp",
    "TestInputStreamLengthHelper.cpp",
    "TestJSHolderMap.cpp",
    "TestLogCommandLineHandler.cpp",
    "TestLogging.cpp",
    "TestMemoryPressure.cpp",
    "TestMoveString.cpp",
    "TestMozPromise.cpp",
    "TestMruCache.cpp",
    "TestMultiplexInputStream.cpp",
    "TestNonBlockingAsyncInputStream.cpp",
    "TestNsDeque.cpp",
    "TestNSPRLogModulesParser.cpp",
    "TestObserverArray.cpp",
    "TestObserverService.cpp",
    "TestOwningNonNull.cpp",
    "TestPLDHash.cpp",
    "TestPriorityQueue.cpp",
    "TestQueue.cpp",
    "TestRacingServiceManager.cpp",
    "TestRecursiveMutex.cpp",
    "TestRustRegex.cpp",
    "TestRWLock.cpp",
    "TestSegmentedBuffer.cpp",
    "TestSlicedInputStream.cpp",
    "TestSmallArrayLRUCache.cpp",
    "TestSnappyStreams.cpp",
    "TestStateMirroring.cpp",
    "TestStateWatching.cpp",
    "TestStorageStream.cpp",
    "TestStrings.cpp",
    "TestStringStream.cpp",
    "TestSubstringTuple.cpp",
    "TestSynchronization.cpp",
    "TestTArray.cpp",
    "TestTArray2.cpp",
    "TestTaskController.cpp",
    "TestTaskQueue.cpp",
    "TestTextFormatter.cpp",
    "TestThreadManager.cpp",
    "TestThreadPoolIdleTimeout.cpp",
    "TestThreadPoolListener.cpp",
    "TestThreadUtils.cpp",
    "TestThrottledEventQueue.cpp",
    "TestTimeStamp.cpp",
    "TestTokenizer.cpp",
    "TestUTF.cpp",
    "TestVariant.cpp",
]

# Bug 1894540 - Fails under TSAN
if not CONFIG["MOZ_TSAN"]:
    UNIFIED_SOURCES += [
        "TestThreadPool.cpp",
    ]

if CONFIG["OS_TARGET"] != "Android":
    UNIFIED_SOURCES += [
        "TestPipes.cpp",
        "TestThreads.cpp",
    ]

# skip the test on OSX due to frequent failures (bug 1571186)
if CONFIG["OS_TARGET"] != "Darwin":
    UNIFIED_SOURCES += ["TestExpirationTracker.cpp"]

# skip the test on windows10-aarch64 and Android, aarch64 due to bug 1545670
if CONFIG["OS_TARGET"] != "Android":
    UNIFIED_SOURCES += ["TestTimers.cpp"]


if (
    CONFIG["MOZ_DEBUG"]
    and CONFIG["OS_ARCH"] not in ("WINNT")
    and CONFIG["OS_TARGET"] != "Android"
):
    # FIXME bug 523392: TestDeadlockDetector doesn't like Windows
    # Bug 1054249: Doesn't work on Android
    UNIFIED_SOURCES += [
        "TestDeadlockDetector.cpp",
        "TestDeadlockDetectorScalability.cpp",
    ]

if CONFIG["OS_TARGET"] == "WINNT":
    UNIFIED_SOURCES += [
        "TestFileNTFSSpecialPaths.cpp",
        "TestFilePreferencesWin.cpp",
        "TestHandleWatcher.cpp",
    ]
else:
    UNIFIED_SOURCES += [
        "TestFilePreferencesUnix.cpp",
    ]

# Bug 1940978 - Fails 50% of the time when the address santizer is enabled
if CONFIG["OS_TARGET"] == "WINNT" and not CONFIG["MOZ_ASAN"]:
    UNIFIED_SOURCES += [
        "TestAvailableMemoryWatcherWin.cpp",
    ]


if CONFIG["OS_TARGET"] == "Darwin":
    UNIFIED_SOURCES += [
        "TestAvailableMemoryWatcherMac.cpp",
        "TestThreads_mac.mm",
    ]

if CONFIG["TARGET_OS"] == "OSX":
    UNIFIED_SOURCES += [
        "TestMacNSURLEscaping.mm",
    ]

if CONFIG["OS_TARGET"] == "Linux":
    UNIFIED_SOURCES += [
        "TestAvailableMemoryWatcherLinux.cpp",
    ]

if (
    CONFIG["WRAP_STL_INCLUDES"]
    and CONFIG["CC_TYPE"] != "clang-cl"
    and CONFIG["OS_TARGET"] != "Android"
):
    UNIFIED_SOURCES += [
        "TestSTLWrappers.cpp",
    ]

if CONFIG["MOZ_MEMORY"]:
    UNIFIED_SOURCES += [
        "TestAllocReplacement.cpp",
    ]

SOURCES += [
    "TestCOMArray.cpp",
    "TestCOMPtr.cpp",  # Redefines IFoo and IBar
    "TestHashtables.cpp",  # Redefines IFoo
    "TestNsRefPtr.cpp",  # Redefines Foo
]

LOCAL_INCLUDES += [
    "../../base",
    "/toolkit/components/telemetry/tests/gtest",
    "/xpcom/components",
]

GeneratedFile(
    "dafsa_test_1.inc",
    script="../../ds/tools/make_dafsa.py",
    inputs=["dafsa_test_1.dat"],
)

TEST_HARNESS_FILES.gtest += [
    "wikipedia/ar.txt",
    "wikipedia/de-edit.txt",
    "wikipedia/de.txt",
    "wikipedia/ja.txt",
    "wikipedia/ko.txt",
    "wikipedia/ru.txt",
    "wikipedia/th.txt",
    "wikipedia/tr.txt",
    "wikipedia/vi.txt",
]

FINAL_LIBRARY = "xul-gtest"

include("/ipc/chromium/chromium-config.mozbuild")
