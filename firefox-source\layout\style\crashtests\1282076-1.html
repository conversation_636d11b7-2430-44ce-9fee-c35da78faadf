<script>
function start() {
  o0=document;
  o24=document.createElement('table');
  o35=window;
  o60=document.createElement('input');
  o24.appendChild(o60);
  o62=document.body;
  o66=document.createElement('input');
  o62.appendChild(o66);
  o60.innerHTML="<svg><color-profile><script><rect><animateColor><style><style>*{ all: unset<script><style>div<style>";
  o93=o60.querySelectorAll('*')[5];
  o97=o60.querySelectorAll('*')[9];
  document.body.appendChild(o24);
  o305=document.createTextNode("{}:first-line{");
  o93.appendChild(o305);
  o318=(new DOMParser()).parseFromString('','text/html');
  o320=o318.all[1];
  o355=document.createElement('style');
  o356=document.createTextNode("@keyframes key2{ from{ opacity: 0}}#id2{ animation-name: key2; animation-duration: 0.01s");
  o355.appendChild(o356);
  o97.appendChild(o355);
  o66.style.display='list-item';
  o473=document.createElement('script');
  o24.appendChild(o473);
  o577=document.createElement('style');
  o320.appendChild(o577);
  o577.style.position='fixed';
  document.replaceChild(o318.documentElement,document.documentElement);
  o908=(new DOMParser()).parseFromString('','text/html');
  o911=o908.all[2];
  o911.style.display='inline';
  o577.id='id2';
  o1202=document.createElement('table');
  document.body=o911;
  document.body.appendChild(o1202);
  document.replaceChild(o0.documentElement,document.documentElement);
  o1232=o473.parentNode;
  o1233=o1232.parentNode;
  document.body=o1233;
  o35.scrollByLines(1);
  o577.style.position='absolute';
  setTimeout(f2, 4);
}

function f2() {
  o0.designMode='on';
  o0.execCommand('insertparagraph',false,null);
}
</script>
<body onload="start()"></body>
