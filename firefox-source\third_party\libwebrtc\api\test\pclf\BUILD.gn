# Copyright (c) 2022 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../../webrtc.gni")

rtc_source_set("media_configuration") {
  visibility = [ "*" ]
  testonly = true
  sources = [
    "media_configuration.cc",
    "media_configuration.h",
  ]

  deps = [
    "../..:array_view",
    "../..:audio_options_api",
    "../..:media_stream_interface",
    "../..:rtp_parameters",
    "../../../rtc_base:checks",
    "../../../rtc_base:stringutils",
    "../../../test:fileutils",
    "../../../test:video_frame_writer",
    "../../../test/pc/e2e/analyzer/video:video_dumping",
    "../../units:time_delta",
    "../video:video_frame_writer",
    "//third_party/abseil-cpp/absl/strings:string_view",
  ]
}

rtc_library("media_quality_test_params") {
  visibility = [ "*" ]
  testonly = true
  sources = [ "media_quality_test_params.h" ]

  deps = [
    ":media_configuration",
    "../..:async_dns_resolver",
    "../..:fec_controller_api",
    "../..:field_trials_view",
    "../..:ice_transport_interface",
    "../..:libjingle_peerconnection_api",
    "../..:scoped_refptr",
    "../../../p2p:port_allocator",
    "../../../rtc_base:checks",
    "../../../rtc_base:network",
    "../../../rtc_base:rtc_certificate_generator",
    "../../../rtc_base:ssl",
    "../../../rtc_base:threading",
    "../../audio:audio_mixer_api",
    "../../audio:audio_processing",
    "../../audio_codecs:audio_codecs_api",
    "../../neteq:neteq_api",
    "../../rtc_event_log:rtc_event_log_factory_interface",
    "../../transport:bitrate_settings",
    "../../transport:network_control",
    "../../units:time_delta",
    "../../video_codecs:video_codecs_api",
  ]
}

rtc_library("peer_configurer") {
  visibility = [ "*" ]
  testonly = true
  sources = [
    "peer_configurer.cc",
    "peer_configurer.h",
  ]
  deps = [
    ":media_configuration",
    ":media_quality_test_params",
    "../..:async_dns_resolver",
    "../..:create_peer_connection_quality_test_frame_generator",
    "../..:fec_controller_api",
    "../..:field_trials_view",
    "../..:frame_generator_api",
    "../..:ice_transport_interface",
    "../..:libjingle_peerconnection_api",
    "../..:network_emulation_manager_api",
    "../..:peer_network_dependencies",
    "../..:scoped_refptr",
    "../../../p2p:port_allocator",
    "../../../rtc_base:checks",
    "../../../rtc_base:rtc_certificate_generator",
    "../../../rtc_base:ssl",
    "../../audio:audio_mixer_api",
    "../../audio:audio_processing",
    "../../audio_codecs:audio_codecs_api",
    "../../neteq:neteq_api",
    "../../rtc_event_log:rtc_event_log_factory_interface",
    "../../transport:bitrate_settings",
    "../../transport:network_control",
    "../../video_codecs:video_codecs_api",
    "//third_party/abseil-cpp/absl/strings:string_view",
  ]
}
