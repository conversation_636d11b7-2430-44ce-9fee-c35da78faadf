<!DOCTYPE html>
<meta charset="utf-8">
<title>CSS display: State changes are handled correctly for display: contents children</title>
<link rel="help" href="https://drafts.csswg.org/css-display-3/#valdef-display-contents">
<link rel="author" name="<PERSON>" href="mailto:<EMAIL>">
<link rel="match" href="display-contents-state-change-ref.html">
<style>
:focus-within .contents {
  color: green;
}
.contents {
  display: contents;
  border: 10px solid red;
  color: red;
}
input {
  position: absolute;
  left: -9999px;
  top: -9999px;
}
</style>
<div>
  <input type="text">
  <div class="contents">This text should be green, there should be no red border at any time.</div>
</div>
<script>
onload = function() {
  document.querySelector('input').focus();
}
</script>
