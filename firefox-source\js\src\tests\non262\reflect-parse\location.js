// |reftest| skip-if(!xulRuntime.shell)
function test() {

// Source location information
var withoutFileOrLine = Reflect.parse("42");
var withFile = Reflect.parse("42", {source:"foo.js"});
var withFileAndLine = Reflect.parse("42", {source:"foo.js", line:111});

Pattern({ source: null, start: { line: 1, column: 1 }, end: { line: 1, column: 3 } }).match(withoutFileOrLine.loc);
Pattern({ source: "foo.js", start: { line: 1, column: 1 }, end: { line: 1, column: 3 } }).match(withFile.loc);
Pattern({ source: "foo.js", start: { line: 111, column: 1 }, end: { line: 111, column: 3 } }).match(withFileAndLine.loc);

var withoutFileOrLine2 = Reflect.parse("foo +\nbar");
var withFile2 = Reflect.parse("foo +\nbar", {source:"foo.js"});
var withFileAndLine2 = Reflect.parse("foo +\nbar", {source:"foo.js", line:111});

<PERSON>tern({ source: null, start: { line: 1, column: 1 }, end: { line: 2, column: 4 } }).match(withoutFileOrLine2.loc);
Pattern({ source: "foo.js", start: { line: 1, column: 1 }, end: { line: 2, column: 4 } }).match(withFile2.loc);
Pattern({ source: "foo.js", start: { line: 111, column: 1 }, end: { line: 112, column: 4 } }).match(withFileAndLine2.loc);

var nested = Reflect.parse("(-b + sqrt(sqr(b) - 4 * a * c)) / (2 * a)", {source:"quad.js"});
var fourAC = nested.body[0].expression.left.right.arguments[0].right;

Pattern({ source: "quad.js", start: { line: 1, column: 21 }, end: { line: 1, column: 30 } }).match(fourAC.loc);

// No source location

assertEq("loc" in Reflect.parse("42", {loc:false}), false);
program([exprStmt(lit(42))]).assert(Reflect.parse("42", {loc:false}));

}

runtest(test);
