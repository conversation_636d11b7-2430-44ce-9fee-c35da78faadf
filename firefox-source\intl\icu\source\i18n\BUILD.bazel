# © 2021 and later: Unicode, Inc. and others.
# License & terms of use: http://www.unicode.org/copyright.html

# This file defines <PERSON>zel targets for a subset of the ICU4C "i18n" library header and source files.
# The configuration of dependencies among targets is strongly assisted by the
# file in depstest that maintains such information, at
# icu4c/source/test/depstest/dependencies.txt .

load("@rules_cc//cc:defs.bzl", "cc_binary", "cc_library")

package(
    default_visibility = ["//visibility:public"],
)

# When compiling code in the `common` dir, the constant
# `U_I18n_IMPLEMENTATION` needs to be defined. See 
# https://unicode-org.github.io/icu/userguide/howtouseicu#c-with-your-own-build-system .

# If linker errors occur, then this may be a sign that the dependencies were 
# not specified correctly. Use dependencies.txt in depstest for assistance. See
# https://stackoverflow.com/q/66111709/2077918 .

cc_library(
    name = "headers",
    hdrs = glob([
        "unicode/*.h", # public
        "*.h",         # internal
    ]),
    # We need to add includes in order to preserve existing source files'
    # include directives that use traditional paths, not paths relative to
    # Bazel workspace:
    # https://stackoverflow.com/a/65635893/2077918
    includes = ["."],
    local_defines = [
        "U_I18N_IMPLEMENTATION",
    ],
)

cc_library(
    name = "collation",
    srcs = [
        "bocsu.cpp",
        "coleitr.cpp",
        "coll.cpp",
        "collation.cpp",
        "collationcompare.cpp",
        "collationdata.cpp",
        "collationdatareader.cpp",
        "collationdatawriter.cpp",
        "collationfastlatin.cpp",
        # collationfcd.cpp is generated by genuca;
        # probably hard to build genuca without depending on the old version.
        "collationfcd.cpp",
        "collationiterator.cpp",
        "collationkeys.cpp",
        "collationroot.cpp",
        "collationrootelements.cpp",
        "collationsets.cpp",
        "collationsettings.cpp",
        "collationtailoring.cpp",
        "rulebasedcollator.cpp",
        "sortkey.cpp",
        "ucol.cpp",
        "ucol_res.cpp",
        "ucol_sit.cpp",
        "ucoleitr.cpp",
        "uitercollationiterator.cpp",
        "utf16collationiterator.cpp",
        "utf8collationiterator.cpp",
    ],
    includes = ["."],
    deps = [
        ":headers",
        ":uclean_i18n",
        "//icu4c/source/common:bytestream",
        "//icu4c/source/common:normalizer2",
        "//icu4c/source/common:platform",
        "//icu4c/source/common:propname",
        "//icu4c/source/common:resourcebundle",
        "//icu4c/source/common:service_registration",
        "//icu4c/source/common:ucharstrieiterator",
        "//icu4c/source/common:uiter",
        "//icu4c/source/common:ulist",
        "//icu4c/source/common:unifiedcache",
        "//icu4c/source/common:uset",
        "//icu4c/source/common:usetiter",
        "//icu4c/source/common:utrie2",
        "//icu4c/source/common:uvector32",
        "//icu4c/source/common:uvector64",
    ],
    local_defines = [
        "U_I18N_IMPLEMENTATION",
    ],
)

cc_library(
    name = "collation_builder",
    srcs = [
        "collationbuilder.cpp",
        "collationdatabuilder.cpp",
        "collationfastlatinbuilder.cpp",
        "collationruleparser.cpp",
        "collationweights.cpp",
    ],
    includes = ["."],
    deps = [
        ":collation",
        "//icu4c/source/common:canonical_iterator",
        "//icu4c/source/common:ucharstriebuilder",
        "//icu4c/source/common:uset_props"
    ],
    local_defines = [
        "U_I18N_IMPLEMENTATION",
    ],
)

cc_library(
    name = "uclean_i18n",
    srcs = [
        "ucln_in.cpp",
    ],
    hdrs = ["ucln_in.h"],
    includes = ["."],
    deps = [
        "//icu4c/source/common:platform",
    ],
    local_defines = [
        "U_I18N_IMPLEMENTATION",
    ],
)
