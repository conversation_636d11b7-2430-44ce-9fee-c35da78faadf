<?xml version="1.0"?>
<!--
  Any copyright is dedicated to the Public Domain.
  http://creativecommons.org/publicdomain/zero/1.0/
-->
<window title="Testing IOUtils on a chrome worker thread"
  xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul" onload="test();">

  <script src="chrome://mochikit/content/tests/SimpleTest/SimpleTest.js" />
  <script src="chrome://mochikit/content/tests/SimpleTest/EventUtils.js"/>
  <script src="chrome://mochikit/content/tests/SimpleTest/WorkerHandler.js"/>

  <script type="application/javascript">
  <![CDATA[

      // Test IOUtils in a chrome worker.
      function test() {
        // finish() will be called in the worker.
        SimpleTest.waitForExplicitFinish();
        info("test_ioutils_worker.xhtml: Starting test");

        const worker = new ChromeWorker("file_ioutils_worker.js");
        info("test_ioutils_worker.xhtml: Chrome worker created");

        // Set up the worker with testing facilities, and start it.
        listenForTests(worker, { verbose: false });
        worker.postMessage(0);
        info("test_ioutils_worker.xhtml: Test in progress");
      };

  ]]>
  </script>

  <body xmlns="http://www.w3.org/1999/xhtml">
    <p id="display"></p>
    <div id="content" style="display:none;"></div>
    <pre id="test"></pre>
  </body>
  <label id="test-result" />
</window>
