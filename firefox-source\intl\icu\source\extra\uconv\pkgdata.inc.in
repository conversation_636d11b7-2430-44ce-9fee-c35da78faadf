# Copyright (C) 2016 and later: Unicode, Inc. and others.
# License & terms of use: http://www.unicode.org/copyright.html
#******************************************************************************
#
#   Copyright (C) 2000-2007, International Business Machines
#   Corporation and others.  All Rights Reserved.
#
#******************************************************************************
# This Makefile is used to build the ICU's data.
# It is included with the -O option to pkgdata.

PLATFORM=@platform@

top_srcdir=@top_srcdir@
srcdir=@srcdir@
top_builddir=../..

include $(top_builddir)/icudefs.mk

CPPFLAGS+= -I$(top_builddir)/common -I$(top_srcdir)/common
LIBS=@LIBS@ 
exec_prefix=@exec_prefix@
prefix=@prefix@
program_transform_name=@program_transform_name@
bindir=@bindir@
sbindir=@sbindir@
libexecdir=@libexecdir@
datadir=@datadir@
sysconfdir=@sysconfdir@
sharedstatedir=@sharedstatedir@
localstatedir=@localstatedir@
libdir=@libdir@
includedir=@includedir@
oldincludedir=@oldincludedir@
infodir=@infodir@
mandir=@mandir@
PACKAGE=@PACKAGE@
VERSION=@VERSION@

INSTALL = @INSTALL@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_DATA = @INSTALL_DATA@

INSTALL_PROGRAM=@INSTALL_PROGRAM@
INSTALL_SCRIPT=@INSTALL_SCRIPT@
INSTALL_DATA=@INSTALL_DATA@
host=@host@
host_alias=@host_alias@
host_cpu=@host_cpu@
host_vendor=@host_vendor@
host_os=@host_os@
LIB_M=@LIB_M@
CPP=@CPP@
U_IS_BIG_ENDIAN=@U_IS_BIG_ENDIAN@
platform=@platform@

##### Add the following to source/config/Makefile.in

GENCCODE=$(BINDIR)/genccode
GENCMN=$(BINDIR)/gencmn
ICUPKG=$(BINDIR)/icupkg


