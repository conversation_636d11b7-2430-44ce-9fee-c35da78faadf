# calendrical_calculations [![crates.io](https://img.shields.io/crates/v/calendrical_calculations)](https://crates.io/crates/calendrical_calculations)

<!-- cargo-rdme start -->

Calendrical calculations

This crate implements algorithms from
Calendrical Calculations by <PERSON><PERSON><PERSON> & <PERSON>, Cambridge University Press, 4th edition (2018)
as needed by [ICU4X](https://github.com/unicode-org/icu4x).

Most of these algorithms can be found as lisp code in the book or
[on GithHub](https://github.com/EdReingold/calendar-code2/blob/main/calendar.l).

The primary purpose of this crate is use by ICU4X, however if non-ICU4X users need this we are happy
to add more structure to this crate as needed.

<!-- cargo-rdme end -->

## More Information

For more information on development, authorship, contributing etc. please visit [`ICU4X home page`](https://github.com/unicode-org/icu4x).
