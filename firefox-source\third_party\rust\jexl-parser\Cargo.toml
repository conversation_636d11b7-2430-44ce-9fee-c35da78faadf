# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "jexl-parser"
version = "0.3.0"
authors = [
    "<PERSON> <<EMAIL>>",
    "The Sync Team <<EMAIL>>",
    "The Glean Team <<EMAIL>>",
]
build = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "A JEXL parser written in Rust"
readme = false
license = "MPL-2.0"
repository = "https://github.com/mozilla/jexl-rs"

[lib]
name = "jexl_parser"
path = "src/lib.rs"

[dependencies.lalrpop-util]
version = "0.19"
features = ["lexer"]

[dependencies.regex]
version = "1.3"
