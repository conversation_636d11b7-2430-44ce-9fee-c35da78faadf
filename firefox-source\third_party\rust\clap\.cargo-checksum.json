{"files": {"Cargo.lock": "05adbb69f5012f7afdf1d2397f762791fb58d30fd3a362a7a175e3af04e23265", "Cargo.toml": "31f2c76bbee24b375528b904976490b3075a510428f10dec5249e9bf00dacfc0", "LICENSE-APACHE": "c6596eb7be8581c18be736c846fb9173b69eccf6ef94c5135893ec56bd92ba08", "LICENSE-MIT": "6efb0476a1cc085077ed49357026d8c173bf33017278ef440f222fb9cbcb66e6", "README.md": "bcb96a3aedd4da85e24e201a4922e435928b0c0a2f97fd5843d0793a54e6a09f", "examples/README.md": "c5f45032ee7acda0c5f98db456cbf4f9b0362d62dc81218fcf03bcdfbd19af05", "examples/cargo-example-derive.md": "66d7455f08a038872f42d9d45f9b47ebbdbf9366fafa16b749ece8ba5bdcfcce", "examples/cargo-example-derive.rs": "e4a69cb1260b91ba07252a01ca9dceb3a9e47da44f51250d2ad64ecf1c31bd48", "examples/cargo-example.md": "60a1d0f253142de5003a9474b28a5a8324a9860fc646af6309d84594d97356bd", "examples/cargo-example.rs": "d8009d4c71e81dd1b21ba6189bef45400d907b32e1e24daa668b2ac53aabaff0", "examples/demo.md": "6c00568794ff99007ab0f08bf7e06fe521cb1f2f0fab30c219f16c6f54bf638d", "examples/demo.rs": "2b06a625319d9485b22228f25beb344c660c2b2b7f5fb5890b6881bd6c70661f", "examples/derive_ref/augment_args.rs": "274bbe3c91d72609289274a22330067dd0ab42eb9bc3c44c94ed6da149ed56eb", "examples/derive_ref/augment_subcommands.rs": "f202fd8a836552c53bb4b33a652b577f254726334f8a72f5fac0526937ab15ef", "examples/derive_ref/flatten_hand_args.rs": "d3783fdea6297ffef23939df913ece655670b617f39addfab5147dc217e70fb1", "examples/derive_ref/hand_subcommand.rs": "0fc5407369b9331ed31a4267f1c87acbdf591799d250e8793a1f8d55de314c44", "examples/derive_ref/interop_tests.md": "aaf307e313c3926b14f1265199ab18665bb3dea723729a7e6cbf3b237fa73941", "examples/escaped-positional-derive.md": "fb4b4351ceb43ef0c25048e8603dab6fdf4391b84e9b3ad9593602fa89abc572", "examples/escaped-positional-derive.rs": "f292db181e81031fe29235830caa92892f19472df03a467857450332f87efe74", "examples/escaped-positional.md": "b44e23e5706eb815e7875825be815a370f2ba38e41d7998ea762d0ef1fb2ba56", "examples/escaped-positional.rs": "e1e784bb90805ef3412f002d9660c979ebbd2d59eb609ceb7f8926c0ad8e7c31", "examples/find.md": "441ad59186eca36324d982c1b2ba458354568007cb34fc560516dc68a40ad5fd", "examples/find.rs": "fef520e193606051da628a92b4e18d2c75edafd1cec8f32b1500597be49b343b", "examples/git-derive.md": "be14fffc9f6391579d6a0d8b7af70b551b64fb7b1d3cb1b08666c208ad21f180", "examples/git-derive.rs": "056f51dd7424e2a1cc1a694581f3e5dca74d2f39f4ab9904654e815f68662e61", "examples/git.md": "005f7c82c5ab6289c6f0d0173e12b299922ba8bd662b33f506f83c12c4d308af", "examples/git.rs": "598fb8ec98764cdfe00e0e6b39c8ffa714457377b93803ab7f9b6f8a6a2e8991", "examples/multicall-busybox.md": "b8e8a89312e2088b6dd1fac5b5b92266fda9b82c40a650dae2d41b5fcc253594", "examples/multicall-busybox.rs": "0be842810003f69de33c9e65258f74a5d1ec742fb0ebc8a49fdebfecc861c408", "examples/multicall-hostname.md": "1dedcdcc9927c08cebee11fa01fda7a730f5ddf5227c4902e1eb5ff847a80346", "examples/multicall-hostname.rs": "cc58a924dd2e57281f36e30481a1cbc8186952adbf149f9e1e13f9c673614fd9", "examples/pacman.md": "a050be1727605a4202faf69db7f991bf974bba51ce0fbd41413de68e6a4af5aa", "examples/pacman.rs": "fb82fb553b428cbbeb288ed1ec38d60452ae74fc0d52ddcd4012f7a3642eb650", "examples/repl-derive.rs": "a6be1ff8837d61ba48a10be38e7ca05a2044051fc94075f784531980548cfb2e", "examples/repl.rs": "972ed0b06962f9daf20b7cee5dd6dc873fc31747e84bce646c0e057678c653f3", "examples/tutorial_builder/01_quick.md": "d2a242a6ba832cdb0a2b6f55d428d62e7048f6b7d8d8f4a75f7923da85628ca8", "examples/tutorial_builder/01_quick.rs": "0ee2d2f6e6a1ab27be173c7ff692dc47ba61759b0a06e0fff8cd99fd7d1658a4", "examples/tutorial_builder/02_app_settings.md": "4e7097deb0eb8e926978ef383b0da4bda33966d4e760ac0d258cd4127a054fba", "examples/tutorial_builder/02_app_settings.rs": "c050d19fa8c0ecefde73746200fb513da0dd78d904011a1c0a75835fd9c172b3", "examples/tutorial_builder/02_apps.md": "3cbc7010e0214d047d98104dadb76ee02ec3c5bba868758e35aebb2fdfd0fa22", "examples/tutorial_builder/02_apps.rs": "35071e4501cadef6488172d07de52186030491af23b6e60fdfaa3f8ff8b3380c", "examples/tutorial_builder/02_crate.md": "995dc62680b9d7c1983c673bf5775c07ae96175db186b5313c0d614308569ec0", "examples/tutorial_builder/02_crate.rs": "b0bdc601133bf9e566231dd6be439d0ce82b6911ca53ae4b5c1b5da62eb2acd8", "examples/tutorial_builder/03_01_flag_bool.md": "326d8267ce6fcc62e95fdb19f77bdc959067e41af48c7d6e465c0eafe2db741c", "examples/tutorial_builder/03_01_flag_bool.rs": "d49b30b527faa6a383b334e572d546fab5c4b298a5549842073ab42a33a05c94", "examples/tutorial_builder/03_01_flag_count.md": "c34ff091d239f5861a868bda051322774340be9a63dea32cbfabe8c0cc4dfd94", "examples/tutorial_builder/03_01_flag_count.rs": "a003825bc045d4d89265344cfb00082da379150e72fc5d16b33ee93ec9ec0927", "examples/tutorial_builder/03_02_option.md": "f436d35a7f2aece1a30f0258f44ae09166fb4faae3b6623beb6a824d2ed234fd", "examples/tutorial_builder/03_02_option.rs": "13e58e3f922ef79bb4f3cec63f84dc906bb20ea8bbd33e5ca3cb8cfce847095a", "examples/tutorial_builder/03_02_option_mult.md": "31930e9dbc407f5cfbb24707388d3abb17e1cd6ea4d8e9f55c15bac8d5d3168c", "examples/tutorial_builder/03_02_option_mult.rs": "9672e7fa9e17785019ab3099b9562d8cb49324590c687c74d9e91e73bd0ef1dd", "examples/tutorial_builder/03_03_positional.md": "dc2e13cf897e2e2d2b397a1ab739bfd290ec04f437128030eb7f6f609b910be7", "examples/tutorial_builder/03_03_positional.rs": "582ffa5b056363a74d2ba966d212fbd6b00f42fe42f0e71ae95009fe75746349", "examples/tutorial_builder/03_03_positional_mult.md": "f1d03ca58d9ee75ec9a4cc980006a666e2c529ab6f7deaf6bd88b3fbaf09fc07", "examples/tutorial_builder/03_03_positional_mult.rs": "271a9ee04038244fdf378c6a603af9d4cb6a401d82f9e28928c2e7ad971aff10", "examples/tutorial_builder/03_04_subcommands.md": "64f5899e1bfb8c5f166642db89ba1de1a7f2dc88b3ea0025b3ce5543f12a605f", "examples/tutorial_builder/03_04_subcommands.rs": "a309a332004edbed6dc14b64b1ba6cc0cd42871f3c5b8da7daab772764839c68", "examples/tutorial_builder/03_05_default_values.md": "c30c72c85190aaddc06a7c0ed1cf87356eb81f03284b0e9c5f47d4f233d06a00", "examples/tutorial_builder/03_05_default_values.rs": "96c8bd0004cf0dc0338851ccf4ae3309ec49e61cc90f41bcf912207eac95ea18", "examples/tutorial_builder/04_01_enum.md": "c89cef996c17aa909031233b283349b23427c065b634d43ee82b974f4f28d1c6", "examples/tutorial_builder/04_01_enum.rs": "72e71f091343c973f1ca4b0fc22eac6db817320d8e0a11ed99355ecd1553b2cf", "examples/tutorial_builder/04_01_possible.md": "e16ec1ee5e7a67ab040afd4c87c3ebf84e5ab229491b73f3da337160a0dd14bb", "examples/tutorial_builder/04_01_possible.rs": "3d781b26d257061b55a957744c2dbd99c1d2346c151230fb18fe5f576d19585c", "examples/tutorial_builder/04_02_parse.md": "d02e8e79e96c2def7a2576a5acc06ba539061a221960ce89eeca1a51e52ef317", "examples/tutorial_builder/04_02_parse.rs": "fb9f82791717775b7112bc829e950db29277c5b21367d73e322a87f326678fd4", "examples/tutorial_builder/04_02_validate.md": "3f9f29b3629f0d20d8ff149ec721ee67a4baad39c12e2e379d472865c6cf682f", "examples/tutorial_builder/04_02_validate.rs": "a5c03e60d4e475d23c38b253bcea29a4673f9494f60359a243f09b84c34e5a2a", "examples/tutorial_builder/04_03_relations.md": "cfc6a4ee6d6912566773c9c86b92466725e8eb90c5c765d7cb603f2ffd0d911a", "examples/tutorial_builder/04_03_relations.rs": "f367c93ddc7db37ec002a59e12d93ad58bdcf8b4b832c45adb4f6b197db31f0f", "examples/tutorial_builder/04_04_custom.md": "11581660afa0679cda9355965cf55e0a282da9184f8d2eec97a415f48878002e", "examples/tutorial_builder/04_04_custom.rs": "1da17cebc5a6a2887ba8f39070a47408c7ca6f16256a071f10d10a151b5e1f40", "examples/tutorial_builder/05_01_assert.rs": "c42469ab8eeaaaab6dcec816459a934fb1380d208b14055d15e3e877b375bb20", "examples/tutorial_derive/01_quick.md": "345bbe0689766fd83929826bffc32c9067c8e97c42fb7390db2620931bd27834", "examples/tutorial_derive/01_quick.rs": "72d4d18abec6ffff5dec325372e920a7faaa7cdf5f3d68458484944a7dee60d5", "examples/tutorial_derive/02_app_settings.md": "7b3ef753653be837d6c7d92c805a8238dd61fce2fc46d58b860c3771dbfec113", "examples/tutorial_derive/02_app_settings.rs": "551e752d8baa2aee3b75248ac0476b321a2a3fb28db402c2d26ea3b9990fce45", "examples/tutorial_derive/02_apps.md": "166b48034c788304ce9d3f48f91dd7b1b7e18a6d66f689114ba618d79b60cc24", "examples/tutorial_derive/02_apps.rs": "b1031c150f39440b04403cfad02c923872acc053c3ca58fdc57c6af4ea44f1fe", "examples/tutorial_derive/02_crate.md": "c15606793151791bddf5763f660a913f799303e8de4c1d28334449d0d184681d", "examples/tutorial_derive/02_crate.rs": "dad7603268e52fc091f23e5c19042abef601c277851b325fd911bcc60f126281", "examples/tutorial_derive/03_01_flag_bool.md": "7ffa235eb4bc61aaa15d63d4301250e2a6635520c02a55aa6294afe6798f39a0", "examples/tutorial_derive/03_01_flag_bool.rs": "9eb1ddedc4b682c47dc2043787ca65551e3b7c74052833a6e6c607e795008a7f", "examples/tutorial_derive/03_01_flag_count.md": "02e6dc26d00cc82109350ca7910e4962007b91fe82fde1c8b86ae49dc4428a76", "examples/tutorial_derive/03_01_flag_count.rs": "5959afc98120dc8d8ca773f1833f7cd0614363704fadce213166bf9ac5b6d116", "examples/tutorial_derive/03_02_option.md": "3ff91da5ee7bb987bf3e4e66ab1148973664ca9b50b9dd97d40b76f623a0f607", "examples/tutorial_derive/03_02_option.rs": "6fb1cf14b927a0b2bf6fc761f83e83a93f40e12acf7bbbd42ddf4961d7988b0d", "examples/tutorial_derive/03_02_option_mult.md": "4241088813da3acd0079dfbcb3dea896388642d7fc0c976a143824da031d17d9", "examples/tutorial_derive/03_02_option_mult.rs": "54f9d253860da2ea4affa9632b7b0ffbf2786fdfc6a0e1876ecc685234ed650a", "examples/tutorial_derive/03_03_positional.md": "52a5c10dccc3774a29e1fe6f7ff87d44eaee67d723404ddcbec24f32b7b0f048", "examples/tutorial_derive/03_03_positional.rs": "0be3799af833672493b66432a356dc4484f9ca51073c55c0a6bb6adfba930e6e", "examples/tutorial_derive/03_03_positional_mult.md": "00e3fc665835a4df1190a5ecb7c854b1259ab2324966d69ac40770cde8e2dd21", "examples/tutorial_derive/03_03_positional_mult.rs": "6fd6ff50456fb5aeec5287670c00671c3bc9301f0fbd1c60888db5e121863d8f", "examples/tutorial_derive/03_04_subcommands.md": "361ef1b34f84919831248a8ea141474a89efb1bc46b977ade51f61cc3dfc85a1", "examples/tutorial_derive/03_04_subcommands.rs": "c0765d2269681d5b221ed0404b7443b980a3c72a5f831193c51aba8ae045fa43", "examples/tutorial_derive/03_04_subcommands_alt.rs": "0b5b85f339037d805673c0fa0fcdc63499959d27eaeb705a20a25bef2369f452", "examples/tutorial_derive/03_05_default_values.md": "b09e17bf0322311801881f53d9b86e5179163d4b05263988a79fd7d2b5c1eb79", "examples/tutorial_derive/03_05_default_values.rs": "52b347b338206903d3ad3f7233967d3e7f73b4554d9103dddd41d44ea34e5058", "examples/tutorial_derive/04_01_enum.md": "fa1deb854c06f6ca921b53e9fd98eaf8ff0a641095a592552537ed74197ef801", "examples/tutorial_derive/04_01_enum.rs": "c9757a342aee558014c30b0758ef5f5359e675ebbb7647571d4b227f4021c184", "examples/tutorial_derive/04_02_parse.md": "a87492c5d6fb9817345f67389142282394c525fbeade3a2750b84d949cba1c7e", "examples/tutorial_derive/04_02_parse.rs": "532ef1b9dae8a6fca32b49b5b79ddd36b82ab799fed360f4ae2b25122a7b7424", "examples/tutorial_derive/04_02_validate.md": "8f83fdf4ccb89550c0df20eef49358cb222ca1a7404c18e3eafed6413c0e1313", "examples/tutorial_derive/04_02_validate.rs": "bae539ef173dc8e44101ce17135b7952b9a4850b732e6115e1a3effd683edd20", "examples/tutorial_derive/04_03_relations.md": "15172a1e5b056ee30b6ab1fbe848c71d8499b06aa74ce30219275989034c76ac", "examples/tutorial_derive/04_03_relations.rs": "fb0a2ea97fd034d21fdfee8876ead25ea2b163b4a033e703c74bb301310e98df", "examples/tutorial_derive/04_04_custom.md": "f96f4ced9cce7e04e5150c2906cac315bce04a5319cab080917dd976cb815bdc", "examples/tutorial_derive/04_04_custom.rs": "88cb3129e53eb22aa1c83342d5f8cde8cf35bd486c851acc5cb493a0868bad19", "examples/tutorial_derive/05_01_assert.rs": "55f7e86100a95800634b959c5cf25fcb246226e4edd03784bfc1371776d6dda1", "examples/typed-derive.md": "6eacd8a18cf5c77f974247e00d2f60f58b6520915c9956cb7f432be2da1757d9", "examples/typed-derive.rs": "a7fee93f140e34d4981581d98be0c5df074232e26cf45a5e6bff27acaa445348", "src/_cookbook/cargo_example.rs": "fe7594a5233e9106a159aa1f5d5f0cde0d844356f630d55c78b8ef322327d4e5", "src/_cookbook/cargo_example_derive.rs": "badf3e931ef5d5b7f5addc4912aca057ba83ee6302c43d7eeecd1661673fd407", "src/_cookbook/escaped_positional.rs": "2789d8fea126355805b29e76b52c6cea4982565014087a46e0d48e4ddfbed7ab", "src/_cookbook/escaped_positional_derive.rs": "802d0b672f4ed48152235d4a26a64c97fa921b361177bdd3a1a33cbac96f665d", "src/_cookbook/find.rs": "6152ad5df466adc6de56bddb0f01930b4e4db1997fa972b055217cc7d6748a0a", "src/_cookbook/git.rs": "372977252a22fc776e3acaa4629e964114ccd6a49b8ef334d2b6646f12e8b5ee", "src/_cookbook/git_derive.rs": "4ab7c0197efda06607ca60c2a85ea743aed3494f5fe9e408d935ea0500a345fc", "src/_cookbook/mod.rs": "c195ca5300ef525d3aa229f7b51f88b3fe60bbf8ce93e4e540192b61a58c7538", "src/_cookbook/multicall_busybox.rs": "56176b4fa15e7a39c433706971d4e68aaf26ddc2a5790078b6dbe722ee13efee", "src/_cookbook/multicall_hostname.rs": "907f8decf81ea4d4cbf81639ea4cd2f925eda64d4831454a7656369b65522780", "src/_cookbook/pacman.rs": "863125b2d3d7931a9e4541c8ab1242b8bfcb421d8b5c604ea681efd805f68da7", "src/_cookbook/repl.rs": "1393209b2cc5c203296d57c5a065b764b4318be7855e48baf16de851e250cf90", "src/_cookbook/repl_derive.rs": "99afa5a3cd44fabcce093dc821922eb52721113cf8925c38de6759bda96cb545", "src/_cookbook/typed_derive.rs": "3d28e78cd0b068b4fcb32a7fea6244de176f2fe75dfcb59e99c33b66a7ae4864", "src/_derive/_tutorial/chapter_0.rs": "e5ecb3c09c97ff7f84eb71f17a30d812931cfaf0b14e3824699f1a8227464b63", "src/_derive/_tutorial/chapter_1.rs": "8110c437807ee259b4e4170bfbcf2fb24d406068567a9725f9d81d66809d9963", "src/_derive/_tutorial/chapter_2.rs": "d807f24fab16e2c3f6f8f69fca6d3fc5f12223ee75ad38525398a1d70230a21a", "src/_derive/_tutorial/chapter_3.rs": "a49d2d450a5c6676aa39fa82bf2067b058ca76e65c81b2d2bd7905aee5e146e3", "src/_derive/_tutorial/chapter_4.rs": "42eeb27e352911dc783c7206747f539ca119bffcbddaa8e3a8490e7595095891", "src/_derive/_tutorial/chapter_5.rs": "490d800ada1c4727d386a0075c5e0752c9b6996d486f0c17de51431f8b61fa29", "src/_derive/_tutorial/mod.rs": "9253795ea95033c1271b340fcb05ff0da52cc76b820e8a0c9083e63ca3ef7194", "src/_derive/mod.rs": "63ad595726855900f9f178c6bf6af864807cc913194d46d7644aa5576d43fbec", "src/_faq.rs": "96c82d8954b1e2075cc6488e8048e3335dd9bb15c360a9fde4991b0097715705", "src/_features.rs": "e7ba4ed83b9ff2aef65e6773d603a4aaa0eb0ff96bc86dde4360e20385bf2809", "src/_tutorial/chapter_0.rs": "14ec920659bcda23dd4f392a987a6961bb7c04d2c01655dc2bc9c818f0e32f7f", "src/_tutorial/chapter_1.rs": "719d6e4316d3496046077bf1cea91ee2c4444f100f6837d60031e1c624876202", "src/_tutorial/chapter_2.rs": "0eb36348ab64c6560d4d0eb562f9f0fb02b94ccd58837efd3e64bb3092aee717", "src/_tutorial/chapter_3.rs": "6f336c84909e809283309706239ba56a03ee23d13aa95e9b4eeb4d314ed50f87", "src/_tutorial/chapter_4.rs": "beae5be9b371fb9139421ce668a27304c64beb78e5ab6e4b3c624d849f364850", "src/_tutorial/chapter_5.rs": "e85e08d4d9daf0f7f23a6b3b1f4fc612d3b910d370836883f262ec11d8634d23", "src/_tutorial/mod.rs": "cb73f13e7c8b006185a8323580f0495e1580047c426dd6d515afeb40d69bfaef", "src/bin/stdio-fixture.rs": "927921d48a5bbab06f8a329f42f6e76e20c843107df603443482dbb509b7b2e1", "src/lib.rs": "3e4683af31b4e871f86c8ab6b89b6e580252295a3b83b8172fd98032c04687dc"}, "package": "ed6719fffa43d0d87e5fd8caeab59be1554fb028cd30edc88fc4369b17971019"}