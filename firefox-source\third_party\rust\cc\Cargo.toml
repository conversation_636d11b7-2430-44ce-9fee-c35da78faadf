# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
rust-version = "1.63"
name = "cc"
version = "1.2.12"
authors = ["<PERSON> <<EMAIL>>"]
build = false
exclude = [
    "/.github",
    "tests",
    "src/bin",
]
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = """
A build-time dependency for Cargo build scripts to assist in invoking the native
C compiler to compile native C code into a static archive to be linked into Rust
code.
"""
homepage = "https://github.com/rust-lang/cc-rs"
documentation = "https://docs.rs/cc"
readme = "README.md"
keywords = ["build-dependencies"]
categories = ["development-tools::build-utils"]
license = "MIT OR Apache-2.0"
repository = "https://github.com/rust-lang/cc-rs"

[lib]
name = "cc"
path = "src/lib.rs"

[dependencies.jobserver]
version = "0.1.30"
optional = true
default-features = false

[dependencies.shlex]
version = "1.3.0"

[dev-dependencies.tempfile]
version = "3"

[features]
jobserver = []
parallel = [
    "dep:libc",
    "dep:jobserver",
]

[target."cfg(unix)".dependencies.libc]
version = "0.2.62"
optional = true
default-features = false
