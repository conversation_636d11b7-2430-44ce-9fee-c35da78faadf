<!DOCTYPE html>
<html class="reftest-wait">
<title>
Transform animation winning over transition creates a stacking context
for correct style
</title>
<style>
span {
  height: 100px;
  width: 100px;
  position: fixed;
  background: green;
  top: 50px;
}
@keyframes TransformNone {
  from, to { transform: none; }
}
#test {
  width: 100px; height: 100px;
  background: blue;
  transform: translateX(200px);
  transition: transform 100s steps(1, start);
  animation: TransformNone 100s;
}
</style>
<span></span>
<div id="test"></div>
<script>
window.addEventListener("load", () => {
  var target = document.getElementById("test");
  getComputedStyle(target).transform;

  // CSS animation wins over transition, so transition won't be visible during
  // the CSS animation.
  target.style.transform = "translateX(100px)";
  getComputedStyle(target).transform;
  document.documentElement.classList.remove("reftest-wait");
});
</script>
