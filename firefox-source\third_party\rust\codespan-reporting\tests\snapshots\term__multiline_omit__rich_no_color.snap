---
source: codespan-reporting/tests/term.rs
expression: TEST_DATA.emit_no_color(&config)
---
error[empty_if]: empty elseif block
   ┌─ empty_if_comments.lua:1:1
   │    
 1 │ ╭   elseif 3 then
 2 │ │   
 3 │ │ ╭ 
 4 │ │ │ 
 5 │ │ │ 
   · │ │
 8 │ │ │ 
 9 │ │ │ 
   │ │ ╰' content should be in here
10 │ │   else
   │ ╰───^

error[E0308]: mismatched types
   ┌─ src/lib.rs:2:6
   │  
 2 │       1
   │ ╭─────^
 3 │ │     + 1
 4 │ │     + 1
   · │
 7 │ │     +1
   │ │      - missing whitespace
 8 │ │     + 1
 9 │ │     + 1
10 │ │     + 1
   │ ╰───────^ expected (), found integer
   │  
   = note:	expected type `()`
     	found type `{integer}`


