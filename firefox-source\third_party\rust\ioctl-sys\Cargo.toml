# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies
#
# If you believe there's an error in this file please file an
# issue against the rust-lang/cargo repository. If you're
# editing this file be aware that the upstream Cargo.toml
# will likely look very different (and much more reasonable)

[package]
name = "ioctl-sys"
version = "0.7.1"
authors = ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"]
include = ["Cargo.toml", "**/*.rs"]
description = "IO Control for POSIX-and-beyond systems (core fn & macros, see `ioctls` for many ioctl definitions)"
documentation = "https://docs.rs/ioctl-sys"
license = "MIT OR Apache-2.0"
repository = "https://github.com/jmesmon/ioctl"

[dependencies]
