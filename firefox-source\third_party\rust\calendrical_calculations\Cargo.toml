# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.82"
name = "calendrical_calculations"
version = "0.2.0"
authors = ["The ICU4X Project Developers"]
build = false
include = [
    "data/**/*",
    "src/**/*",
    "examples/**/*",
    "benches/**/*",
    "tests/**/*",
    "Cargo.toml",
    "LICENSE",
    "README.md",
    "build.rs",
]
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "Calendrical calculations in Rust"
readme = "README.md"
keywords = [
    "zerocopy",
    "serialization",
    "zero-copy",
    "serde",
]
categories = [
    "rust-patterns",
    "memory-management",
    "caching",
    "no-std",
    "data-structures",
]
license = "Apache-2.0"
repository = "https://github.com/unicode-org/icu4x"

[package.metadata.docs.rs]
all-features = true

[package.metadata.workspaces]
independent = true

[features]
logging = ["dep:log"]

[lib]
name = "calendrical_calculations"
path = "src/lib.rs"

[dependencies.core_maths]
version = "0.1.0"
default-features = false

[dependencies.displaydoc]
version = "0.2.3"
default-features = false

[dependencies.log]
version = "0.4.17"
optional = true
default-features = false
