# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.82"
name = "icu_time"
version = "2.0.0"
authors = ["The ICU4X Project Developers"]
build = false
include = [
    "data/**/*",
    "src/**/*",
    "examples/**/*",
    "benches/**/*",
    "tests/**/*",
    "Cargo.toml",
    "LICENSE",
    "README.md",
    "build.rs",
]
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "Processing of dates, times, and time zones with a focus on i18n and interop"
homepage = "https://icu4x.unicode.org"
readme = "README.md"
categories = ["internationalization"]
license = "Unicode-3.0"
repository = "https://github.com/unicode-org/icu4x"

[package.metadata.docs.rs]
all-features = true

[features]
alloc = [
    "tinystr/alloc",
    "zerotrie/alloc",
    "serde?/alloc",
]
compiled_data = [
    "dep:icu_time_data",
    "icu_calendar/compiled_data",
    "icu_provider/baked",
]
datagen = [
    "serde",
    "dep:databake",
    "zerovec/databake",
    "zerotrie/databake",
    "tinystr/databake",
    "icu_provider/export",
    "icu_locale_core/databake",
]
default = [
    "compiled_data",
    "ixdtf",
]
ixdtf = [
    "dep:ixdtf",
    "icu_calendar/ixdtf",
]
serde = [
    "dep:serde",
    "zerovec/serde",
    "zerotrie/serde",
    "tinystr/serde",
    "icu_provider/serde",
    "icu_locale_core/serde",
]

[lib]
name = "icu_time"
path = "src/lib.rs"

[dependencies.calendrical_calculations]
version = "0.2.0"
default-features = false

[dependencies.databake]
version = "0.2.0"
features = ["derive"]
optional = true
default-features = false

[dependencies.displaydoc]
version = "0.2.3"
default-features = false

[dependencies.icu_calendar]
version = "~2.0.0"
default-features = false

[dependencies.icu_locale_core]
version = "2.0.0"
features = ["zerovec"]
default-features = false

[dependencies.icu_provider]
version = "2.0.0"
default-features = false

[dependencies.icu_time_data]
version = "~2.0.0"
optional = true
default-features = false

[dependencies.ixdtf]
version = "0.5.0"
optional = true
default-features = false

[dependencies.serde]
version = "1.0.110"
features = ["derive"]
optional = true
default-features = false

[dependencies.tinystr]
version = "0.8.0"
features = ["zerovec"]
default-features = false

[dependencies.writeable]
version = "0.6.0"
default-features = false

[dependencies.zerotrie]
version = "0.2.0"
features = [
    "yoke",
    "zerofrom",
]
default-features = false

[dependencies.zerovec]
version = "0.11.1"
features = [
    "derive",
    "yoke",
]
default-features = false

[dev-dependencies]
