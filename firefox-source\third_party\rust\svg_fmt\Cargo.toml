# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies
#
# If you believe there's an error in this file please file an
# issue against the rust-lang/cargo repository. If you're
# editing this file be aware that the upstream Cargo.toml
# will likely look very different (and much more reasonable)

[package]
edition = "2018"
name = "svg_fmt"
version = "0.4.1"
authors = ["<PERSON> <<EMAIL>>"]
description = "Very simple debugging utilities to dump shapes in SVG format."
documentation = "https://docs.rs/svg_fmt/"
keywords = ["2d", "graphics", "svg"]
license = "MIT/Apache-2.0"
repository = "https://github.com/nical/rust_debug"

[dependencies]
