XPCOM
=====

These pages contain documentation for Mozilla's Cross-Platform Component Object Model (XPCOM) module.  It abstracts core systems functionality for cross-platform use.  The component architecture follows the standard COM approach.


Consuming XPCOM functionality
-----------------------------

.. toctree::
   :maxdepth: 1

   logging
   stringguide
   refptr
   thread-safety
   collections
   hashtables
   hashtables_detailed
   mozpromise
   sorting.md
   xpcomutils

Tooling around XPCOM
--------------------

.. toctree::
   :maxdepth: 1

   huntingleaks

Writing new XPCOM interfaces/classes
------------------------------------

.. toctree::
   :maxdepth: 1

   xpidl
   writing-xpcom-interface
   cc-macros
   fmt-in-gecko.md
