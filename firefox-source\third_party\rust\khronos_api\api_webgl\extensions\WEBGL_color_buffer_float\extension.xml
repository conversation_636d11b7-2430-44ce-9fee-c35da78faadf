<?xml version="1.0" encoding="UTF-8"?>
<extension href="WEBGL_color_buffer_float/">
  <name>WEBGL_color_buffer_float</name>

  <contact><a href="https://www.khronos.org/webgl/public-mailing-list">WebGL
  working group</a> (public_webgl 'at' khronos.org) </contact>

  <contributors>
    <contributor><PERSON>, HI Corporation</contributor>

    <contributor>Members of the WebGL working group</contributor>
  </contributors>

  <number>15</number>

  <depends>
    <api version="1.0"/>

    <ext name="EXT_color_buffer_half_float"/>

    <ext name="OES_texture_float" require="true"/>

    <subsumed version="2.0" by="EXT_color_buffer_float" />
  </depends>

  <overview>
    <p>Adds support for rendering to 32-bit floating-point color buffers.</p>

    <features>
      <feature>
        <p>The 32-bit floating-point type <code>RGBA32F</code> becomes available
        as a color-renderable format. Renderbuffers can be created in this
        format. These and textures created with <code>format = RGBA</code> and
        <code>type = FLOAT</code> as specified in <a
        href="http://www.khronos.org/registry/webgl/extensions/OES_texture_float/">OES_texture_float</a>,
        can be attached to framebuffer object color attachments for rendering.
        </p>
      </feature>

      <feature>
        <p>The 32-bit floating-point type <code>RGB32F</code> may also optionally
	become available as a color-renderable format. These and textures created
	with <code>format = RGB</code> and <code>type = FLOAT</code> as specified in
	<a href="http://www.khronos.org/registry/webgl/extensions/OES_texture_float/">OES_texture_float</a>,
        can be attached to framebuffer object color attachments for rendering.
	Applications must check framebuffer completeness to determine if an
	implementation actually supports this format.
        </p>
      </feature>

      <feature>
        <p><span style="color: red">NOTE:</span> fragment shaders outputs
        gl_FragColor and gl_FragData[0] will only be clamped and converted
        when the color buffer is fixed-point and <code>blendColor()</code> and
        <code>clearColor()</code> will no longer clamp their parameter values
        on input. Clamping will be applied as necessary at draw time according
        to the type of color buffer in use.</p>
      </feature>

      <feature>
        <p>The format and type combination <code>RGBA</code> and
        <code>FLOAT</code> becomes valid for reading from a floating-point
        rendering buffer. Note: <code>RGBA</code> and
        <code>UNSIGNED_BYTE</code> cannot be used for reading from a
        floating-point rendering buffer.</p>
      </feature>

      <feature>
        <p>The component types of framebuffer object attachments can be
        queried.</p>
      </feature>
    </features>
  </overview>

  <idl xml:space="preserve">
[NoInterfaceObject]
interface WEBGL_color_buffer_float {
  const GLenum RGBA32F_EXT = 0x8814;
  const GLenum FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE_EXT = 0x8211;
  const GLenum UNSIGNED_NORMALIZED_EXT = 0x8C17;
}; // interface WEBGL_color_buffer_float
  </idl>

  <newtok>
    <function name="renderbufferStorage" type="void"><param name="target"
    type="GLenum"/><param name="internalformat" type="GLenum"/><param
    name="width" type="GLsizei"/><param name="height"
    type="GLsizei"/><code>RGBA32F_EXT</code> is accepted as the
    <code>internalformat</code> parameter of
    <code>renderbufferStorage()</code>.</function>
  </newtok>

  <additions>
    <p>The new tokens and the behavioral changes for floating-point color
    buffers specified in <a
    href="http://www.khronos.org/registry/webgl/extensions/EXT_color_buffer_half_float/">EXT_color_buffer_half_float</a>
    are incorporated into WebGL except for the <code>RGB16F</code> and
    <code>RGBA16F</code> types. References to <code>RGB16F</code> are ignored,
    and references to <code>RGBA16F</code> are replaced by references to
    <code>RGBA32F</code>.</p>
  </additions>

  <history>
    <revision date="2012/11/08">
      <change>Initial revision.</change>
    </revision>

    <revision date="2012/11/12">
      <change>Don't mirror EXT_color_buffer_half_float. Mirror has a different
      meaning from what is done here.</change>
    </revision>

    <revision date="2012/11/13">
      <change>Add reading-pixels-as-FLOAT feature to the Overview.</change>
    </revision>

    <revision date="2012/11/26">
      <change>Move to draft.</change>
    </revision>

    <revision date="2014/07/15">
      <change>Removed webgl module. Added NoInterfaceObject extended attribute.</change>
    </revision>

    <revision date="2014/09/11">
      <change>Fixed the name of the interface from EXT_color_buffer_float to WEBGL_color_buffer_float.</change>
    </revision>

    <revision date="2014/11/24">
      <change>Removed the support for RGB32F, since it is not natively supported on all platforms where WebGL is implemented.</change>
    </revision>

    <revision date="2014/11/24">
      <change>Move to community approved.</change>
    </revision>

    <revision date="2016/05/05">
      <change>Subsumed in WebGL 2.0 by EXT_color_buffer_float.</change>
    </revision>

    <revision date="2017/09/14">
      <change>Add optional RGB renderability.</change>
    </revision>
  </history>
</extension>
