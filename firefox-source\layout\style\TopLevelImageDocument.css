/* -*- Mode: C++; tab-width: 2; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

/*
  This CSS stylesheet defines the rules to be applied to ImageDocuments that
  are top level (e.g. not iframes).
*/

@media not print {
  :root {
    /* The font color here was chosen to be readable over the corresponding
       backgrounds. This is important in case this ImageDocument is for an
       image that happens to be corrupt, in which case we'll display a textual
       error message over the background, instead of the image itself. */
    color: #eee;
    /* The background-attachment is fixed to stop an ugly white gutter
       from appearing when the document is overscrolled. */
    background: url("chrome://global/skin/media/imagedoc-darknoise.png") fixed;
  }

  img.transparent {
    color: #222;
    background: hsl(0, 0%, 90%) url("chrome://global/skin/media/imagedoc-lightnoise.png");
  }

  img {
    text-align: center;
    position: absolute;
    inset: 0;
    margin: auto;
  }

  img.overflowingVertical {
    /* If we're overflowing vertically, we need to set margin-top to
       0.  Otherwise we'll end up trying to vertically center, and end
       up cutting off the top part of the image. */
    margin-top: 0;
  }

  .completeRotation {
    transition: transform 0.3s ease 0s;
  }
}

img {
  image-orientation: from-image;
}
