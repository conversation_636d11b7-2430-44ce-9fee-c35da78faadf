# icu_provider_adapters [![crates.io](https://img.shields.io/crates/v/icu_provider_adapters)](https://crates.io/crates/icu_provider_adapters)

<!-- cargo-rdme start -->

Adapters for composing and manipulating data providers.

- Use the [`fork`] module to marshall data requests between multiple possible providers.
- Use the [`either`] module to choose between multiple provider types at runtime.
- Use the [`filter`] module to programmatically reject certain data requests.
- Use the [`fallback`] module to automatically resolve arbitrary locales for data loading.

<!-- cargo-rdme end -->

## More Information

For more information on development, authorship, contributing etc. please visit [`ICU4X home page`](https://github.com/unicode-org/icu4x).
