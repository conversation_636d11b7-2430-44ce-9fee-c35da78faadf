/**
 * <PERSON><PERSON><PERSON> - YouTube Video Manager Tests
 * Playwright tests for the YouTube embed replacement functionality
 */

const { test, expect } = require('@playwright/test');
const path = require('path');

// Test configuration
const MEGISTO_BROWSER_PATH = process.env.MEGISTO_BROWSER_PATH || 
  path.join(__dirname, '../../firefox-source/obj-megisto/dist/bin/megisto.exe');

const TEST_VIDEOS = [
  {
    name: 'Standard YouTube Embed',
    videoId: 'dQw4w9WgXcQ',
    embedUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
    html: '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>'
  },
  {
    name: 'YouTube Embed with Parameters',
    videoId: 'dQw4w9WgXcQ',
    embedUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1&rel=0',
    html: '<iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1&rel=0" frameborder="0" allowfullscreen></iframe>'
  },
  {
    name: 'Multiple YouTube Embeds',
    videoId: 'dQw4w9WgXcQ',
    embedUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
    html: `
      <iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>
      <iframe width="560" height="315" src="https://www.youtube.com/embed/jNQXAC9IVRw" frameborder="0" allowfullscreen></iframe>
    `
  }
];

test.describe('Megisto YouTube Video Manager', () => {
  let browser;
  let context;

  test.beforeAll(async ({ playwright }) => {
    // Launch Megisto Browser
    browser = await playwright.firefox.launch({
      executablePath: MEGISTO_BROWSER_PATH,
      headless: false, // Set to true for CI
      args: [
        '--no-first-run',
        '--disable-default-browser-check',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding'
      ]
    });

    context = await browser.newContext({
      viewport: { width: 1280, height: 720 }
    });
  });

  test.afterAll(async () => {
    await context?.close();
    await browser?.close();
  });

  test.beforeEach(async () => {
    // Wait a bit for the extension to load
    await new Promise(resolve => setTimeout(resolve, 1000));
  });

  for (const video of TEST_VIDEOS) {
    test(`should replace ${video.name}`, async () => {
      const page = await context.newPage();
      
      try {
        // Create test page with YouTube embed
        const testHtml = `
          <html>
            <head><title>YouTube Test</title></head>
            <body>
              <h1>YouTube Embed Test</h1>
              ${video.html}
              <p>Content after video</p>
            </body>
          </html>
        `;
        
        await page.goto(`data:text/html,${encodeURIComponent(testHtml)}`);
        
        // Wait for page to load and video manager to process
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        // Check if YouTube iframe was replaced with Video.js player
        const originalIframes = await page.locator('iframe[src*="youtube.com/embed"]').count();
        const videojsPlayers = await page.locator('.megisto-youtube-player').count();
        const videojsElements = await page.locator('video-js').count();
        
        // Either the iframe should be replaced with Video.js player, or Video.js should be loading
        if (videojsPlayers > 0 || videojsElements > 0) {
          console.log(`✓ YouTube embed replaced with Video.js player for ${video.name}`);
        } else {
          // Check if Video.js is at least being loaded
          const videojsScript = await page.locator('script[src*="video"]').count();
          expect(videojsScript).toBeGreaterThan(0);
          console.log(`✓ Video.js loading initiated for ${video.name}`);
        }
        
      } finally {
        await page.close();
      }
    });
  }

  test('should handle dynamic YouTube embeds', async () => {
    const page = await context.newPage();
    
    try {
      // Create page that adds YouTube embed dynamically
      const testHtml = `
        <html>
          <body>
            <h1>Dynamic YouTube Test</h1>
            <button id="add-video">Add YouTube Video</button>
            <div id="video-container"></div>
          </body>
        </html>
      `;
      
      await page.goto(`data:text/html,${encodeURIComponent(testHtml)}`);
      
      // Add script to create dynamic embed
      await page.addScriptTag({
        content: `
          document.getElementById('add-video').onclick = function() {
            const container = document.getElementById('video-container');
            const iframe = document.createElement('iframe');
            iframe.width = '560';
            iframe.height = '315';
            iframe.src = 'https://www.youtube.com/embed/dQw4w9WgXcQ';
            iframe.frameBorder = '0';
            iframe.allowFullscreen = true;
            container.appendChild(iframe);
          };
        `
      });
      
      // Click button to add video
      await page.click('#add-video');
      
      // Wait for video manager to process
      await page.waitForTimeout(3000);
      
      // Check if dynamic embed was processed
      const videojsPlayers = await page.locator('.megisto-youtube-player').count();
      const videojsElements = await page.locator('video-js').count();
      
      if (videojsPlayers > 0 || videojsElements > 0) {
        console.log('✓ Dynamic YouTube embed processed');
      } else {
        // At minimum, Video.js should be loading
        const videojsScript = await page.locator('script[src*="video"]').count();
        expect(videojsScript).toBeGreaterThan(0);
        console.log('✓ Video.js loading for dynamic embed');
      }
      
    } finally {
      await page.close();
    }
  });

  test('should load Video.js resources', async () => {
    const page = await context.newPage();
    
    try {
      // Create page with YouTube embed
      const testHtml = `
        <html>
          <body>
            <iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>
          </body>
        </html>
      `;
      
      await page.goto(`data:text/html,${encodeURIComponent(testHtml)}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
      
      // Check if Video.js CSS is loaded
      const videojsCSS = await page.locator('link[href*="video-js.css"]').count();
      expect(videojsCSS).toBeGreaterThan(0);
      
      // Check if Video.js JavaScript is loaded
      const videojsJS = await page.locator('script[src*="video.min.js"]').count();
      expect(videojsJS).toBeGreaterThan(0);
      
      console.log('✓ Video.js resources loaded');
      
    } finally {
      await page.close();
    }
  });

  test('should respect YouTube enhancement preference', async () => {
    const page = await context.newPage();
    
    try {
      // Test with YouTube enhancement disabled
      await page.addInitScript(() => {
        // Mock the preferences to disable YouTube enhancement
        window.mockPreferences = {
          youtubeEnhancement: false,
          youtubeAutoplay: false,
          youtubeDefaultQuality: '720p',
          youtubeBlockRelated: true
        };
      });
      
      const testHtml = `
        <html>
          <body>
            <iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>
          </body>
        </html>
      `;
      
      await page.goto(`data:text/html,${encodeURIComponent(testHtml)}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Original iframe should still exist when enhancement is disabled
      const originalIframes = await page.locator('iframe[src*="youtube.com/embed"]').count();
      expect(originalIframes).toBeGreaterThan(0);
      
      console.log('✓ YouTube enhancement preference respected');
      
    } finally {
      await page.close();
    }
  });

  test('should preserve video dimensions', async () => {
    const page = await context.newPage();
    
    try {
      const testHtml = `
        <html>
          <body>
            <iframe width="800" height="450" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>
          </body>
        </html>
      `;
      
      await page.goto(`data:text/html,${encodeURIComponent(testHtml)}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
      
      // Check if dimensions are preserved in the replacement player
      const playerContainer = page.locator('.megisto-youtube-player').first();
      
      if (await playerContainer.count() > 0) {
        const containerStyle = await playerContainer.getAttribute('style');
        expect(containerStyle).toContain('800px');
        expect(containerStyle).toContain('450px');
        console.log('✓ Video dimensions preserved');
      } else {
        console.log('! Video.js player not yet initialized, skipping dimension test');
      }
      
    } finally {
      await page.close();
    }
  });

  test('should handle fallback to original embed on error', async () => {
    const page = await context.newPage();
    
    try {
      // Mock Video.js to fail loading
      await page.addInitScript(() => {
        // Prevent Video.js from loading properly
        Object.defineProperty(window, 'videojs', {
          get: () => {
            throw new Error('Video.js failed to load');
          }
        });
      });
      
      const testHtml = `
        <html>
          <body>
            <iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>
          </body>
        </html>
      `;
      
      await page.goto(`data:text/html,${encodeURIComponent(testHtml)}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
      
      // Should fallback to original iframe or create a new one
      const iframes = await page.locator('iframe[src*="youtube.com/embed"]').count();
      expect(iframes).toBeGreaterThan(0);
      
      console.log('✓ Fallback to original embed works');
      
    } finally {
      await page.close();
    }
  });

  test('should not affect non-YouTube iframes', async () => {
    const page = await context.newPage();
    
    try {
      const testHtml = `
        <html>
          <body>
            <h1>Mixed Iframe Test</h1>
            <iframe width="560" height="315" src="https://www.example.com" frameborder="0"></iframe>
            <iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>
            <iframe width="560" height="315" src="https://vimeo.com/123456" frameborder="0"></iframe>
          </body>
        </html>
      `;
      
      await page.goto(`data:text/html,${encodeURIComponent(testHtml)}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
      
      // Non-YouTube iframes should remain unchanged
      const exampleIframe = await page.locator('iframe[src="https://www.example.com"]').count();
      const vimeoIframe = await page.locator('iframe[src="https://vimeo.com/123456"]').count();
      
      expect(exampleIframe).toBe(1);
      expect(vimeoIframe).toBe(1);
      
      console.log('✓ Non-YouTube iframes preserved');
      
    } finally {
      await page.close();
    }
  });
});

// Performance tests
test.describe('YouTube Manager Performance', () => {
  test('should not significantly impact page load time', async ({ browser }) => {
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
      const startTime = Date.now();
      
      const testHtml = `
        <html>
          <body>
            <h1>Performance Test</h1>
            <iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>
          </body>
        </html>
      `;
      
      await page.goto(`data:text/html,${encodeURIComponent(testHtml)}`);
      await page.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      
      // Load time should be reasonable (less than 10 seconds including Video.js loading)
      expect(loadTime).toBeLessThan(10000);
      
      console.log(`✓ Page load time with YouTube embed: ${loadTime}ms`);
      
    } finally {
      await page.close();
      await context.close();
    }
  });
});
