#
# This file is autogenerated by pip-compile with Python 3.13
# by the following command:
#
#    pip-compile typing.in
#
iniconfig==2.0.0
    # via pytest
mypy==1.12.0
    # via -r typing.in
mypy-extensions==1.0.0
    # via mypy
nodeenv==1.9.1
    # via pyright
packaging==24.1
    # via pytest
pluggy==1.5.0
    # via pytest
pyright==1.1.385
    # via -r typing.in
pytest==8.3.3
    # via -r typing.in
typing-extensions==4.12.2
    # via
    #   mypy
    #   pyright
