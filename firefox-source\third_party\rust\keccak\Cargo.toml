# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "keccak"
version = "0.1.4"
authors = ["RustCrypto Developers"]
description = """
Pure Rust implementation of the Keccak sponge function including the keccak-f
and keccak-p variants
"""
documentation = "https://docs.rs/keccak"
readme = "README.md"
keywords = [
    "crypto",
    "sponge",
    "keccak",
    "keccak-f",
    "keccak-p",
]
categories = [
    "cryptography",
    "no-std",
]
license = "Apache-2.0 OR MIT"
repository = "https://github.com/RustCrypto/sponges/tree/master/keccak"

[features]
asm = []
no_unroll = []
simd = []

[target."cfg(target_arch = \"aarch64\")".dependencies.cpufeatures]
version = "0.2"
