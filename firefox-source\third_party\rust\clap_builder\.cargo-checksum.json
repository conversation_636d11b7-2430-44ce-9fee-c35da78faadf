{"files": {"Cargo.toml": "2de9d949bd99c3a7819dd9ae318b7162cebd8f9b9693d1cfa09ea7f16c3286bb", "LICENSE-APACHE": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "LICENSE-MIT": "0d687e1f07b58fe68bda74668ff6326125e5e5efa184cce755cd84ac535b7058", "README.md": "8a6532c8a686cbff189533cd8472b3295b4de2463420c1a94193cc6f33bd4bb0", "src/builder/action.rs": "358d34f11ca276fa2ee8b4c6c4b3601d167ad53c901c55780bfcb3d3d0f53988", "src/builder/app_settings.rs": "0ab6f9ca6d198d56547adcbeaddb2c3240df82bd79c75903d4a70b3f1f5a1b55", "src/builder/arg.rs": "63b68aafb63374e3c18200835cfcf71de87e0130da137a26587898ea11d5fb90", "src/builder/arg_group.rs": "62cbb808be8b006c412da9d96e2f83e417b5b8f274158b93b24029a4daa4ab92", "src/builder/arg_predicate.rs": "4fe55fdd0441acb1577f28188c86f920df4863f451a7dcad8e050dbb6e1e7516", "src/builder/arg_settings.rs": "e8ad1bd6d36da43d7329b6cd86833d4cc63734e64119c6b950a45ded5e3aec9c", "src/builder/command.rs": "82ca4332e1a6dfd1a46e3aa837ff0c782734dc1d5e848359fed34bc281941f44", "src/builder/debug_asserts.rs": "ccf8eaaf7a575aff748267258bd3f9c0743c01e2f3cce1dc70251012f15f9d79", "src/builder/ext.rs": "b337f096992d51f686e64096fc219340c38276c7cd27199711c5862f86925d59", "src/builder/mod.rs": "3389a15af88ed079ae6c53146897f233a1dcc5e70ea22ecb7aa98ef85432390a", "src/builder/os_str.rs": "7dddfd33e406352123857f8c421a4cec69c79004a8ef93bf59a8de30f76ab287", "src/builder/possible_value.rs": "c648df9ee5bfb4d4d9633f76b9cf8102ebbdf57e3e20263eda9e44496d631d20", "src/builder/range.rs": "b96d152e2abe90087f03540a8bf2adb2201ab4d027691a114eaf6e7569f54f43", "src/builder/resettable.rs": "c4cd481051ec361529817ebae73b4dd307b0a4a3b88a3ddcecb8dd45b05db431", "src/builder/str.rs": "24706f652973143699381bb288a50a59818dd528163f9f991c134d2110bcd9d8", "src/builder/styled_str.rs": "9c043c4b908d220346855b0ab6d35f0ea5564e9e338e2dab0697b7de2788ea5b", "src/builder/styling.rs": "1f4ceba07ad098bfe263face44617777f7fa1e21600e7aa2dfa14385bdf93fcb", "src/builder/tests.rs": "e0005bce9a53a020c3f3a37add53e3936cbbb2bac80126aca57542ae13c29252", "src/builder/value_hint.rs": "fedc7dfb968b98c3d880bf1a55c23e928d915d6cf59c69462ae8d7e9b9fee406", "src/builder/value_parser.rs": "0fe8fd36977caec2f3a287ff10ef0dea16f4a62fe18c40f7fad4d3883acf2a37", "src/derive.rs": "69ab7560ea2931358441d0218d1f8dd0b4655c535a82a353158dddcd9765ad9c", "src/error/context.rs": "156d2611a349ce826378476c9dcb3242a01ad834a095baad096a10cc2f73e1ed", "src/error/format.rs": "ceeb91bd91f58785e803f871573280bb1539f653292a28976887a91bb1188243", "src/error/kind.rs": "29603c64b8cea7886498afc3f7e7c489505c9820c5159e99d153d57fdafe95ec", "src/error/mod.rs": "d79c0e5843d65f84b6127d89dfea6d7df603552dc05e85d2f7bfe816093b107e", "src/lib.rs": "473d57ced08f03c751c4f5030f1667ac84bf8cce635b5c5e9ee183b676fbd49c", "src/macros.rs": "9be3991d735ce627eaf6b35c1ca6baae5a5e10e46b64d31033225687ad64b57b", "src/mkeymap.rs": "f91cf154bd531a05f81f34a552b33d17f551228d827f2b944f45dae3caf0522f", "src/output/fmt.rs": "d35009cc66b455fad4ad6edfe22d201187985a7bd68615d37d29873b36666dd4", "src/output/help.rs": "b3bb6cfffe9c1113d377f1f4951a22b6ad2ba2813dcc55a146899c808cd393d3", "src/output/help_template.rs": "9fc32caf5f1dd5a8040ac83bb4105a812d9f2f6aefd07cf6363ed1658db121ae", "src/output/mod.rs": "74ea52be8981c30d10fda3f6c80cf51aafb15700de99aa73bc94700bca25ae11", "src/output/textwrap/core.rs": "0d45b1ebe8bba3e54e8bcbfec015be626d55803a0a46aef753eb3a4be3cabab4", "src/output/textwrap/mod.rs": "2f532e8ebde977a5e1277956b63d2168723cab222b74b4a08bdec44a8487682d", "src/output/textwrap/word_separators.rs": "ae3af310295e707ae1aa95633f6bb078cedf8f65662a86caa9d25420f459acc1", "src/output/textwrap/wrap_algorithms.rs": "a6eb1491ef5e64dbd2ef55d367e46747fcf2fb22fdb66a2b7c3cb9ba3a02d86a", "src/output/usage.rs": "2c2369e62b8f9fb6de52c8f52966a44a23b3b4aab9922b27343be703e1cf5bec", "src/parser/arg_matcher.rs": "c4932e05eaf05c79513c722eab52b1f12195c41ff7785b8df155b0c3d54a37b2", "src/parser/error.rs": "0b240e7c5d70c921f7f74b6fbfa06b6e1ff8b277c34fc15c9ff4bc107aa3c854", "src/parser/features/mod.rs": "6ed075e97af56bff22f22ed1ee83ff6479360e05f9d3661a3145f822c242b694", "src/parser/features/suggestions.rs": "832b62f927f9f4ffb95abdcef09c8b675045aab0b5471bae484e5d07f18f7621", "src/parser/matches/arg_matches.rs": "5abb9a6c0900301e7c41f2c7dc125231517e5f6aacce4e85dce34851bd82f9a1", "src/parser/matches/matched_arg.rs": "c5418f02b4ec7ef57b93e90308a942ef94a4b83858f90e14c072c705fe48270e", "src/parser/matches/mod.rs": "5578335f4b9103cc4060546621e5664d5e92c43848ec48c2d95a4b045373de1f", "src/parser/matches/value_source.rs": "ecb9e09db06579489daa9cbcf351381041dff7c5e2956fb96052b70b9360955b", "src/parser/mod.rs": "34d689dac5d878790e5c29872f59ccec000ceab737ddaa642054cb464e26edb8", "src/parser/parser.rs": "0ab4eec3f533f04b42789463448b091b3097fd69de1e71765d92561a3b43d5ad", "src/parser/validator.rs": "df5aa94d5b5b4108757387882bf5e4f4d810a7f7358d0789983d19c8792f19d6", "src/util/any_value.rs": "ac88e78c520ae84edb160c121d1277ce64db3aea7199fbddfe1024d6c312f722", "src/util/color.rs": "260a06150df2928cfce63a38de5e275d1c573330cffe1a60cd58ba31c608c8f1", "src/util/flat_map.rs": "f2ac1eb8c94143b0def7ec83a82377972a8d1f0eee43eee90c97717c6fb24f6e", "src/util/flat_set.rs": "f570144940e263954451ae4393941b76c2bea6af0d64062f94a527daa84ac4bd", "src/util/graph.rs": "f35396b6e2a427377dcbbca69b1b98737d89684a3834cfda98cbf8cc70ff9c2f", "src/util/id.rs": "910a6b4ce06a04d9eb93364d99e801cfe0a5376a778f5ac1d0e44230c1549d96", "src/util/mod.rs": "6fe80de51c5c441620af25da961c986e51ee77e8e05dc8bc831f72e38a5bd9e9", "src/util/str_to_bool.rs": "1ce90b4939a884eeefc73392722bdfcf906e3070c4398e1557c586c10c684cd0"}, "package": "216aec2b177652e3846684cbfe25c9964d18ec45234f0f5da5157b207ed1aab6"}