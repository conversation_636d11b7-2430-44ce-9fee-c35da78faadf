@echo off
echo Starting Megisto Browser build...

set MOZCONFIG=E:\megisto\mozconfig
set MOZ_OBJDIR=E:\megisto\firefox-source\obj-megisto
set Verbose=False
set Package=False

REM Change to project directory
cd /d "E:\megisto"

REM Start MozillaBuild and run our build script
"C:\mozilla-build\msys2\usr\bin\bash.exe" -l -c "cd 'E:/megisto' && bash tools/build-internal.sh"

if %ERRORLEVEL% NEQ 0 (
    echo Build failed with error code %ERRORLEVEL%
    pause
    exit /b %ERRORLEVEL%
)

echo Build completed successfully!
