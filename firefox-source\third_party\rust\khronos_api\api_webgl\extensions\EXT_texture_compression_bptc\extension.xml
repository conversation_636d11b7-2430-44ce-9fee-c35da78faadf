<?xml version="1.0"?>

<draft href="EXT_texture_compression_bptc/">
  <name>EXT_texture_compression_bptc</name>
  <contact>
    <a href="https://www.khronos.org/webgl/public-mailing-list/">WebGL working group</a> (public_webgl 'at' khronos.org)
  </contact>
  <contributors>
    <contributor><PERSON><PERSON>, NVIDIA</contributor>
    <contributor>Members of the WebGL working group</contributor>
  </contributors>
  <number>38</number>
  <depends>
    <api version="1.0"/>
  </depends>
  <overview>
    <p>
      This extension exposes the compressed texture format defined in the
      <a href="https://www.khronos.org/registry/OpenGL/extensions/EXT/EXT_texture_compression_bptc.txt">
      EXT_texture_compression_bptc</a> OpenGL ES extension to WebGL. Consult that extension
      specification for behavioral definitions, including error behaviors.
    </p>
    <p>
      Sampling from textures in the COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT format performs a color space
      conversion as specified for SRGB textures in the
      <a href="https://www.khronos.org/registry/OpenGL/extensions/EXT/EXT_sRGB.txt">EXT_sRGB</a> OpenGL ES
      extension.
    </p>
    <features>
      <feature>
        Compression format <code>COMPRESSED_RGBA_BPTC_UNORM_EXT</code>,
        <code>COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT</code>,
        <code>COMPRESSED_RGB_BPTC_SIGNED_FLOAT_EXT</code>,
        and <code>COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT_EXT</code> may be passed to
        the <code>compressedTexImage2D</code> and <code>compressedTexSubImage2D</code> entry points.
      </feature>
      <feature>
        Calling <code>getParameter</code> with the argument <code>COMPRESSED_TEXTURE_FORMATS</code>
        will include the formats from this specification.
      </feature>
      <feature>
        <p>
          If the <code>internalformat</code> is one of the BPTC internal formats from this specification,
          the <code>byteLength</code> of the ArrayBufferView, <code>pixels</code>, passed to
          <code>compressedTexImage2D</code> or <code>compressedTexSubImage2D</code> must be
          equal to the following number of bytes:
        </p>
        <blockquote><code>
          ceil(width / 4) * ceil(height / 4) * 16
        </code></blockquote>
        <p>If it is not, an <code>INVALID_VALUE</code> error is generated.</p>
      </feature>
    </features>
  </overview>
  <idl xml:space="preserve">
[NoInterfaceObject]
interface EXT_texture_compression_bptc {
    const GLenum COMPRESSED_RGBA_BPTC_UNORM_EXT = 0x8E8C;
    const GLenum COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT = 0x8E8D;
    const GLenum COMPRESSED_RGB_BPTC_SIGNED_FLOAT_EXT = 0x8E8E;
    const GLenum COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT_EXT = 0x8E8F;
};
  </idl>

  <newtok>
    <function name="compressedTexImage2D">
      <param name="internalformat" type="GLenum"/>
      Accepted by the <code>internalformat</code> parameter:
      <code>COMPRESSED_RGBA_BPTC_UNORM_EXT</code>,
      <code>COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT</code>,
      <code>COMPRESSED_RGB_BPTC_SIGNED_FLOAT_EXT</code>,
      <code>COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT_EXT</code>
      <br/>
    </function>

    <function name="compressedTexSubImage2D">
      <param name="internalformat" type="GLenum"/>
      Accepted by the <code>internalformat</code> parameter:
      <code>COMPRESSED_RGBA_BPTC_UNORM_EXT</code>,
      <code>COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT</code>,
      <code>COMPRESSED_RGB_BPTC_SIGNED_FLOAT_EXT</code>,
      <code>COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT_EXT</code>
      <br/>
    </function>
  </newtok>

  <errors>
    <error enum="INVALID_VALUE">
      The error <code>INVALID_VALUE</code> is generated by <code>compressedTexImage2D</code> and
      <code>compressedTexSubImage2D</code> if the <code>internalformat</code> parameter is one of the BPTC
      internal formats from this extension and the byteLength of the ArrayBufferView is not:
      <blockquote><code>
        ceil(width / 4) * ceil(height / 4) * 16
      </code></blockquote>
    </error>
  </errors>

  <history>
    <revision date="2018/09/14">
      <change>Initial revision.</change>
    </revision>
    <revision date="2018/09/18">
      <change>Moved to draft status.</change>
    </revision>
    <revision date="2018/09/26">
      <change>Retarget against EXT_texture_compression_bptc instead of ARB_texture_compression_bptc</change>
      <change>Rename to EXT_texture_compression_bptc from WEBGL_compressed_texture_bptc</change>
    </revision>
  </history>
</draft>
