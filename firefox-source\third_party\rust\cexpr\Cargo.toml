# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "cexpr"
version = "0.6.0"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
description = "A C expression parser and evaluator"
documentation = "https://docs.rs/cexpr/"
keywords = ["C", "expression", "parser"]
license = "Apache-2.0/MIT"
repository = "https://github.com/jethrogb/rust-cexpr"
[dependencies.nom]
version = "7"
features = ["std"]
default-features = false
[dev-dependencies.clang-sys]
version = ">= 0.13.0, < 0.29.0"
[badges.travis-ci]
repository = "jethrogb/rust-cexpr"
