/* -*- Mode: IDL; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 4 -*- */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

/**
 * The nsIComponentManager interface.
 */

#include "nsISupports.idl"

interface nsIArray;
interface nsIUTF8StringEnumerator;

[scriptable, builtinclass, uuid(d604ffc3-1ba3-4f6c-b65f-1ed4199364c3)]
interface nsIComponentManager : nsISupports
{
    /**
     * getClassObject
     *
     * Returns the factory object that can be used to create instances of
     * CID aClass
     *
     * @param aClass The classid of the factory that is being requested
     */
    void getClassObject(in nsCIDRef aClass,
                        in nsIIDRef aIID,
                        [iid_is(aIID),retval] out nsQIResult result);

    /**
     * getClassObjectByContractID
     *
     * Returns the factory object that can be used to create instances of
     * CID aClass
     *
     * @param aClass The classid of the factory that is being requested
     */
    void getClassObjectByContractID(in string aContractID,
                                    in nsIIDRef aIID,
                                    [iid_is(aIID),retval] out nsQIResult result);


   /**
     * createInstance
     *
     * Create an instance of the CID aClass and return the interface aIID.
     *
     * @param aClass : ClassID of object instance requested
     * @param aIID : IID of interface requested
     */
    [noscript]
    void createInstance(in nsCIDRef aClass,
                        in nsIIDRef aIID,
                        [iid_is(aIID),retval] out nsQIResult result);

    /**
     * createInstanceByContractID
     *
     * Create an instance of the CID that implements aContractID and return the
     * interface aIID.
     *
     * @param aContractID : aContractID of object instance requested
     * @param aIID : IID of interface requested
     */
    [noscript]
    void createInstanceByContractID(in string aContractID,
                                    in nsIIDRef aIID,
                                    [iid_is(aIID),retval] out nsQIResult result);

    /**
     * getManifestLocations
     *
     * Get an array of nsIURIs of all registered and builtin manifest locations.
     */
    nsIArray getManifestLocations();

    /**
     * Returns a list of ESM URLs which are used to create components. This
     * should only be used in automation.
     */
    nsIUTF8StringEnumerator getComponentESModules();
};


%{ C++
#ifdef MOZILLA_INTERNAL_API
#include "nsComponentManagerUtils.h"
#endif
%} C++
