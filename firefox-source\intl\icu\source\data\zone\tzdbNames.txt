// ***************************************************************************
// Copyright (C) 2016 and later: Unicode, Inc. and others.
// License & terms of use: http://www.unicode.org/copyright.html
// *
// * Copyright (C) 2014 International Business Machines
// * Corporation and others. All Rights Reserved.
// *
// * This file is manually edited for supporting the tz database name
// * compatibility.
// *
// ***************************************************************************#
tzdbNames{
    zoneStrings{
        "meta:Acre"{
            sd{"ACST"}
            ss{"ACT"}
            parseRegions{"BR"}
        }
        "meta:Afghanistan"{
            ss{"AFT"}
        }
        "meta:Africa_Central"{
            sd{"CAST"}
            ss{"CAT"}
        }
        "meta:Africa_Eastern"{
            sd{"EAST"}
            ss{"EAT"}
        }
        "meta:Africa_FarWestern"{
            ss{"WAT"}
            parseRegions{""} // this metazone is never used for parsing
        }
        "meta:Africa_Southern"{
            ss{"SAST"}
        }
        "meta:Africa_Western"{
            sd{"WAST"}
            ss{"WAT"}
        }
        "meta:Aktyubinsk"{
            sd{"AKTST"}
            ss{"AKTT"}
        }
        "meta:Alaska"{
            sd{"AKDT"}
            ss{"AKST"}
        }
        "meta:Alaska_Hawaii"{
            sd{"AHDT"}
            ss{"AHST"}
        }
        "meta:Almaty"{
            sd{"ALMST"}
            ss{"ALMT"}
            parseRegions{""} // this metazone is never used for parsing
        }
        "meta:Amazon"{
            sd{"AMST"}
            ss{"AMT"}
        }
        "meta:America_Central"{
            sd{"CDT"}
            ss{"CST"}
        }
        "meta:America_Eastern"{
            sd{"EDT"}
            ss{"EST"}
        }
        "meta:America_Mountain"{
            sd{"MDT"}
            ss{"MST"}
        }
        "meta:America_Pacific"{
            sd{"PDT"}
            ss{"PST"}
        }
        "meta:Anadyr"{
            sd{"ANAST"}
            ss{"ANAT"}
        }
        "meta:Apia"{
            sd{"WSDT"}
            ss{"WST"}
        }
        "meta:Aqtau"{
            sd{"AQTST"}
            ss{"AQTT"}
            parseRegions{""} // this metazone is never used for parsing
        }
        "meta:Aqtobe"{
            sd{"AQTST"}
            ss{"AQTT"}
            parseRegions{""} // this metazone is never used for parsing
        }
        "meta:Arabian"{
            sd{"ADT"}
            ss{"AST"}
            parseRegions{"BH", "IQ", "KW", "QA", "SA", "YE"}
        }
        "meta:Argentina"{
            sd{"ARST"}
            ss{"ART"}
        }
        "meta:Argentina_Western"{
            sd{"WARST"}
            ss{"WART"}
        }
        "meta:Armenia"{
            sd{"AMST"}
            ss{"AMT"}
            parseRegions{"AM"}
        }
        "meta:Ashkhabad"{
            sd{"ASHST"}
            ss{"ASHT"}
        }
        "meta:Atlantic"{
            sd{"ADT"}
            ss{"AST"}
        }
        "meta:Australia_Central"{
            sd{"ACDT"}
            ss{"ACST"}
        }
        "meta:Australia_CentralWestern"{
            sd{"ACWDT"}
            ss{"ACWST"}
        }
        "meta:Australia_Eastern"{
            sd{"AEDT"}
            ss{"AEST"}
        }
        "meta:Australia_Western"{
            sd{"AWDT"}
            ss{"AWST"}
        }
        "meta:Azerbaijan"{
            sd{"AZT"}
            ss{"AZST"}
        }
        "meta:Azores"{
            sd{"AZOST"}
            ss{"AZOT"}
        }
        "meta:Baku"{
            sd{"BAKST"}
            ss{"BAKT"}
        }
        "meta:Bangladesh"{
            sd{"BDST"}
            ss{"BDT"}
        }
        "meta:Bering"{
            sd{"BDT"}
            ss{"BST"}
            parseRegions{""} // this metazone is never used for parsing
        }
        "meta:Bhutan"{
            ss{"BTT"}
        }
        "meta:Bolivia"{
            ss{"BOT"}
        }
        "meta:Borneo"{
            ss{"BORT"}
        }
        "meta:Brasilia"{
            sd{"BRST"}
            ss{"BRT"}
        }
        "meta:British"{
            sd{"BST"}
            parseRegions{""} // this metazone is never used for parsing
        }
        "meta:Brunei"{
            ss{"BNT"}
        }
        "meta:Cape_Verde"{
            sd{"CVST"}
            ss{"CVT"}
        }
        "meta:Casey"{
            ss{"CAST"}
            parseRegions{"AQ"}
        }
        "meta:Chamorro"{
            ss{"ChST"}
        }
        "meta:Changbai"{
            ss{"CHAT"}
        }
        "meta:Chatham"{
            sd{"CHAST"}
            ss{"CHADT"}
        }
        "meta:Chile"{
            sd{"CLST"}
            ss{"CLT"}
        }
        "meta:China"{
            sd{"CDT"}
            ss{"CST"}
            parseRegions{"CN", "MO"}
        }
        "meta:Choibalsan"{
            sd{"CHOST"}
            ss{"CHOT"}
        }
        "meta:Christmas"{
            ss{"CXT"}
        }
        "meta:Cocos"{
            ss{"CCT"}
        }
        "meta:Colombia"{
            sd{"COST"}
            ss{"COT"}
        }
        "meta:Cook"{
            sd{"CKHST"}
            ss{"CKT"}
        }
        "meta:Cuba"{
            sd{"CDT"}
            ss{"CST"}
            parseRegions{"CU"}
        }
        "meta:Dacca"{
            ss{"DACT"}
        }
        "meta:Davis"{
            ss{"DAVT"}
        }
        "meta:Dominican"{
            sd{"EHDT"}
            ss{"EST"}
        }
        "meta:DumontDUrville"{
            ss{"DDUT"}
        }
        "meta:Dushanbe"{
            sd{"DUSST"}
            ss{"DUST"}
        }
        "meta:Dutch_Guiana"{
            ss{"NEGT"}
        }
        "meta:East_Timor"{
            ss{"TLT"}
        }
        "meta:Easter"{
            sd{"EASST"}
            ss{"EAST"}
            parseRegions{"CL"}
        }
        "meta:Ecuador"{
            ss{"ECT"}
        }
        "meta:Europe_Central"{
            sd{"CEST"}
            ss{"CET"}
        }
        "meta:Europe_Eastern"{
            sd{"EEST"}
            ss{"EET"}
        }
        "meta:Europe_Western"{
            sd{"WEST"}
            ss{"WET"}
        }
        "meta:Falkland"{
            ss{"FKST"}
        }
        "meta:Fiji"{
            sd{"FJST"}
            ss{"FJT"}
        }
        "meta:French_Guiana"{
            ss{"GFT"}
        }
        "meta:French_Southern"{
            ss{"TFT"}
        }
        "meta:Frunze"{
            sd{"FRUST"}
            ss{"FRUT"}
        }
        "meta:GMT"{
            ss{"GMT"}
        }
        "meta:Galapagos"{
            ss{"GALT"}
        }
        "meta:Gambier"{
            ss{"GAMT"}
        }
        "meta:Georgia"{
            sd{"GEST"}
            ss{"GET"}
        }
        "meta:Gilbert_Islands"{
            ss{"GILT"}
        }
        "meta:Goose_Bay"{
            sd{"ADT"}
            ss{"AST"}
            parseRegions{"CA"}
        }
        "meta:Greenland_Central"{
            sd{"CGST"}
            ss{"CGT"}
        }
        "meta:Greenland_Eastern"{
            sd{"EGST"}
            ss{"EGT"}
        }
        "meta:Greenland_Western"{
            sd{"WGST"}
            ss{"WGT"}
        }
        "meta:Guam"{
            ss{"GST"}
            parseRegions{""} // this metazone is never used for parsing
        }
        "meta:Gulf"{
            ss{"GST"}
        }
        "meta:Guyana"{
            ss{"GYT"}
        }
        "meta:Hawaii_Aleutian"{
            sd{"HDT"}
            ss{"HST"}
        }
        "meta:Hong_Kong"{
            sd{"HKST"}
            ss{"HKT"}
        }
        "meta:Hovd"{
            sd{"HOVST"}
            ss{"HOVT"}
        }
        "meta:India"{
            ss{"IST"}
        }
        "meta:Indian_Ocean"{
            ss{"IOT"}
        }
        "meta:Indochina"{
            ss{"ICT"}
        }
        "meta:Indonesia_Central"{
            ss{"WITA"}
        }
        "meta:Indonesia_Eastern"{
            ss{"WIT"}
        }
        "meta:Indonesia_Western"{
            ss{"WIB"}
        }
        "meta:Iran"{
            sd{"IRDT"}
            ss{"IRST"}
        }
        "meta:Irkutsk"{
            sd{"IRKST"}
            ss{"IRKT"}
        }
        "meta:Israel"{
            sd{"IDT"}
            ss{"IST"}
            parseRegions{"IL", "PS"}
        }
        "meta:Japan"{
            sd{"JDT"}
            ss{"JST"}
        }
        "meta:Kamchatka"{
            sd{"PETST"}
            ss{"PETT"}
        }
        "meta:Karachi"{
            ss{"KART"}
        }
        "meta:Kazakhstan_Eastern"{
            sd{"ALMST"}
            ss{"ALMT"}
        }
        "meta:Kazakhstan_Western"{
            sd{"AQTST"}
            ss{"AQTT"}
        }
        "meta:Kizilorda"{
            sd{"KIZST"}
            ss{"KIZT"}
        }
        "meta:Korea"{
            sd{"KDT"}
            ss{"KST"}
        }
        "meta:Kosrae"{
            ss{"KOST"}
        }
        "meta:Krasnoyarsk"{
            sd{"KRAST"}
            ss{"KRAT"}
        }
        "meta:Kuybyshev"{
            sd{"KUYST"}
            ss{"KUYT"}
        }
        "meta:Kwajalein"{
            ss{"KWAT"}
        }
        "meta:Kyrgystan"{
            sd{"KGST"}
            ss{"KGT"}
        }
        "meta:Lanka"{
            ss{"LKT"}
        }
        "meta:Liberia"{
            ss{"LRT"}
        }
        "meta:Line_Islands"{
            ss{"LINT"}
        }
        "meta:Lord_Howe"{
            sd{"LHDT"}
            ss{"LHST"}
        }
        "meta:Macau"{
            sd{"MOST"}
            ss{"MOT"}
        }
        "meta:Macquarie"{
            ss{"MIST"}
        }
        "meta:Magadan"{
            sd{"MAGST"}
            ss{"MAGT"}
        }
        "meta:Malaya"{
            ss{"MALT"}
        }
        "meta:Malaysia"{
            ss{"MYT"}
        }
        "meta:Maldives"{
            ss{"MVT"}
        }
        "meta:Marquesas"{
            ss{"MART"}
        }
        "meta:Marshall_Islands"{
            ss{"MHT"}
        }
        "meta:Mauritius"{
            sd{"MUST"}
            ss{"MUT"}
        }
        "meta:Mawson"{
            ss{"MAWT"}
        }
        "meta:Mexico_Northwest"{
            sd{"PDT"}
            ss{"PST"}
        }
        "meta:Mexico_Pacific"{
            sd{"MDT"}
            ss{"MST"}
            parseRegions{"MX"}
        }
        "meta:Mongolia"{
            sd{"ULAST"}
            ss{"ULAT"}
        }
        "meta:Moscow"{
            sd{"MSD"}
            ss{"MSK"}
        }
        "meta:Myanmar"{
            ss{"MMT"}
        }
        "meta:Nauru"{
            ss{"NRT"}
        }
        "meta:Nepal"{
            ss{"NPT"}
        }
        "meta:New_Caledonia"{
            sd{"NCST"}
            ss{"NCT"}
        }
        "meta:New_Zealand"{
            sd{"NZDT"}
            ss{"NZST"}
        }
        "meta:Newfoundland"{
            sd{"NDT"}
            ss{"NST"}
        }
        "meta:Niue"{
            ss{"NUT"}
        }
        "meta:Norfolk"{
            ss{"NFT"}
        }
        "meta:Noronha"{
            sd{"FNST"}
            ss{"FNT"}
        }
        "meta:North_Mariana"{
            ss{"MPT"}
        }
        "meta:Novosibirsk"{
            sd{"NOVST"}
            ss{"NOVT"}
        }
        "meta:Omsk"{
            sd{"OMSST"}
            ss{"OMST"}
        }
        "meta:Oral"{
            sd{"ORAST"}
            ss{"ORAT"}
        }
        "meta:Pakistan"{
            sd{"PKST"}
            ss{"PKT"}
        }
        "meta:Palau"{
            ss{"PWT"}
        }
        "meta:Papua_New_Guinea"{
            ss{"PGT"}
        }
        "meta:Paraguay"{
            sd{"PYST"}
            ss{"PYT"}
        }
        "meta:Peru"{
            sd{"PEST"}
            ss{"PET"}
        }
        "meta:Philippines"{
            sd{"PHST"}
            ss{"PHT"}
        }
        "meta:Phoenix_Islands"{
            ss{"PHOT"}
        }
        "meta:Pierre_Miquelon"{
            sd{"PMDT"}
            ss{"PMST"}
        }
        "meta:Pitcairn"{
            ss{"PST"}
            parseRegions{"PN"}
        }
        "meta:Ponape"{
            ss{"PONT"}
        }
        "meta:Pyongyang"{
            ss{"KST"}
            parseRegions{"KP"}
        }
        "meta:Qyzylorda"{
            sd{"QYZST"}
            ss{"QYZT"}
        }
        "meta:Reunion"{
            ss{"RET"}
        }
        "meta:Rothera"{
            ss{"ROTT"}
        }
        "meta:Sakhalin"{
            sd{"SAKST"}
            ss{"SAKT"}
        }
        "meta:Samara"{
            sd{"SAMST"}
            ss{"SAMT"}
        }
        "meta:Samarkand"{
            sd{"SAMST"}
            ss{"SAMT"}
            parseRegions{"UZ"}
        }
        "meta:Samoa"{
            ss{"SST"}
        }
        "meta:Seychelles"{
            ss{"SCT"}
        }
        "meta:Shevchenko"{
            sd{"SHEST"}
            ss{"SHET"}
        }
        "meta:Singapore"{
            ss{"SGT"}
        }
        "meta:Solomon"{
            ss{"SBT"}
        }
        "meta:South_Georgia"{
            ss{"GST"}
            parseRegions{"GS"}
        }
        "meta:Suriname"{
            ss{"SRT"}
        }
        "meta:Syowa"{
            ss{"SYOT"}
        }
        "meta:Tahiti"{
            ss{"TAHT"}
        }
        "meta:Taipei"{
            sd{"CDT"}
            ss{"CST"}
            parseRegions{"TW"}
        }
        "meta:Tajikistan"{
            ss{"TJT"}
        }
        "meta:Tashkent"{
            sd{"TASST"}
            ss{"TAST"}
        }
        "meta:Tbilisi"{
            sd{"TBIST"}
            ss{"TBIT"}
        }
        "meta:Tokelau"{
            ss{"TKT"}
        }
        "meta:Tonga"{
            sd{"TOST"}
            ss{"TOT"}
        }
        "meta:Truk"{
            ss{"CHUT"}
        }
        "meta:Turkey"{
            sd{"TRST"}
            ss{"TRT"}
        }
        "meta:Turkmenistan"{
            sd{"TMST"}
            ss{"TMT"}
        }
        "meta:Tuvalu"{
            ss{"TVT"}
        }
        "meta:Uralsk"{
            sd{"URAST"}
            ss{"URAT"}
        }
        "meta:Uruguay"{
            sd{"UYST"}
            ss{"UYT"}
        }
        "meta:Urumqi"{
            ss{"XJT"}
        }
        "meta:Uzbekistan"{
            sd{"UZST"}
            ss{"UZT"}
        }
        "meta:Vanuatu"{
            sd{"VUST"}
            ss{"VUT"}
        }
        "meta:Venezuela"{
            ss{"VET"}
        }
        "meta:Vladivostok"{
            sd{"VLAST"}
            ss{"VLAT"}
        }
        "meta:Volgograd"{
            sd{"VOLST"}
            ss{"VOLT"}
        }
        "meta:Vostok"{
            ss{"VOST"}
        }
        "meta:Wake"{
            ss{"WAKT"}
        }
        "meta:Wallis"{
            ss{"WFT"}
        }
        "meta:Yakutsk"{
            sd{"YAKST"}
            ss{"YAKT"}
        }
        "meta:Yekaterinburg"{
            sd{"YEKST"}
            ss{"YEKT"}
        }
        "meta:Yerevan"{
            sd{"YERST"}
            ss{"YERT"}
        }
        "meta:Yukon"{
            sd{"YDT"}
            ss{"YST"}
        }
    }
}
