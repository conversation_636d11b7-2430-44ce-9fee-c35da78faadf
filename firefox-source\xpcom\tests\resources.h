/* -*- Mode: C++; tab-width: 2; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */
#ifndef resources_h___
#define resources_h___

#define TIMER_1SECOND 40000
#define TIMER_5SECOND 40001
#define TIMER_10SECOND 40002

#define TIMER_1REPEAT 40003
#define TIMER_5REPEAT 40004
#define TIMER_10REPEAT 40005

#define TIMER_CANCEL 40006
#define TIMER_EXIT 40010

#endif /* resources_h___ */
