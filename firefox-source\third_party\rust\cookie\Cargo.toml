# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "cookie"
version = "0.16.2"
authors = [
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
]
build = "build.rs"
description = """
HTTP cookie parsing and cookie jar management. Supports signed and private
(encrypted, authenticated) jars.
"""
documentation = "https://docs.rs/cookie"
readme = "README.md"
license = "MIT OR Apache-2.0"
repository = "https://github.com/SergioBenitez/cookie-rs"

[package.metadata.docs.rs]
all-features = true

[dependencies.aes-gcm]
version = "0.10.0"
optional = true

[dependencies.base64]
version = "0.20"
optional = true

[dependencies.hkdf]
version = "0.12.0"
optional = true

[dependencies.hmac]
version = "0.12.0"
optional = true

[dependencies.percent-encoding]
version = "2.0"
optional = true

[dependencies.rand]
version = "0.8"
optional = true

[dependencies.sha2]
version = "0.10.0"
optional = true

[dependencies.subtle]
version = "2.3"
optional = true

[dependencies.time]
version = "0.3"
features = [
    "std",
    "parsing",
    "formatting",
    "macros",
]
default-features = false

[build-dependencies.version_check]
version = "0.9.4"

[features]
key-expansion = [
    "sha2",
    "hkdf",
]
percent-encode = ["percent-encoding"]
private = [
    "aes-gcm",
    "base64",
    "rand",
    "subtle",
]
secure = [
    "private",
    "signed",
    "key-expansion",
]
signed = [
    "hmac",
    "sha2",
    "base64",
    "rand",
    "subtle",
]
