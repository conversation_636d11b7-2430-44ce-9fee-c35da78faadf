# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
name = "clubcard-crlite"
version = "0.3.0"
authors = ["<PERSON> <<EMAIL>>"]
build = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "An instantiation of Clubcard for use in CRLite"
readme = false
license = "MPL-2.0"
repository = "https://github.com/mozilla/clubcard-crlite/"

[lib]
name = "clubcard_crlite"
path = "src/lib.rs"

[[example]]
name = "inspect"
path = "examples/inspect.rs"

[[example]]
name = "query"
path = "examples/query.rs"

[dependencies.base64]
version = "0.21"

[dependencies.bincode]
version = "1.3"

[dependencies.clubcard]
version = "0.3"

[dependencies.rand]
version = "0.8"
optional = true

[dependencies.serde]
version = "1.0"
features = ["derive"]

[dependencies.serde_json]
version = "1"
optional = true

[dependencies.sha2]
version = "0.10"

[dev-dependencies.rand]
version = "0.8"

[dev-dependencies.sha2]
version = "0.10"

[dev-dependencies.x509-parser]
version = "0.16"
features = ["verify"]

[features]
builder = [
    "dep:rand",
    "dep:serde_json",
    "clubcard/builder",
]
