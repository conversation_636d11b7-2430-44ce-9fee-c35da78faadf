# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

# Adding a new metric? We have docs for that!
# https://firefox-source-docs.mozilla.org/toolkit/components/glean/user/new_definitions_file.html

---
$schema: moz://mozilla.org/schemas/glean/metrics/2-0-0
$tags:
  - "Core :: XPCOM"

timer_thread:
  timers_fired_per_wakeup:
    type: custom_distribution
    description: >
      How many timers were processed in a single wake-up of the Timer Thread.
    range_min: 0
    range_max: 80
    bucket_count: 20
    histogram_type: exponential
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1814718
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1814718
    data_sensitivity:
      - technical
    notification_emails:
      - <EMAIL>
    expires: never

memory_watcher:
  on_high_memory_stats:
    type: event
    description: >
      This event is recorded when the memory situation is no longer low.
      The "stats" object consists of three numbers comma-delimited: 1) how
      many times a tab was unloaded 2) how many memory-pressure events
      were dispatched 3) how long we were in the low-memory situation in
      seconds
      This event was generated to correspond to the Legacy Telemetry event
      memory_watcher.on_high_memory#stats.
    bugs:
      - https://bugzil.la/1715858
    data_reviews:
      - https://bugzil.la/1715858
    notification_emails:
      - <EMAIL>
    expires: never
    extra_keys:
      value:
        description: >
          The `value` of the event. Mirrors to the Legacy Telemetry
          event's `value` parameter.
          The stringified, comma-separated stats.
        type: string
    telemetry_mirror: Memory_watcher_OnHighMemory_Stats

memory_phc:
  slop:
    type: memory_distribution
    description: >
      Over-allocation due to PHC's rounding (aka internal
      fragmentation).  Measured in bytes.

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram MEMORY_PHC_SLOP.
    memory_unit: byte
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1829127
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1892149
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1969177
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1829127
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1892149
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: 152
    telemetry_mirror: MEMORY_PHC_SLOP

  slots_allocated:
    type: custom_distribution
    description: >
      Number of PHC slots currently allocated

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram MEMORY_PHC_SLOTS_ALLOCATED.
    range_min: 1
    range_max: 16384
    bucket_count: 64
    histogram_type: exponential
    unit: PHC slots
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1829127
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1892149
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1969177
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1829127
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1892149
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: 152
    telemetry_mirror: MEMORY_PHC_SLOTS_ALLOCATED

  slots_freed:
    type: custom_distribution
    description: >
      Number of PHC slots allocated-then-freed

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram MEMORY_PHC_SLOTS_FREED.
    range_min: 1
    range_max: 16384
    bucket_count: 64
    histogram_type: exponential
    unit: PHC slots
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1829127
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1892149
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1969177
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1829127
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1892149
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: 152
    telemetry_mirror: MEMORY_PHC_SLOTS_FREED

cycle_collector:
  time:
    type: timing_distribution
    description: >
      Time spent on one cycle collection (ms)

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram CYCLE_COLLECTOR.
    time_unit: millisecond
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: CYCLE_COLLECTOR

  worker_time:
    type: timing_distribution
    description: >
      Time spent on one cycle collection in a worker (ms)

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram CYCLE_COLLECTOR_WORKER.
    time_unit: millisecond
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: CYCLE_COLLECTOR_WORKER

  visited_ref_counted:
    type: custom_distribution
    description: >
      Number of ref counted objects visited by the cycle collector

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram CYCLE_COLLECTOR_VISITED_REF_COUNTED.
    range_min: 1
    range_max: 300000
    bucket_count: 50
    histogram_type: exponential
    unit: ref counted objects
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: CYCLE_COLLECTOR_VISITED_REF_COUNTED

  worker_visited_ref_counted:
    type: custom_distribution
    description: >
      Number of ref counted objects visited by the cycle collector in a worker

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram CYCLE_COLLECTOR_WORKER_VISITED_REF_COUNTED.
    range_min: 1
    range_max: 300000
    bucket_count: 50
    histogram_type: exponential
    unit: ref counted objects
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: CYCLE_COLLECTOR_WORKER_VISITED_REF_COUNTED

  visited_gced:
    type: custom_distribution
    description: >
      Number of JS objects visited by the cycle collector

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram CYCLE_COLLECTOR_VISITED_GCED.
    range_min: 1
    range_max: 300000
    bucket_count: 50
    histogram_type: exponential
    unit: JS objects
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: CYCLE_COLLECTOR_VISITED_GCED

  worker_visited_gced:
    type: custom_distribution
    description: >
      Number of JS objects visited by the cycle collector in a worker

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram CYCLE_COLLECTOR_WORKER_VISITED_GCED.
    range_min: 1
    range_max: 300000
    bucket_count: 50
    histogram_type: exponential
    unit: JS objects
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: CYCLE_COLLECTOR_WORKER_VISITED_GCED

  collected:
    type: custom_distribution
    description: >
      Number of objects collected by the cycle collector

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram CYCLE_COLLECTOR_COLLECTED.
    range_min: 1
    range_max: 100000
    bucket_count: 50
    histogram_type: exponential
    unit: JS objects
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: CYCLE_COLLECTOR_COLLECTED

  worker_collected:
    type: custom_distribution
    description: >
      Number of objects collected by the cycle collector in a worker

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram CYCLE_COLLECTOR_WORKER_COLLECTED.
    range_min: 1
    range_max: 100000
    bucket_count: 50
    histogram_type: exponential
    unit: JS objects
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: CYCLE_COLLECTOR_WORKER_COLLECTED

  need_gc:
    type: labeled_counter
    description: >
      Needed garbage collection before cycle collection.

      This metric was generated to correspond to the Legacy Telemetry boolean
      histogram CYCLE_COLLECTOR_NEED_GC.
    labels:
      - "false"
      - "true"
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: h#CYCLE_COLLECTOR_NEED_GC

  worker_need_gc:
    type: labeled_counter
    description: >
      Needed garbage collection before cycle collection in a worker.

      This metric was generated to correspond to the Legacy Telemetry boolean
      histogram CYCLE_COLLECTOR_WORKER_NEED_GC.
    labels:
      - "false"
      - "true"
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: h#CYCLE_COLLECTOR_WORKER_NEED_GC

  full:
    type: timing_distribution
    description: >
      Full pause time for one cycle collection, including preparation (ms)

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram CYCLE_COLLECTOR_FULL.
    time_unit: millisecond
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: CYCLE_COLLECTOR_FULL

  max_pause:
    type: timing_distribution
    description: >
      Longest pause for an individual slice of one cycle collection, including
      preparation (ms)

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram CYCLE_COLLECTOR_MAX_PAUSE.
    time_unit: millisecond
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1364503
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1364503
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: CYCLE_COLLECTOR_MAX_PAUSE

  finish_igc:
    type: labeled_counter
    description: >
      Cycle collection finished an incremental GC

      This metric was generated to correspond to the Legacy Telemetry boolean
      histogram CYCLE_COLLECTOR_FINISH_IGC.
    labels:
      - "false"
      - "true"
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: h#CYCLE_COLLECTOR_FINISH_IGC

  sync_skippable:
    type: labeled_counter
    description: >
      Cycle collection synchronously ran forget skippable

      This metric was generated to correspond to the Legacy Telemetry boolean
      histogram CYCLE_COLLECTOR_SYNC_SKIPPABLE.
    labels:
      - "false"
      - "true"
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: h#CYCLE_COLLECTOR_SYNC_SKIPPABLE

  time_between:
    type: timing_distribution
    description: >
      Time spent in between cycle collections (seconds)

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram CYCLE_COLLECTOR_TIME_BETWEEN.
    time_unit: second
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: CYCLE_COLLECTOR_TIME_BETWEEN

  slice_during_idle:
    type: custom_distribution
    description: >
      Percent of cycle collector slice done during idle time

      This metric was generated to correspond to the Legacy Telemetry linear
      histogram CYCLE_COLLECTOR_SLICE_DURING_IDLE.
    range_min: 1
    range_max: 100
    bucket_count: 50
    histogram_type: linear
    unit: percent
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1372042
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1372042
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: CYCLE_COLLECTOR_SLICE_DURING_IDLE

  async_snow_white_freeing:
    type: timing_distribution
    description: >
      Time spent on one asynchronous SnowWhite freeing (ms)

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram CYCLE_COLLECTOR_ASYNC_SNOW_WHITE_FREEING.
    time_unit: millisecond
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: CYCLE_COLLECTOR_ASYNC_SNOW_WHITE_FREEING

  deferred_finalize_async:
    type: timing_distribution
    description: >
      Pause time for asynchronous deferred finalization (ms)

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram DEFERRED_FINALIZE_ASYNC.
    time_unit: millisecond
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1935420
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: DEFERRED_FINALIZE_ASYNC

  forget_skippable_max:
    type: timing_distribution
    description: >
      Max time spent on one forget skippable (ms)

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram FORGET_SKIPPABLE_MAX.
    time_unit: millisecond
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1956726
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1956726
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: FORGET_SKIPPABLE_MAX

memory:
  resident_fast:
    type: memory_distribution
    description: >
      Resident memory size (KB)

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram MEMORY_RESIDENT_FAST.
    memory_unit: kilobyte
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1226196
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1870550
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1226196
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1870550
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: MEMORY_RESIDENT_FAST

  resident_peak:
    type: memory_distribution
    description: >
      Peak resident memory size (KB)

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram MEMORY_RESIDENT_PEAK.
    memory_unit: kilobyte
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1551648
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1870550
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1551648
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1870550
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: MEMORY_RESIDENT_PEAK

  total:
    type: memory_distribution
    description: >
      Total Memory Across All Processes (KB) (inaccurate WRT shared memory. See
      MemoryTelemetry.cpp)

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram MEMORY_TOTAL.
    memory_unit: kilobyte
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1198209
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1511918
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1198209
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1511918
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: MEMORY_TOTAL

  distribution_among_content:
    type: labeled_custom_distribution
    description: >
      Absolute difference of each content process' USS and the mean of USS's,
      normalized by the mean, in percentage. It will be recorded with the rest
      of the memory probes when gatherMemory is called, if at least 2 content
      processes are alive. Example: in case of 4 content processes with USS's:
      1G, 500MB, 1G, 1.5G, the reported numbers will be: 0, 50, 0, 50. Which
      indicates that 2 processes used 50% more or 50% less memory than the
      avarage and 2 used exactly as much as the avarage.

      This metric was generated to correspond to the Legacy Telemetry linear
      histogram MEMORY_DISTRIBUTION_AMONG_CONTENT.
    range_min: 1
    range_max: 200
    bucket_count: 100
    histogram_type: linear
    unit: percentage
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1344174
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1344174
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: MEMORY_DISTRIBUTION_AMONG_CONTENT

  unique:
    type: memory_distribution
    description: >
      Unique Set Size (KB)

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram MEMORY_UNIQUE.
    memory_unit: kilobyte
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1198209
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1870550
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1198209
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1870550
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: MEMORY_UNIQUE

  vsize:
    type: memory_distribution
    description: >
      Virtual memory size (KB)

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram MEMORY_VSIZE.
    memory_unit: kilobyte
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: MEMORY_VSIZE

  vsize_max_contiguous:
    type: memory_distribution
    description: >
      Maximum-sized block of contiguous virtual memory (KB)

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram MEMORY_VSIZE_MAX_CONTIGUOUS.
    memory_unit: kilobyte
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: MEMORY_VSIZE_MAX_CONTIGUOUS

  js_compartments_system:
    type: custom_distribution
    description: >
      Total JavaScript compartments used for add-ons and internals.

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram MEMORY_JS_COMPARTMENTS_SYSTEM.
    range_min: 1
    range_max: 1000
    bucket_count: 50
    histogram_type: exponential
    unit: compartments
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: MEMORY_JS_COMPARTMENTS_SYSTEM

  js_compartments_user:
    type: custom_distribution
    description: >
      Total JavaScript compartments used for web pages

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram MEMORY_JS_COMPARTMENTS_USER.
    range_min: 1
    range_max: 1000
    bucket_count: 50
    histogram_type: exponential
    unit: compartments
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: MEMORY_JS_COMPARTMENTS_USER

  js_realms_system:
    type: custom_distribution
    description: >
      Total JavaScript realms used for add-ons and internals.

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram MEMORY_JS_REALMS_SYSTEM.
    range_min: 1
    range_max: 1000
    bucket_count: 50
    histogram_type: exponential
    unit: realms
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1518077
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1518077
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: MEMORY_JS_REALMS_SYSTEM

  js_realms_user:
    type: custom_distribution
    description: >
      Total JavaScript realms used for web pages.

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram MEMORY_JS_REALMS_USER.
    range_min: 1
    range_max: 1000
    bucket_count: 50
    histogram_type: exponential
    unit: realms
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1518077
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1518077
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: MEMORY_JS_REALMS_USER

  js_gc_heap:
    type: memory_distribution
    description: >
      Memory used by the garbage-collected JavaScript heap (KB)

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram MEMORY_JS_GC_HEAP.
    memory_unit: kilobyte
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: MEMORY_JS_GC_HEAP

  storage_sqlite:
    type: memory_distribution
    description: >
      Memory used by SQLite (KB)

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram MEMORY_STORAGE_SQLITE.
    memory_unit: kilobyte
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: MEMORY_STORAGE_SQLITE

  images_content_used_uncompressed:
    type: memory_distribution
    description: >
      Memory used for uncompressed, in-use content images (KB)

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram MEMORY_IMAGES_CONTENT_USED_UNCOMPRESSED.
    memory_unit: kilobyte
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: MEMORY_IMAGES_CONTENT_USED_UNCOMPRESSED

  heap_allocated:
    type: memory_distribution
    description: >
      Heap memory allocated (KB)

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram MEMORY_HEAP_ALLOCATED.
    memory_unit: kilobyte
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: MEMORY_HEAP_ALLOCATED

  heap_overhead_fraction:
    type: custom_distribution
    description: >
      Fraction of committed heap memory that is overhead (percentage).

      This metric was generated to correspond to the Legacy Telemetry linear
      histogram MEMORY_HEAP_OVERHEAD_FRACTION.
    range_min: 1
    range_max: 100
    bucket_count: 25
    histogram_type: linear
    unit: percentage
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1252375
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1252375
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: MEMORY_HEAP_OVERHEAD_FRACTION

  ghost_windows:
    type: custom_distribution
    description: >
      Number of ghost windows

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram GHOST_WINDOWS.
    range_min: 1
    range_max: 128
    bucket_count: 32
    histogram_type: exponential
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: GHOST_WINDOWS

  low_memory_events_physical:
    type: custom_distribution
    description: >
      Number of low-physical-memory events fired since last ping

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram LOW_MEMORY_EVENTS_PHYSICAL.
    range_min: 1
    range_max: 1024
    bucket_count: 21
    histogram_type: exponential
    unit: events
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=711490
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1451005
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=711490
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1451005
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: LOW_MEMORY_EVENTS_PHYSICAL

  page_faults_hard:
    type: custom_distribution
    description: >
      Hard page faults (since last telemetry ping)

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram PAGE_FAULTS_HARD.
    range_min: 8
    range_max: 65536
    bucket_count: 13
    histogram_type: exponential
    unit: page faults
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: PAGE_FAULTS_HARD

  collection_time:
    type: timing_distribution
    description: >
      Time spent gathering memory telemetry in milliseconds

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram MEMORY_COLLECTION_TIME.
    time_unit: millisecond
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1786864
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1786864
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: MEMORY_COLLECTION_TIME

  free_purged_pages:
    type: timing_distribution
    description: >
      Time(ms) to purge dirty heap pages.

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram MEMORY_FREE_PURGED_PAGES_MS.
    time_unit: millisecond
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950710
    notification_emails:
      - <EMAIL>
    expires: never
    telemetry_mirror: MEMORY_FREE_PURGED_PAGES_MS

  unique_content_startup:
    type: memory_distribution
    description: >
      Unique Set Size of Content Process at Startup (KB)

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram MEMORY_UNIQUE_CONTENT_STARTUP.
    memory_unit: kilobyte
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1494827
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1494827
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: MEMORY_UNIQUE_CONTENT_STARTUP

event:
  longtask:
    type: labeled_timing_distribution
    description: >
      LongTask events for parent process (keys: event name): time the event ran
      in ms

      This metric was generated to correspond to the Legacy Telemetry
      exponential histogram EVENT_LONGTASK.
    time_unit: millisecond
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=12345678
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=12345678
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: never
    telemetry_mirror: EVENT_LONGTASK

xpcom:
  abi:
    type: string
    lifetime: application
    description: |
      A string tag identifying the binary ABI of the current processor and
      compiler vtable. This is taken from the TARGET_XPCOM_ABI configure
      variable. It may not be available on all platforms, especially
      unusual processor or compiler combinations.

      The result takes the form <processor>-<compilerABI>, for example:
        - x86-msvc
        - ppc-gcc3

      This value should almost always be used in combination with the
      operating system.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950386
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950386
    notification_emails:
      - <EMAIL>
    data_sensitivity:
      - technical
    expires: never
    send_in_pings:
      - metrics
      - update

system.cpu:
  name:
    type: string
    lifetime: application
    description: >
      The user readable CPU name.
      (e.g. "Intel(R) Core(TM) i9-8950HK CPU @ 2.90GHz")
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1797587
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1797587
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - metrics
      - update

  vendor:
    type: string
    lifetime: application
    description: >
      The CPU vendor (e.g. "GenuineIntel").
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1128472
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1128472
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - metrics
      - update

  logical_cores:
    type: quantity
    lifetime: application
    description: >
      Logical CPU cores.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1122052
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1122052
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    notification_emails:
      - <EMAIL>
    expires: never
    unit: core
    send_in_pings:
      - metrics
      - update

  physical_cores:
    type: quantity
    lifetime: application
    description: >
      Physical CPU cores.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1128472
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1128472
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    notification_emails:
      - <EMAIL>
    expires: never
    unit: core
    send_in_pings:
      - metrics
      - update

  big_cores:
    type: quantity
    lifetime: application
    description: >
      Big (or Performance) CPU cores.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1926292
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1926292
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    notification_emails:
      - <EMAIL>
    expires: never
    unit: core
    send_in_pings:
      - metrics
      - update

  medium_cores:
    type: quantity
    lifetime: application
    description: >
      Medium CPU cores.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1926292
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1926292
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    notification_emails:
      - <EMAIL>
    expires: never
    unit: core
    send_in_pings:
      - metrics
      - update

  little_cores:
    type: quantity
    lifetime: application
    description: >
      Little (or Efficient) CPU cores.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1926292
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1926292
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    notification_emails:
      - <EMAIL>
    expires: never
    unit: core
    send_in_pings:
      - metrics
      - update

  family:
    type: quantity
    lifetime: application
    description: >
      CPU family.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1128472
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1128472
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    notification_emails:
      - <EMAIL>
    expires: never
    unit: cpu family number
    send_in_pings:
      - metrics
      - update

  model:
    type: quantity
    lifetime: application
    description: >
      CPU model.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1128472
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1128472
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    notification_emails:
      - <EMAIL>
    expires: never
    unit: cpu model number
    send_in_pings:
      - metrics
      - update

  stepping:
    type: quantity
    lifetime: application
    description: >
      CPU stepping.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1128472
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1128472
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    notification_emails:
      - <EMAIL>
    expires: never
    unit: cpu stepping number
    send_in_pings:
      - metrics
      - update

  l2_cache:
    type: quantity
    lifetime: application
    description: >
      L2 cache size (only on Windows and Mac).
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1128472
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1128472
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    notification_emails:
      - <EMAIL>
    expires: never
    unit: kilobyte
    send_in_pings:
      - metrics
      - update

  l3_cache:
    type: quantity
    lifetime: application
    description: >
      L3 cache size.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1128472
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1128472
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    notification_emails:
      - <EMAIL>
    expires: never
    unit: kilobyte
    send_in_pings:
      - metrics
      - update

  speed:
    type: quantity
    lifetime: application
    description: >
      CPU speed in MHz.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1128472
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1128472
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950406
    notification_emails:
      - <EMAIL>
    expires: never
    unit: megahertz
    send_in_pings:
      - metrics
      - update

  extensions:
    type: string_list
    lifetime: application
    description: >
      Recognized CPU extensions.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1962547
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1962547
    data_sensitivity:
      - technical
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - metrics
      - update

hdd:
  profile: &hdd_object
    type: object
    lifetime: application
    description: |
      Information about the disk the profile is stored on.
      Windows only.
      * model - The disk's self-reported model string
      * revision - The disk's self-reported revision string
      * diskType - Either "HDD" or "SSD"
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950410
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950410
    data_sensitivity:
      - technical
    structure:
      type: object
      properties:
        model:
          type: string
        revision:
          type: string
        diskType:
          type: string
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - metrics
      - update

  binary:
    <<: *hdd_object
    description: |
      Information about the disk the application binary is stored on.
      Windows only.
      * model - The disk's self-reported model string
      * revision - The disk's self-reported revision string
      * diskType - Either "HDD" or "SSD"

  system:
    <<: *hdd_object
    description: |
      Information about the disk the system files are stored on.
      Windows only.
      * model - The disk's self-reported model string
      * revision - The disk's self-reported revision string
      * diskType - Either "HDD" or "SSD"

system.os:
  name:
    type: string
    lifetime: application
    description: |
      The Operating System's name, according to sysinfo.
      e.g. "Windows_NT"
      Expected in most cases to be identical to `client_info.os`.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950409
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950409
    data_sensitivity:
      - technical
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - metrics
      - sync
      - update
      - third-party-modules

  version:
    type: string
    lifetime: application
    description: |
      The Operating System's version, according to sysinfo.
      e.g. "6.3"
      Expected in most cases to be identical to `client_info.os_version`.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950409
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950409
    data_sensitivity:
      - technical
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - metrics
      - sync
      - update
      - third-party-modules

  locale:
    type: string
    lifetime: application
    description: |
      The Operating System's local, according to ospreferences.
      e.g. "en-US"
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950409
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950409
    data_sensitivity:
      - technical
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - metrics
      - sync
      - update
      - third-party-modules

  distro:
    type: string
    lifetime: application
    description: |
      The Operating System's distribution, according to sysinfo.
      Linux only.
      e.g. "Linuxmint"
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950409
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950409
    data_sensitivity:
      - technical
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - metrics
      - sync
      - update
      - third-party-modules

  distro_version:
    type: string
    lifetime: application
    description: |
      The Operating System's distribution's version, according to sysinfo.
      Linux only.
      e.g. "22"
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950409
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950409
    data_sensitivity:
      - technical
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - metrics
      - sync
      - update
      - third-party-modules

  service_pack_major:
    type: quantity
    unit: version
    lifetime: application
    description: |
      The Windows service pack's major version, according to WindowsVersionInfo.
      Windows only.
      e.g. 0
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950409
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950409
    data_sensitivity:
      - technical
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - metrics
      - sync
      - update
      - third-party-modules

  service_pack_minor:
    type: quantity
    unit: version
    lifetime: application
    description: |
      The Windows service pack's minor version, according to WindowsVersionInfo.
      Windows only.
      e.g. 0
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950409
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950409
    data_sensitivity:
      - technical
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - metrics
      - sync
      - update
      - third-party-modules

  windows_build_number:
    type: quantity
    unit: build number
    lifetime: application
    description: |
      The Windows build number, according to WindowsVersionInfo.
      Windows only.
      e.g. 26100
      Expected in most cases to be identical to `client_info.windows_build_number`.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950409
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950409
    data_sensitivity:
      - technical
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - metrics
      - sync
      - update
      - third-party-modules

  windows_ubr:
    type: quantity
    unit: build number
    lifetime: application
    description: |
      The Windows update build revision number, according to
      `SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion`.
      Windows only.
      e.g. 3775
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950409
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950409
    data_sensitivity:
      - technical
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - metrics
      - sync
      - update
      - third-party-modules

system:
  memory:
    type: quantity
    unit: MB
    lifetime: application
    description: |
      The physical memory size in MB (2^20 bytes).
      e.g. 32211
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950403
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950403
    data_sensitivity:
      - technical
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - metrics
      - update

  virtual_memory:
    type: quantity
    unit: MB
    lifetime: application
    description: |
      The size of the user-mode portion of the virtual address space of the parent process.
      Windows only.
      e.g. 134217728
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950403
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950403
    data_sensitivity:
      - technical
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - metrics
      - update

  is_wow_64:
    type: boolean
    lifetime: application
    description: |
      Whether we're in Windows32-on-Windows64 mode.
      Windows only.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950403
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950403
    data_sensitivity:
      - technical
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - metrics
      - update
      - third-party-modules

  is_wow_arm_64:
    type: boolean
    lifetime: application
    description: |
      Whether we're in Windows32-on-WindowsForArm64 mode.
      Windows only.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950403
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950403
    data_sensitivity:
      - technical
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - metrics
      - update
      - third-party-modules

  has_win_package_id:
    type: boolean
    lifetime: application
    description: |
      Whether the app has a package identity.
      Windows only.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950403
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950403
    data_sensitivity:
      - technical
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - metrics
      - update

  win_package_family_name:
    type: string
    lifetime: application
    description: |
      The full application package name without any of the components that might change
      during the life cycle of the app (such as the version number or the architecture).
      Uniquely identifies the application within one Windows installation.
      Windows only.
      Only included if it begins with "Mozilla." or "MozillaCorporation.".
      e.g. "Mozilla.Firefox_n80bbvh6b1yt2"
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950403
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950403
    data_sensitivity:
      - technical
    notification_emails:
      - <EMAIL>
    expires: never
    send_in_pings:
      - metrics
      - update

  apple_model_id:
    type: string
    lifetime: application
    description: |
      The `hw.model` of the hardware running this software.
      Mac only.
      e.g. "MacBookPro14,1"
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950403
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1950403
    data_sensitivity:
      - technical
    notification_emails:
      - <EMAIL>
    expires: never

  special_directory_appdata_fallback:
    type: labeled_boolean
    lifetime: ping
    description: |
      Success status of using SpecialSystemDirectory's fallback path to retrieve
      either %AppData% or %LocalAppData%.
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1974357
    data_reviews:
      - https://phabricator.services.mozilla.com/D255241
    notification_emails:
      - <EMAIL>
    expires: 145
    labels:
      - appdata
      - localappdata
