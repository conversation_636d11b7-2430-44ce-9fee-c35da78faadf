# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.78.0"
name = "cargo_metadata"
version = "0.19.2"
authors = ["<PERSON> <<EMAIL>>"]
build = false
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "structured access to the output of `cargo metadata`"
readme = "README.md"
license = "MIT"
repository = "https://github.com/oli-obk/cargo_metadata"

[package.metadata.cargo_metadata_test]
other_field = "foo"
some_field = true

[features]
builder = ["derive_builder"]
default = []
unstable = []

[lib]
name = "cargo_metadata"
path = "src/lib.rs"

[[test]]
name = "selftest"
path = "tests/selftest.rs"

[[test]]
name = "test_samples"
path = "tests/test_samples.rs"

[dependencies.camino]
version = "1.0.7"
features = ["serde1"]

[dependencies.cargo-platform]
version = "0.1.2"

[dependencies.derive_builder]
version = "0.20"
optional = true

[dependencies.semver]
version = "1.0.7"
features = ["serde"]

[dependencies.serde]
version = "1.0.136"
features = ["derive"]

[dependencies.serde_json]
version = "1.0.118"
features = ["unbounded_depth"]

[dependencies.thiserror]
version = "2.0.3"
