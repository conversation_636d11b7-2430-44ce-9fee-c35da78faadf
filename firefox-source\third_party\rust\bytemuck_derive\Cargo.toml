# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
rust-version = "1.61"
name = "bytemuck_derive"
version = "1.9.3"
authors = ["Lokathor <<EMAIL>>"]
build = false
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "derive proc-macros for `bytemuck`"
readme = "README.md"
keywords = [
    "transmute",
    "bytes",
    "casting",
]
categories = [
    "encoding",
    "no-std",
]
license = "Zlib OR Apache-2.0 OR MIT"
repository = "https://github.com/Lokathor/bytemuck"

[lib]
name = "bytemuck_derive"
path = "src/lib.rs"
proc-macro = true

[[test]]
name = "basic"
path = "tests/basic.rs"

[dependencies.proc-macro2]
version = "1.0.60"

[dependencies.quote]
version = "1"

[dependencies.syn]
version = "2.0.1"

[dev-dependencies]
