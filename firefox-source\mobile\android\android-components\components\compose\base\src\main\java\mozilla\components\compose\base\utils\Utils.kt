/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

package mozilla.components.compose.base.utils

import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalInspectionMode

/**
 * Indicates whether the app is currently running in a Composable @Preview.
 */
val inComposePreview: <PERSON><PERSON>an
    @Composable
    get() = LocalInspectionMode.current
