# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2015"
name = "bit-vec"
version = "0.8.0"
authors = ["<PERSON> <<EMAIL>>"]
build = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "A vector of bits"
homepage = "https://github.com/contain-rs/bit-vec"
documentation = "https://docs.rs/bit-vec/"
readme = "README.md"
keywords = [
    "data-structures",
    "bitvec",
    "bitmask",
    "bitmap",
    "bit",
]
license = "Apache-2.0 OR MIT"
repository = "https://github.com/contain-rs/bit-vec"

[package.metadata.docs.rs]
features = [
    "borsh",
    "serde",
    "miniserde",
    "nanoserde",
]

[lib]
name = "bit_vec"
path = "src/lib.rs"

[[bench]]
name = "bench"
path = "benches/bench.rs"

[dependencies.borsh]
version = "1.5"
features = ["derive"]
optional = true
default-features = false

[dependencies.miniserde]
version = "0.1"
optional = true

[dependencies.nanoserde]
version = "0.1"
optional = true

[dependencies.serde]
version = "1.0"
features = ["derive"]
optional = true
default-features = false

[dev-dependencies.rand]
version = "0.8"

[dev-dependencies.rand_xorshift]
version = "0.3"

[dev-dependencies.serde_json]
version = "1.0"

[features]
borsh_std = ["borsh/std"]
default = ["std"]
serde_no_std = ["serde/alloc"]
serde_std = [
    "std",
    "serde/std",
]
std = []
