# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.82"
name = "ixdtf"
version = "0.5.0"
authors = ["The ICU4X Project Developers"]
build = false
include = [
    "data/**/*",
    "src/**/*",
    "examples/**/*",
    "benches/**/*",
    "tests/**/*",
    "Cargo.toml",
    "LICENSE",
    "README.md",
    "build.rs",
]
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "Parser for Internet eXtended DateTime Format"
homepage = "https://icu4x.unicode.org"
readme = "README.md"
categories = ["internationalization"]
license = "Unicode-3.0"
repository = "https://github.com/unicode-org/icu4x"

[package.metadata.docs.rs]
all-features = true

[package.metadata.workspaces]
independent = true

[features]
default = ["duration"]
duration = []

[lib]
name = "ixdtf"
path = "src/lib.rs"

[dependencies.displaydoc]
version = "0.2.3"
default-features = false

[dev-dependencies.serde-json-core]
version = "0.6.0"
features = ["std"]
default-features = false

[target.'cfg(not(target_arch = "wasm32"))'.dev-dependencies.criterion]
version = "0.5.0"
