# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

Classes = [
    {
        'cid': '{35c66fd1-95e9-4e0a-80c5-c3bd2b375481}',
        'contract_ids': ['@mozilla.org/array;1'],
        'legacy_constructor': 'nsArrayBase::XPCOMConstructor',
        'headers': ['nsArray.h'],
    },
    {
        'name': 'Observer',
        'js_name': 'obs',
        'cid': '{d07f5195-e3d1-11d2-8acd-00105a1b8860}',
        'contract_ids': ['@mozilla.org/observer-service;1'],
        'interfaces': ['nsIObserverService'],
        'legacy_constructor': 'nsObserverService::Create',
        'headers': ['/xpcom/ds/nsObserverService.h'],
        'processes': ProcessSelector.ALLOW_IN_GPU_RDD_VR_SOCKET_UTILITY_AND_GMPLUGIN_PROCESS,
    },
]
