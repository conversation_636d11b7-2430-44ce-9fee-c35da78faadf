# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "chardetng_c"
version = "0.1.2"
authors = ["<PERSON> <<EMAIL>>"]
build = false
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "C bindings for chardetng"
homepage = "https://docs.rs/chardetng_c/"
documentation = "https://docs.rs/chardetng_c/"
readme = "README.md"
keywords = [
    "encoding",
    "web",
    "unicode",
    "charset",
]
categories = [
    "text-processing",
    "encoding",
    "web-programming",
    "internationalization",
]
license = "Apache-2.0 OR MIT"
repository = "https://github.com/hsivonen/chardetng-c"

[lib]
name = "chardetng_c"
path = "src/lib.rs"

[dependencies]
chardetng = "0.1.1"
encoding_rs = "0.8.17"
