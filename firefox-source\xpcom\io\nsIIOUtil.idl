/* -*- Mode: C++; tab-width: 2; indent-tabs-mode: nil; c-basic-offset: 4 -*- */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#include "nsISupports.idl"

interface nsIInputStream;
interface nsIOutputStream;

/**
 * nsIIOUtil provdes various xpcom/io-related utility methods.
 */
[scriptable, builtinclass, uuid(e8152f7f-4209-4c63-ad23-c3d2aa0c5a49)]
interface nsIIOUtil : nsISupports
{
  /**
   * Test whether an input stream is buffered.  See nsStreamUtils.h
   * documentation for NS_InputStreamIsBuffered for the definition of
   * "buffered" used here and for edge-case behavior.
   *
   * @throws NS_ERROR_INVALID_POINTER if null is passed in.
   */
  boolean inputStreamIsBuffered(in nsIInputStream aStream);

  /**
   * Test whether an output stream is buffered.  See nsStreamUtils.h
   * documentation for NS_OutputStreamIsBuffered for the definition of
   * "buffered" used here and for edge-case behavior.
   *
   * @throws NS_ERROR_INVALID_POINTER if null is passed in.
   */
  boolean outputStreamIsBuffered(in nsIOutputStream aStream);
};
