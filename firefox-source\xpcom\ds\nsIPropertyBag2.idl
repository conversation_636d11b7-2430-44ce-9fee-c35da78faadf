/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

/* nsIVariant based Property Bag support. */

#include "nsIPropertyBag.idl"

[scriptable, uuid(625cfd1e-da1e-4417-9ee9-dbc8e0b3fd79)]
interface nsIPropertyBag2 : nsIPropertyBag
{
  // Accessing a property as a different type may attempt conversion to the
  // requested value

  int32_t     getPropertyAsInt32       (in AString prop);
  uint32_t    getPropertyAsUint32      (in AString prop);
  int64_t     getPropertyAsInt64       (in AString prop);
  uint64_t    getPropertyAsUint64      (in AString prop);
  double      getPropertyAsDouble      (in AString prop);
  AString     getPropertyAsAString     (in AString prop);
  ACString    getPropertyAsACString    (in AString prop);
  AUTF8String getPropertyAsAUTF8String (in AString prop);
  boolean     getPropertyAsBool        (in AString prop);

  /**
   * This method returns null if the value exists, but is null.
   *
   * Note: C++ callers should not use this method. They should use the
   * typesafe `do_GetProperty` wrapper instead.
   */
  void        getPropertyAsInterface   (in AString prop,
                                        in nsIIDRef iid,
                                        [iid_is(iid), retval] out nsQIResult result);

  /**
   * This method returns null if the value does not exist,
   * or exists but is null.
   */
  nsIVariant  get                      (in AString prop);

  /**
   * Check for the existence of a key.
   */
  boolean     hasKey                   (in AString prop);
};


%{C++
#include "nsCOMPtr.h"
#include "nsAString.h"

class MOZ_STACK_CLASS nsGetProperty final : public nsCOMPtr_helper {
 public:
  nsGetProperty(nsIPropertyBag2* aPropBag, const nsAString& aPropName, nsresult* aError)
      : mPropBag(aPropBag), mPropName(aPropName), mErrorPtr(aError) {}

  virtual nsresult NS_FASTCALL operator()(const nsIID&, void**) const override;

 private:
  nsIPropertyBag2* MOZ_NON_OWNING_REF mPropBag;
  const nsAString& mPropName;
  nsresult* mErrorPtr;
};

/**
 * A typesafe wrapper around nsIPropertyBag2::GetPropertyAsInterface. Similar
 * to the `do_QueryInterface` family of functions, when assigned to a
 * `nsCOMPtr` of a given type, attempts to query the given property to that
 * type.
 *
 * If `aError` is passed, the return value of `GetPropertyAsInterface` is
 * stored in it.
 */
inline const nsGetProperty do_GetProperty(nsIPropertyBag2* aPropBag,
                                          const nsAString& aPropName,
                                          nsresult* aError = 0) {
  return nsGetProperty(aPropBag, aPropName, aError);
}

%}
