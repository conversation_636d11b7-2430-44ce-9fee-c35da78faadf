/* This Source Code Form is subject to the terms of the Mozilla Public
License, v. 2.0. If a copy of the MPL was not distributed with this
* file, You can obtain one at http://mozilla.org/MPL/2.0/. */

/// The error returned by err_if_interrupted.
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct Interrupted;

impl std::fmt::Display for Interrupted {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.write_str("The operation was interrupted")
    }
}

impl std::error::Error for Interrupted {}
