[package]
name = "block"
version = "0.1.6"
authors = ["<PERSON>"]

description = "Rust interface for Apple's C language extension of blocks."
keywords = ["blocks", "osx", "ios", "objective-c"]
readme = "README.md"
repository = "http://github.com/SSheldon/rust-block"
documentation = "http://ssheldon.github.io/rust-objc/block/"
license = "MIT"

exclude = [
  ".gitignore",
  ".travis.yml",
  "travis_install.sh",
  "travis_test.sh",
  "tests-ios/**",
]

[dev-dependencies.objc_test_utils]
version = "0.0"
path = "test_utils"
