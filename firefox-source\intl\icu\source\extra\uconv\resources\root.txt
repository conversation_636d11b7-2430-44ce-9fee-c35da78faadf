﻿// -*- Coding: utf-8; -*-  [all uconv resource files]
// Copyright (C) 2016 and later: Unicode, Inc. and others.
// License & terms of use: http://www.unicode.org/copyright.html
//
// Copyright (c) 2000-2004 IBM, Inc. and Others.
//
// Root translation file for uconv messages.
// So you want to translate this file??? Great!
// 1. copy it to a new name [ex: se.txt]
//
// 2. You might wish to comment out ALL lines, and then uncomment them
//    as you add translations. That way, you don't inadvertently mark
//    an untranslated English (or whatever) string as already
//    translated. The base translation might change!
// 
// 3. These files are in UTF-8 format (even though root uses only
//    ASCII)
//
// 4. Make note of the location of {0}, {1}, etc.. they are taken from
//    arguments to u_wmsg() in order..
//
// 5. Add se.txt to RESSRC= in resfiles.mk and to the project file on 
//    the Windows side.
//
// 6. Send it <NAME_EMAIL> or ask on the ICU mailing list! thanks!

root
{
  // uconv errors

  lcUsageWord { "usage" }
  ucUsageWord { "Usage" }
  usage {
    "{0}: {1} "
    "[ -h, -?, --help ] [ -V, --version ] [ -s, --silent ] [ -v, --verbose ] "
    "[ -l, --list | --list-code code | --default-code | -L, --list-transliterators ] "
    "[ --canon ] [ -x transliteration ] "
    "[ --to-callback callback | -c ] [ --from-callback callback | -i ] [ --callback callback ] "
    "[ --fallback | --no-fallback ] "
    "[ -b, --block-size size ] "
    "[ -f, --from-code code ] [ -t, --to-code code ] "
    "[ --add-signature ] [ --remove-signature ] "
    "[ -o, --output file ] "
    "[ file ... ]\n" }

  help {  "Options:  -h, --help                    print this message\n"
          "          -V, --version                 print the program version\n"
          "          -s, --silent                  suppress messages\n"
          "          -v, --verbose                 display progress information\n"
          "          -l, --list                    list all available encodings\n"
          "          --list-code code              list only the given encoding\n"
          "          --default-code                list only the default encoding\n"
          "          -L, --list-transliterators    list all available transliterators\n"
          "          --canon                       print list in cnvrtrs.txt(5) format\n"
          "          -x transliteration            run everything through transliteration\n" 
          "          --to-callback callback        use callback on destination encoding\n"
          "          -c                            omit invalid characters from the output\n"
          "          --from-callback callback      use callback on original encoding\n"
          "          -i                            ignore invalid sequences in the input\n"
          "          --callback callback           use callback on both encodings\n"
          "          -b, --block-size size         read size bytes blocks (default: 4096)\n"
          "          --fallback                    use fallback mapping\n"
          "          --no-fallback                 do not use fallback mapping\n"
          "          -f, --from-code code          set the original encoding\n"
          "          -t, --to-code code            set the destination encoding\n" 
          "          --add-signature               add a U+FEFF Unicode signature character (BOM)\n"
          "          --remove-signature            remove a U+FEFF Unicode signature character (BOM)\n"
          "          -o, --output file             write output to file\n"
          "\n"
          "Callbacks:"
  }

  cantGetNames   { "Couldn''t get available converter names.\n" } // 0: err
  cantGetTag   { "Couldn''t get standard tag name: {0}.\n" } // 0: err
  
  noSuchCodeset  { "Couldn''t find encoding: {0}.\n" } // 0: name of the encoding
  noFromCodeset  { "No original encoding set (use -f).\n" }
  noToCodeset    {  "No destination encoding set (use -t).\n" }

  badBlockSize  { "Bad block size: {0}.\n" } // 0: size of the block

  cantSetInBinMode { "Couldn't set standard input to binary mode." }
  cantSetOutBinMode { "Couldn't set standard output to binary mode." }

  cantOpenFromCodeset { "Couldn''t open converter for original encoding {0}: {1}.\n" } // 0:set, 1: err
  cantOpenToCodeset { "Couldn''t open converter for destination encoding {0}: {1}.\n" } // 0: set, 1: err

  cantCreateTranslit { "Couldn''t create transliteration \"{0}\": {1}.\n" } // 0: set, 1: err
  cantCreateTranslitParseErr { "Couldn''t create transliteration \"{0}\": {1}, line {2}, offset {3}.\n" } // 0: set, 1: err, 2: line, 3: offset

  cantSetCallback { "Couldn''t set transcoding callback: {0}.\n" } // 0: err

  unknownCallback { "Unknown callback: {0}.\n" } // 0: callback name

  cantOpenInputF  { "Couldn''t open input file {0}: {1}.\n" } // 0: file, 1: strerror [OS error string]
  cantCreateOutputF  { "Couldn''t create output file {0}: {1}.\n" } // 0: file, 1: strerror [OS error string]

  cantWrite       { "The converted text couldn't be written: {0}.\n" } // 0: OS error string
  cantRead        { "Error reading from input file: {0}.\n" } // 0: OS error string

  problemCvtToU   { "Conversion to Unicode from codepage failed at input byte position {0}. Bytes: {1} Error: {2}\n" } // 0: position, 1: bytes, 2: err
  problemCvtFromU { "Conversion from Unicode to codepage failed at input byte position {0}. Unicode: {1} Error: {2}\n"} // 0: position, 1: Unicode, 2: err
  problemCvtFromUOut { "Conversion from Unicode to codepage failed at output byte position {0}. Unicode: {1} Error: {2}\n"} // 0: position, 1: Unicode, 2: err

// ICU errors - used by u_wmsg_errorName()

  U_USING_FALLBACK_ERROR       { "Using fallback data" }
  U_USING_DEFAULT_ERROR        { "Using default data" }
  U_ZERO_ERROR                 { "No error has occurred" }
  U_ILLEGAL_ARGUMENT_ERROR     { "Illegal argument" }
  U_MISSING_RESOURCE_ERROR     { "A resource was missing" }
  U_INVALID_FORMAT_ERROR       { "Invalid format" }
  U_FILE_ACCESS_ERROR          { "Problem accessing that file/object" }
  U_INTERNAL_PROGRAM_ERROR     { "Internal program error" }
  U_MESSAGE_PARSE_ERROR        { "Parse error on message format" }
  U_MEMORY_ALLOCATION_ERROR    { "Out of memory" }
  U_INDEX_OUTOFBOUNDS_ERROR    { "An index was out-of-bounds" }
  U_PARSE_ERROR                { "Parse error" }
  U_INVALID_CHAR_FOUND         { "Invalid character found" }
  U_TRUNCATED_CHAR_FOUND       { "Truncated character found" }
  U_ILLEGAL_CHAR_FOUND         { "Illegal character found" }
  U_INVALID_TABLE_FORMAT       { "Invalid table data format" }
  U_INVALID_TABLE_FILE         { "Invalid table data file" }
  U_BUFFER_OVERFLOW_ERROR      { "Buffer overflow" }
  U_UNSUPPORTED_ERROR          { "A feature was unsupported" }
  U_RESOURCE_TYPE_MISMATCH     { "Resource type mismatch" }
  U_ILLEGAL_ESCAPE_SEQUENCE    { "Illegal escape sequence found" }
  U_UNSUPPORTED_ESCAPE_SEQUENCE {"Unsupported escape sequence found" }
}

