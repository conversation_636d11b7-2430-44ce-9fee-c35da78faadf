# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g. crates.io) dependencies
#
# If you believe there's an error in this file please file an
# issue against the rust-lang/cargo repository. If you're
# editing this file be aware that the upstream Cargo.toml
# will likely look very different (and much more reasonable)

[package]
name = "ident_case"
version = "1.0.1"
authors = ["<PERSON> <<EMAIL>>"]
description = "Utility for applying case rules to Rust identifiers."
documentation = "https://docs.rs/ident_case/1.0.1"
readme = "README.md"
license = "MIT/Apache-2.0"
repository = "https://github.com/TedDriggs/ident_case"
[badges.travis-ci]
repository = "TedDriggs/ident_case"
