# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "jexl-eval"
version = "0.3.0"
authors = [
    "<PERSON> <<EMAIL>>",
    "The Sync Team <<EMAIL>>",
    "The Glean Team <<EMAIL>>",
]
build = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "A JEXL evaluator written in Rust"
readme = false
license = "MPL-2.0"
repository = "https://github.com/mozilla/jexl-rs"

[lib]
name = "jexl_eval"
path = "src/lib.rs"

[dependencies.anyhow]
version = "1"

[dependencies.jexl-parser]
version = "^0.3.0"

[dependencies.serde]
version = "1"

[dependencies.serde_json]
version = "1"

[dependencies.thiserror]
version = "1"
