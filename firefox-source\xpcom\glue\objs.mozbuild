# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

xpcom_glue_src_lcppsrcs = [
    "FileUtils.cpp",
    "MemUtils.cpp",
    "XREAppData.cpp",
]

xpcom_glue_src_cppsrcs = ["/xpcom/glue/%s" % s for s in xpcom_glue_src_lcppsrcs]

xpcom_gluens_src_lcppsrcs = []

xpcom_gluens_src_cppsrcs = ["/xpcom/glue/%s" % s for s in xpcom_gluens_src_lcppsrcs]
