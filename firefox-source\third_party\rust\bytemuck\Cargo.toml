# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "bytemuck"
version = "1.22.0"
authors = ["Lokathor <<EMAIL>>"]
build = false
exclude = ["/pedantic.bat"]
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "A crate for mucking around with piles of bytes."
readme = "README.md"
keywords = [
    "transmute",
    "bytes",
    "casting",
]
categories = [
    "encoding",
    "no-std",
]
license = "Zlib OR Apache-2.0 OR MIT"
repository = "https://github.com/Lokathor/bytemuck"

[package.metadata.docs.rs]
features = [
    "nightly_docs",
    "latest_stable_rust",
    "extern_crate_alloc",
    "extern_crate_std",
]

[package.metadata.playground]
features = [
    "latest_stable_rust",
    "extern_crate_alloc",
    "extern_crate_std",
]

[features]
aarch64_simd = []
align_offset = []
alloc_uninit = []
avx512_simd = []
const_zeroed = []
derive = ["bytemuck_derive"]
extern_crate_alloc = []
extern_crate_std = ["extern_crate_alloc"]
latest_stable_rust = [
    "aarch64_simd",
    "avx512_simd",
    "align_offset",
    "alloc_uninit",
    "const_zeroed",
    "derive",
    "min_const_generics",
    "must_cast",
    "must_cast_extra",
    "pod_saturating",
    "track_caller",
    "transparentwrapper_extra",
    "wasm_simd",
    "zeroable_atomics",
    "zeroable_maybe_uninit",
]
min_const_generics = []
must_cast = []
must_cast_extra = ["must_cast"]
nightly_docs = []
nightly_float = []
nightly_portable_simd = []
nightly_stdsimd = []
pod_saturating = []
track_caller = []
transparentwrapper_extra = []
unsound_ptr_pod_impl = []
wasm_simd = []
zeroable_atomics = []
zeroable_maybe_uninit = []

[lib]
name = "bytemuck"
path = "src/lib.rs"

[[test]]
name = "array_tests"
path = "tests/array_tests.rs"

[[test]]
name = "cast_slice_tests"
path = "tests/cast_slice_tests.rs"

[[test]]
name = "checked_tests"
path = "tests/checked_tests.rs"

[[test]]
name = "derive"
path = "tests/derive.rs"

[[test]]
name = "doc_tests"
path = "tests/doc_tests.rs"

[[test]]
name = "offset_of_tests"
path = "tests/offset_of_tests.rs"

[[test]]
name = "std_tests"
path = "tests/std_tests.rs"

[[test]]
name = "transparent"
path = "tests/transparent.rs"

[[test]]
name = "wrapper_forgets"
path = "tests/wrapper_forgets.rs"

[dependencies.bytemuck_derive]
version = "1.4.1"
optional = true

[lints.rust.unexpected_cfgs]
level = "deny"
priority = 0
check-cfg = ['cfg(target_arch, values("spirv"))']
