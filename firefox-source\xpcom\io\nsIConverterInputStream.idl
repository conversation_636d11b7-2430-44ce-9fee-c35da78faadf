/* -*- Mode: C++; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 4 -*- */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#include "nsIUnicharInputStream.idl"

interface nsIInputStream;

/**
 * A unichar input stream that wraps an input stream.
 * This allows reading unicode strings from a stream, automatically converting
 * the bytes from a selected character encoding.
 */
[scriptable, builtinclass, uuid(FC66FFB6-5404-4908-A4A3-27F92FA0579D)]
interface nsIConverterInputStream : nsIUnicharInputStream {
    /**
     * Default replacement char value, U+FFFD REPLACEMENT CHARACTER.
     */
    const char16_t DEFAULT_REPLACEMENT_CHARACTER = 0xFFFD;

    /**
     * Special replacement character value that requests errors to
     * be treated as fatal.
     */
    const char16_t ERRORS_ARE_FATAL = 0;

    /**
     * Initialize this stream.
     * @param aStream
     *        The underlying stream to read from.
     * @param aCharset
     *        The character encoding to use for converting the bytes of the
     *        stream. A null charset will be interpreted as UTF-8.
     * @param aBufferSize
     *        How many bytes to buffer.
     * @param aReplacementChar
     *        The character to replace unknown byte sequences in the stream
     *        with. The standard replacement character is U+FFFD.
     *        A value of 0x0000 will cause an exception to be thrown if unknown
     *        byte sequences are encountered in the stream.
     */
    void init (in nsIInputStream aStream, in string aCharset,
               in long aBufferSize, in char16_t aReplacementChar);
};
