== screen-animations.html screen-animations-ref.html
!= screen-animations.html screen-animations-notref.html
== animate-opacity.html animate-opacity-ref.html
== animate-preserves3d.html animate-preserves3d-ref.html
== animation-initially-out-of-view-with-delay.html animation-initially-out-of-view-with-delay-ref.html
== animation-on-empty-height-frame.html about:blank
== in-visibility-hidden-animation.html in-visibility-hidden-animation-ref.html
== in-visibility-hidden-animation-pseudo-element.html in-visibility-hidden-animation-pseudo-element-ref.html
pref(layout.css.marker.restricted,false) == in-visibility-hidden-animation-marker-pseudo-element.html in-visibility-hidden-animation-marker-pseudo-element-ref.html
== partially-out-of-view-animation.html partially-out-of-view-animation-ref.html
fails-if(useDrawSnapshot) == animate-display-table-opacity.html animate-display-table-opacity-ref.html
# We need to run 100% opacity test case when OMTA is disabled to check that the animation creates a stacking context even if the animation is not running on the compositor
test-pref(layers.offmainthreadcomposition.async-animations,false) == stacking-context-opacity-1-animation.html stacking-context-animation-ref.html
# We need to run transform:none test case when OMTA is disabled to check that the animation creates a stacking context even if the animation is not running on the compositor
test-pref(layers.offmainthreadcomposition.async-animations,false) == stacking-context-transform-none-animation.html stacking-context-animation-ref.html
== no-stacking-context-opacity-removing-animation-in-delay.html no-stacking-context-animation-ref.html
== no-stacking-context-transform-removing-animation-in-delay.html no-stacking-context-animation-ref.html
== stacking-context-lose-opacity-1.html stacking-context-animation-ref.html
== stacking-context-lose-transform-none.html stacking-context-animation-ref.html
== stacking-context-opacity-win-in-delay.html stacking-context-animation-ref.html
== stacking-context-opacity-win-in-delay-on-main-thread.html stacking-context-animation-ref.html
== stacking-context-opacity-wins-over-transition.html stacking-context-animation-ref.html
== stacking-context-transform-win-in-delay.html stacking-context-animation-ref.html
== stacking-context-transform-win-in-delay-on-main-thread.html stacking-context-animation-ref.html
== stacking-context-transform-wins-over-transition.html stacking-context-animation-ref.html
== stacking-context-opacity-1-animation.html stacking-context-animation-ref.html
== stacking-context-opacity-1-on-table.html stacking-context-animation-ref.html
== stacking-context-opacity-1-with-fill-backwards.html stacking-context-animation-ref.html
== stacking-context-opacity-1-with-fill-forwards.html stacking-context-animation-ref.html
== stacking-context-paused-on-opacity-1.html stacking-context-animation-ref.html
== stacking-context-paused-on-transform-none.html stacking-context-animation-ref.html
== stacking-context-transform-none-animation.html stacking-context-animation-ref.html
== stacking-context-transform-none-animation-on-svg.html  stacking-context-animation-ref.html
== stacking-context-transform-none-animation-with-backface-visibility.html stacking-context-animation-ref.html
== stacking-context-transform-none-animation-with-preserve-3d.html stacking-context-animation-ref.html
== stacking-context-transform-none-with-fill-backwards.html stacking-context-animation-ref.html
== stacking-context-transform-none-with-fill-forwards.html stacking-context-animation-ref.html
== stacking-context-opacity-1-in-delay.html stacking-context-animation-ref.html
== stacking-context-opacity-removing-important-in-delay.html stacking-context-animation-ref.html
== stacking-context-transform-none-in-delay.html stacking-context-animation-ref.html
== stacking-context-transform-removing-important-in-delay.html stacking-context-animation-ref.html
== no-stacking-context-offset-distance-animation-with-offset-path-none.html no-stacking-context-animation-ref.html
== stacking-context-offset-path-none-animation.html stacking-context-animation-ref.html
== stacking-context-offset-path-none-in-delay.html stacking-context-animation-ref.html
== stacking-context-offset-path-none-with-fill-backwards.html stacking-context-animation-ref.html
== stacking-context-offset-path-none-with-fill-forwards.html stacking-context-animation-ref.html
== background-position-in-delay.html background-position-ref.html # This test fails the reftest-opaque-layer check since animating background-position currently creates an active layer from its delay phse, and reftest-opaque-layer only handles items assigned to PaintedLayers.
== background-position-after-finish.html background-position-ref.html
random-if(useDrawSnapshot) == background-position-running.html background-position-ref.html # This test fails the reftest-opaque-layer check since animating background-position currently creates an active layer, and reftest-opaque-layer only handles items assigned to PaintedLayers.
== background-position-important.html background-position-ref.html # This test fails the reftest-opaque-layer check since animating background-position overridden by a non-animated !important style also creates an active layer, and reftest-opaque-layer only handles items that are assigned to PaintedLayers.

== mask-position-after-finish-1a.html mask-anim-ref.html
== mask-position-after-finish-1b.html mask-anim-ref.html
== mask-position-in-delay-1a.html mask-anim-ref.html
== mask-position-in-delay-1b.html mask-anim-ref.html
== mask-size-after-finish-1a.html mask-anim-ref.html
== mask-size-after-finish-1b.html mask-anim-ref.html
== mask-size-in-delay-1a.html mask-anim-ref.html
== mask-size-in-delay-1b.html mask-anim-ref.html

== stop-animation-on-discarded-pseudo-element.html about:blank

== updating-animation-on-pseudo-element.html updating-animation-on-pseudo-element-ref.html
pref(layout.css.marker.restricted,false) == updating-animation-on-marker-pseudo-element.html updating-animation-on-marker-pseudo-element-ref.html
== content-on-pseudo-element-at-beginning.html content-on-pseudo-element-ref.html
== content-on-pseudo-element-at-half.html content-on-pseudo-element-ref.html
pref(layout.css.marker.restricted,false) == content-on-marker-pseudo-element-at-beginning.html content-on-marker-pseudo-element-at-beginning-ref.html
pref(layout.css.marker.restricted,false) == content-on-marker-pseudo-element-at-half.html content-on-marker-pseudo-element-at-beginning-ref.html
== reframe-and-animation-starts-at-the-same-time.html reframe-and-animation-starts-at-the-same-time-ref.html
pref(layout.css.marker.restricted,false) == marker-reframe-and-animation-starts-at-the-same-time.html marker-reframe-and-animation-starts-at-the-same-time-ref.html
== change-animation-name-to-none-in-rule.html change-animation-name-in-rule-ref.html
== change-animation-name-to-other-in-rule.html change-animation-name-in-rule-ref.html
== change-animation-name-to-non-existent-in-rule.html change-animation-name-in-rule-ref.html
== no-style-sharing-with-animations.html no-style-sharing-with-animations-ref.html

== continuation-opacity.html continuation-opacity-ref.html
== ib-split-sibling-opacity.html about:blank

== opacity-animation-in-fixed-opacity-parent.html opacity-animation-in-fixed-opacity-parent-ref.html
== opacity-animation-in-delay.html about:blank
== transform-animation-in-delay.html transform-animation-in-delay-ref.html
== containing-block-on-visibility-hidden.html containing-block-on-visibility-hidden-ref.html
== background-color.html background-color-ref.html
== background-color-on-html.html background-color-on-html-ref.html
skip-if(Android) == replace-with-new-positive-delay-animation.html replace-with-new-positive-delay-animation-ref.html # bug 1787682 for Android

pref(layout.css.scroll-driven-animations.enabled,true) skip-if(useDrawSnapshot) == scroll-timeline-in-delay-omta.html scroll-timeline-in-delay-omta-ref.html
