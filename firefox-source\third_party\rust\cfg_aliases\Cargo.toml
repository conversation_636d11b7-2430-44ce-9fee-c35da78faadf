# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "cfg_aliases"
version = "0.2.1"
authors = ["Zicklag <<EMAIL>>"]
exclude = [
    "modoc.config",
    "release.toml",
]
description = "A tiny utility to help save you a lot of effort with long winded `#[cfg()]` checks."
homepage = "https://github.com/katharostech/cfg_aliases"
documentation = "https://docs.rs/cfg_aliases"
readme = "README.md"
keywords = [
    "cfg",
    "alias",
    "conditional",
    "compilation",
    "build",
]
categories = [
    "development-tools",
    "development-tools::build-utils",
]
license = "MIT"
repository = "https://github.com/katharostech/cfg_aliases"

[badges.maintenance]
status = "passively-maintained"

[lints.clippy]
str_to_string = "deny"
