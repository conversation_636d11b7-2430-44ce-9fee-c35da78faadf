/* -*- Mode: C++; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 4 -*- */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#include "nsIInputStream.idl"

interface nsIOutputStream;
interface nsIEventTarget;

/**
 * A nsIInputStreamTee is a wrapper for an input stream, that when read
 * reads the specified amount of data from its |source| and copies that
 * data to its |sink|.  |sink| must be a blocking output stream.
 */
[scriptable, builtinclass, uuid(90a9d790-3bca-421e-a73b-98f68e13c917)]
interface nsIInputStreamTee : nsIInputStream
{
    attribute nsIInputStream source;
    attribute nsIOutputStream sink;

    /**
     * If |eventTarget| is set, copying to sink is done asynchronously using
     * the event-target (e.g. a thread). If |eventTarget| is not set, copying
     * to sink happens synchronously while reading from the source.
     */
    attribute nsIEventTarget eventTarget;
};

%{C++
// factory methods
extern nsresult
NS_NewInputStreamTee(nsIInputStream **tee, // read from this input stream
                     nsIInputStream *source,
                     nsIOutputStream *sink);

extern nsresult
NS_NewInputStreamTeeAsync(nsIInputStream **tee, // read from this input stream
                     nsIInputStream *source,
                     nsIOutputStream *sink,
                     nsIEventTarget *eventTarget);
%}
