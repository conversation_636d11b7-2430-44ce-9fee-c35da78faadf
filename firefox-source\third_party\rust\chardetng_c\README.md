# chardetng_c

[![crates.io](https://meritbadge.herokuapp.com/chardetng_c)](https://crates.io/crates/chardetng_c)
[![docs.rs](https://docs.rs/chardetng_c/badge.svg)](https://docs.rs/chardetng_c/)
[![Apache 2 / MIT dual-licensed](https://img.shields.io/badge/license-Apache%202%20%2F%20MIT-blue.svg)](https://github.com/hsivonen/chardetng_c/blob/master/COPYRIGHT)

A C wrapper for [`chardetng`](https://crates.io/crates/chardetng).

## Licensing

Please see the file named
[COPYRIGHT](https://github.com/hsivonen/chardetng_c/blob/master/COPYRIGHT).

## Documentation

Generated [API documentation](https://docs.rs/chardetng_c/) is available
online.

## Release Notes

### 0.1.2

* Remove year from copyright notices.

### 0.1.1

* Add newline to the end of the C header.

### 0.1.0

* Initial release.
