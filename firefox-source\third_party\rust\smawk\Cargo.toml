# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
name = "smawk"
version = "0.3.2"
authors = ["<PERSON> <<EMAIL>>"]
exclude = [
    ".github/",
    ".gitignore",
    "benches/",
    "examples/",
]
description = "Functions for finding row-minima in a totally monotone matrix."
readme = "README.md"
keywords = [
    "smawk",
    "matrix",
    "optimization",
    "dynamic-programming",
]
categories = [
    "algorithms",
    "mathematics",
    "science",
]
license = "MIT"
repository = "https://github.com/mgeisler/smawk"

[dependencies.ndarray]
version = "0.15.4"
optional = true

[dev-dependencies.num-traits]
version = "0.2.14"

[dev-dependencies.rand]
version = "0.8.4"

[dev-dependencies.rand_chacha]
version = "0.3.1"

[dev-dependencies.version-sync]
version = "0.9.4"
