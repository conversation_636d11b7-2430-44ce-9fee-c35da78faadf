# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.82"
name = "icu_provider"
version = "2.0.0"
authors = ["The ICU4X Project Developers"]
build = false
include = [
    "data/**/*",
    "src/**/*",
    "examples/**/*",
    "benches/**/*",
    "tests/**/*",
    "Cargo.toml",
    "LICENSE",
    "README.md",
    "build.rs",
]
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "Trait and struct definitions for the ICU data provider"
homepage = "https://icu4x.unicode.org"
readme = "README.md"
categories = ["internationalization"]
license = "Unicode-3.0"
repository = "https://github.com/unicode-org/icu4x"

[package.metadata.cargo-all-features]
denylist = ["macros"]
max_combination_size = 3

[package.metadata.docs.rs]
all-features = true

[features]
alloc = [
    "icu_locale_core/alloc",
    "zerovec/alloc",
    "zerotrie/alloc",
]
baked = ["zerotrie"]
deserialize_bincode_1 = [
    "serde",
    "dep:bincode",
    "std",
]
deserialize_json = [
    "serde",
    "dep:serde_json",
]
deserialize_postcard_1 = [
    "serde",
    "dep:postcard",
]
export = [
    "serde",
    "dep:erased-serde",
    "dep:databake",
    "std",
    "sync",
    "dep:postcard",
    "zerovec/databake",
]
logging = ["dep:log"]
serde = [
    "dep:serde",
    "yoke/serde",
]
std = ["alloc"]
sync = []

[lib]
name = "icu_provider"
path = "src/lib.rs"
bench = false

[[bench]]
name = "data_locale_bench"
path = "benches/data_locale_bench.rs"
harness = false

[dependencies.bincode]
version = "1.3.1"
optional = true

[dependencies.databake]
version = "0.2.0"
features = ["derive"]
optional = true
default-features = false

[dependencies.displaydoc]
version = "0.2.3"
default-features = false

[dependencies.erased-serde]
version = "0.4.0"
features = ["alloc"]
optional = true

[dependencies.icu_locale_core]
version = "2.0.0"
default-features = false

[dependencies.log]
version = "0.4.17"
optional = true
default-features = false

[dependencies.postcard]
version = "1.0.3"
optional = true
default-features = false

[dependencies.serde]
version = "1.0.110"
features = [
    "derive",
    "alloc",
]
optional = true
default-features = false

[dependencies.serde_json]
version = "1.0.45"
optional = true

[dependencies.stable_deref_trait]
version = "1.2.0"
default-features = false

[dependencies.tinystr]
version = "0.8.0"
default-features = false

[dependencies.writeable]
version = "0.6.0"
default-features = false

[dependencies.yoke]
version = "0.8.0"
features = [
    "alloc",
    "derive",
]
default-features = false

[dependencies.zerofrom]
version = "0.1.3"
features = [
    "alloc",
    "derive",
]
default-features = false

[dependencies.zerotrie]
version = "0.2.0"
optional = true
default-features = false

[dependencies.zerovec]
version = "0.11.1"
features = ["derive"]
default-features = false

[dev-dependencies.serde_json]
version = "1.0.45"

[target.'cfg(not(target_arch = "wasm32"))'.dev-dependencies.criterion]
version = "0.5.0"
