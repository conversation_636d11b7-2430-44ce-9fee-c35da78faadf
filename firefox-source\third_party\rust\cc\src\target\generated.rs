//! This file is generated code. Please edit the generator
//! in dev-tools/gen-target-info if you need to make changes.

use super::TargetInfo;

pub(crate) const LIST: &[(&str, TargetInfo<'static>)] = &[
    (
        "aarch64-apple-darwin",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "apple",
            os: "macos",
            env: "",
            abi: "",
            llvm_target: "arm64-apple-macosx",
        },
    ),
    (
        "aarch64-apple-ios",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "apple",
            os: "ios",
            env: "",
            abi: "",
            llvm_target: "arm64-apple-ios",
        },
    ),
    (
        "aarch64-apple-ios-macabi",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "apple",
            os: "ios",
            env: "",
            abi: "macabi",
            llvm_target: "arm64-apple-ios-macabi",
        },
    ),
    (
        "aarch64-apple-ios-sim",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "apple",
            os: "ios",
            env: "",
            abi: "sim",
            llvm_target: "arm64-apple-ios-simulator",
        },
    ),
    (
        "aarch64-apple-tvos",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "apple",
            os: "tvos",
            env: "",
            abi: "",
            llvm_target: "arm64-apple-tvos",
        },
    ),
    (
        "aarch64-apple-tvos-sim",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "apple",
            os: "tvos",
            env: "",
            abi: "sim",
            llvm_target: "arm64-apple-tvos-simulator",
        },
    ),
    (
        "aarch64-apple-visionos",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "apple",
            os: "visionos",
            env: "",
            abi: "",
            llvm_target: "arm64-apple-xros",
        },
    ),
    (
        "aarch64-apple-visionos-sim",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "apple",
            os: "visionos",
            env: "",
            abi: "sim",
            llvm_target: "arm64-apple-xros-simulator",
        },
    ),
    (
        "aarch64-apple-watchos",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "apple",
            os: "watchos",
            env: "",
            abi: "",
            llvm_target: "arm64-apple-watchos",
        },
    ),
    (
        "aarch64-apple-watchos-sim",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "apple",
            os: "watchos",
            env: "",
            abi: "sim",
            llvm_target: "arm64-apple-watchos-simulator",
        },
    ),
    (
        "aarch64-fuchsia",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "fuchsia",
            env: "",
            abi: "",
            llvm_target: "aarch64-fuchsia",
        },
    ),
    (
        "aarch64-kmc-solid_asp3",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "kmc",
            os: "solid_asp3",
            env: "",
            abi: "",
            llvm_target: "aarch64-unknown-none",
        },
    ),
    (
        "aarch64-linux-android",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "android",
            env: "",
            abi: "",
            llvm_target: "aarch64-linux-android",
        },
    ),
    (
        "aarch64-nintendo-switch-freestanding",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "nintendo",
            os: "horizon",
            env: "",
            abi: "",
            llvm_target: "aarch64-unknown-none",
        },
    ),
    (
        "aarch64-pc-windows-gnullvm",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "pc",
            os: "windows",
            env: "gnu",
            abi: "llvm",
            llvm_target: "aarch64-pc-windows-gnu",
        },
    ),
    (
        "aarch64-pc-windows-msvc",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "pc",
            os: "windows",
            env: "msvc",
            abi: "",
            llvm_target: "aarch64-pc-windows-msvc",
        },
    ),
    (
        "aarch64-unknown-freebsd",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "freebsd",
            env: "",
            abi: "",
            llvm_target: "aarch64-unknown-freebsd",
        },
    ),
    (
        "aarch64-unknown-fuchsia",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "fuchsia",
            env: "",
            abi: "",
            llvm_target: "aarch64-unknown-fuchsia",
        },
    ),
    (
        "aarch64-unknown-hermit",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "hermit",
            env: "",
            abi: "",
            llvm_target: "aarch64-unknown-hermit",
        },
    ),
    (
        "aarch64-unknown-illumos",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "illumos",
            env: "",
            abi: "",
            llvm_target: "aarch64-unknown-solaris2.11",
        },
    ),
    (
        "aarch64-unknown-linux-gnu",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "",
            llvm_target: "aarch64-unknown-linux-gnu",
        },
    ),
    (
        "aarch64-unknown-linux-gnu_ilp32",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "ilp32",
            llvm_target: "aarch64-unknown-linux-gnu_ilp32",
        },
    ),
    (
        "aarch64-unknown-linux-musl",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "",
            llvm_target: "aarch64-unknown-linux-musl",
        },
    ),
    (
        "aarch64-unknown-linux-ohos",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "linux",
            env: "ohos",
            abi: "",
            llvm_target: "aarch64-unknown-linux-ohos",
        },
    ),
    (
        "aarch64-unknown-netbsd",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "netbsd",
            env: "",
            abi: "",
            llvm_target: "aarch64-unknown-netbsd",
        },
    ),
    (
        "aarch64-unknown-none",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "aarch64-unknown-none",
        },
    ),
    (
        "aarch64-unknown-none-softfloat",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "softfloat",
            llvm_target: "aarch64-unknown-none",
        },
    ),
    (
        "aarch64-unknown-nto-qnx700",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "nto",
            env: "nto70",
            abi: "",
            llvm_target: "aarch64-unknown-unknown",
        },
    ),
    (
        "aarch64-unknown-nto-qnx710",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "nto",
            env: "nto71",
            abi: "",
            llvm_target: "aarch64-unknown-unknown",
        },
    ),
    (
        "aarch64-unknown-nto-qnx710_iosock",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "nto",
            env: "nto71_iosock",
            abi: "",
            llvm_target: "aarch64-unknown-unknown",
        },
    ),
    (
        "aarch64-unknown-nto-qnx800",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "nto",
            env: "nto80",
            abi: "",
            llvm_target: "aarch64-unknown-unknown",
        },
    ),
    (
        "aarch64-unknown-nuttx",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "nuttx",
            env: "",
            abi: "",
            llvm_target: "aarch64-unknown-none",
        },
    ),
    (
        "aarch64-unknown-openbsd",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "openbsd",
            env: "",
            abi: "",
            llvm_target: "aarch64-unknown-openbsd",
        },
    ),
    (
        "aarch64-unknown-redox",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "redox",
            env: "relibc",
            abi: "",
            llvm_target: "aarch64-unknown-redox",
        },
    ),
    (
        "aarch64-unknown-teeos",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "teeos",
            env: "",
            abi: "",
            llvm_target: "aarch64-unknown-none",
        },
    ),
    (
        "aarch64-unknown-trusty",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "trusty",
            env: "",
            abi: "",
            llvm_target: "aarch64-unknown-unknown-musl",
        },
    ),
    (
        "aarch64-unknown-uefi",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "unknown",
            os: "uefi",
            env: "",
            abi: "",
            llvm_target: "aarch64-unknown-windows-gnu",
        },
    ),
    (
        "aarch64-uwp-windows-msvc",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "uwp",
            os: "windows",
            env: "msvc",
            abi: "uwp",
            llvm_target: "aarch64-pc-windows-msvc",
        },
    ),
    (
        "aarch64-wrs-vxworks",
        TargetInfo {
            full_arch: "aarch64",
            arch: "aarch64",
            vendor: "wrs",
            os: "vxworks",
            env: "gnu",
            abi: "",
            llvm_target: "aarch64-unknown-linux-gnu",
        },
    ),
    (
        "aarch64_be-unknown-linux-gnu",
        TargetInfo {
            full_arch: "aarch64_be",
            arch: "aarch64",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "",
            llvm_target: "aarch64_be-unknown-linux-gnu",
        },
    ),
    (
        "aarch64_be-unknown-linux-gnu_ilp32",
        TargetInfo {
            full_arch: "aarch64_be",
            arch: "aarch64",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "ilp32",
            llvm_target: "aarch64_be-unknown-linux-gnu_ilp32",
        },
    ),
    (
        "aarch64_be-unknown-netbsd",
        TargetInfo {
            full_arch: "aarch64_be",
            arch: "aarch64",
            vendor: "unknown",
            os: "netbsd",
            env: "",
            abi: "",
            llvm_target: "aarch64_be-unknown-netbsd",
        },
    ),
    (
        "arm-linux-androideabi",
        TargetInfo {
            full_arch: "arm",
            arch: "arm",
            vendor: "unknown",
            os: "android",
            env: "",
            abi: "eabi",
            llvm_target: "arm-linux-androideabi",
        },
    ),
    (
        "arm-unknown-linux-gnueabi",
        TargetInfo {
            full_arch: "arm",
            arch: "arm",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "eabi",
            llvm_target: "arm-unknown-linux-gnueabi",
        },
    ),
    (
        "arm-unknown-linux-gnueabihf",
        TargetInfo {
            full_arch: "arm",
            arch: "arm",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "eabihf",
            llvm_target: "arm-unknown-linux-gnueabihf",
        },
    ),
    (
        "arm-unknown-linux-musleabi",
        TargetInfo {
            full_arch: "arm",
            arch: "arm",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "eabi",
            llvm_target: "arm-unknown-linux-musleabi",
        },
    ),
    (
        "arm-unknown-linux-musleabihf",
        TargetInfo {
            full_arch: "arm",
            arch: "arm",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "eabihf",
            llvm_target: "arm-unknown-linux-musleabihf",
        },
    ),
    (
        "arm64_32-apple-watchos",
        TargetInfo {
            full_arch: "arm64_32",
            arch: "aarch64",
            vendor: "apple",
            os: "watchos",
            env: "",
            abi: "",
            llvm_target: "arm64_32-apple-watchos",
        },
    ),
    (
        "arm64e-apple-darwin",
        TargetInfo {
            full_arch: "arm64e",
            arch: "aarch64",
            vendor: "apple",
            os: "macos",
            env: "",
            abi: "",
            llvm_target: "arm64e-apple-macosx",
        },
    ),
    (
        "arm64e-apple-ios",
        TargetInfo {
            full_arch: "arm64e",
            arch: "aarch64",
            vendor: "apple",
            os: "ios",
            env: "",
            abi: "",
            llvm_target: "arm64e-apple-ios",
        },
    ),
    (
        "arm64e-apple-tvos",
        TargetInfo {
            full_arch: "arm64e",
            arch: "aarch64",
            vendor: "apple",
            os: "tvos",
            env: "",
            abi: "",
            llvm_target: "arm64e-apple-tvos",
        },
    ),
    (
        "arm64ec-pc-windows-msvc",
        TargetInfo {
            full_arch: "arm64ec",
            arch: "arm64ec",
            vendor: "pc",
            os: "windows",
            env: "msvc",
            abi: "",
            llvm_target: "arm64ec-pc-windows-msvc",
        },
    ),
    (
        "armeb-unknown-linux-gnueabi",
        TargetInfo {
            full_arch: "armeb",
            arch: "arm",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "eabi",
            llvm_target: "armeb-unknown-linux-gnueabi",
        },
    ),
    (
        "armebv7r-none-eabi",
        TargetInfo {
            full_arch: "armebv7r",
            arch: "arm",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "eabi",
            llvm_target: "armebv7r-none-eabi",
        },
    ),
    (
        "armebv7r-none-eabihf",
        TargetInfo {
            full_arch: "armebv7r",
            arch: "arm",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "eabihf",
            llvm_target: "armebv7r-none-eabihf",
        },
    ),
    (
        "armv4t-none-eabi",
        TargetInfo {
            full_arch: "armv4t",
            arch: "arm",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "eabi",
            llvm_target: "armv4t-none-eabi",
        },
    ),
    (
        "armv4t-unknown-linux-gnueabi",
        TargetInfo {
            full_arch: "armv4t",
            arch: "arm",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "eabi",
            llvm_target: "armv4t-unknown-linux-gnueabi",
        },
    ),
    (
        "armv5te-none-eabi",
        TargetInfo {
            full_arch: "armv5te",
            arch: "arm",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "eabi",
            llvm_target: "armv5te-none-eabi",
        },
    ),
    (
        "armv5te-unknown-linux-gnueabi",
        TargetInfo {
            full_arch: "armv5te",
            arch: "arm",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "eabi",
            llvm_target: "armv5te-unknown-linux-gnueabi",
        },
    ),
    (
        "armv5te-unknown-linux-musleabi",
        TargetInfo {
            full_arch: "armv5te",
            arch: "arm",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "eabi",
            llvm_target: "armv5te-unknown-linux-musleabi",
        },
    ),
    (
        "armv5te-unknown-linux-uclibceabi",
        TargetInfo {
            full_arch: "armv5te",
            arch: "arm",
            vendor: "unknown",
            os: "linux",
            env: "uclibc",
            abi: "eabi",
            llvm_target: "armv5te-unknown-linux-uclibcgnueabi",
        },
    ),
    (
        "armv6-unknown-freebsd",
        TargetInfo {
            full_arch: "armv6",
            arch: "arm",
            vendor: "unknown",
            os: "freebsd",
            env: "",
            abi: "eabihf",
            llvm_target: "armv6-unknown-freebsd-gnueabihf",
        },
    ),
    (
        "armv6-unknown-netbsd-eabihf",
        TargetInfo {
            full_arch: "armv6",
            arch: "arm",
            vendor: "unknown",
            os: "netbsd",
            env: "",
            abi: "eabihf",
            llvm_target: "armv6-unknown-netbsdelf-eabihf",
        },
    ),
    (
        "armv6k-nintendo-3ds",
        TargetInfo {
            full_arch: "armv6k",
            arch: "arm",
            vendor: "nintendo",
            os: "horizon",
            env: "newlib",
            abi: "eabihf",
            llvm_target: "armv6k-none-eabihf",
        },
    ),
    (
        "armv7-apple-ios",
        TargetInfo {
            full_arch: "armv7",
            arch: "arm",
            vendor: "apple",
            os: "ios",
            env: "",
            abi: "",
            llvm_target: "armv7-apple-ios",
        },
    ),
    (
        "armv7-linux-androideabi",
        TargetInfo {
            full_arch: "armv7",
            arch: "arm",
            vendor: "unknown",
            os: "android",
            env: "",
            abi: "eabi",
            llvm_target: "armv7-none-linux-android",
        },
    ),
    (
        "armv7-rtems-eabihf",
        TargetInfo {
            full_arch: "armv7",
            arch: "arm",
            vendor: "unknown",
            os: "rtems",
            env: "newlib",
            abi: "eabihf",
            llvm_target: "armv7-unknown-none-eabihf",
        },
    ),
    (
        "armv7-sony-vita-newlibeabihf",
        TargetInfo {
            full_arch: "armv7",
            arch: "arm",
            vendor: "sony",
            os: "vita",
            env: "newlib",
            abi: "eabihf",
            llvm_target: "thumbv7a-vita-eabihf",
        },
    ),
    (
        "armv7-unknown-freebsd",
        TargetInfo {
            full_arch: "armv7",
            arch: "arm",
            vendor: "unknown",
            os: "freebsd",
            env: "",
            abi: "eabihf",
            llvm_target: "armv7-unknown-freebsd-gnueabihf",
        },
    ),
    (
        "armv7-unknown-linux-gnueabi",
        TargetInfo {
            full_arch: "armv7",
            arch: "arm",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "eabi",
            llvm_target: "armv7-unknown-linux-gnueabi",
        },
    ),
    (
        "armv7-unknown-linux-gnueabihf",
        TargetInfo {
            full_arch: "armv7",
            arch: "arm",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "eabihf",
            llvm_target: "armv7-unknown-linux-gnueabihf",
        },
    ),
    (
        "armv7-unknown-linux-musleabi",
        TargetInfo {
            full_arch: "armv7",
            arch: "arm",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "eabi",
            llvm_target: "armv7-unknown-linux-musleabi",
        },
    ),
    (
        "armv7-unknown-linux-musleabihf",
        TargetInfo {
            full_arch: "armv7",
            arch: "arm",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "eabihf",
            llvm_target: "armv7-unknown-linux-musleabihf",
        },
    ),
    (
        "armv7-unknown-linux-ohos",
        TargetInfo {
            full_arch: "armv7",
            arch: "arm",
            vendor: "unknown",
            os: "linux",
            env: "ohos",
            abi: "eabi",
            llvm_target: "armv7-unknown-linux-ohos",
        },
    ),
    (
        "armv7-unknown-linux-uclibceabi",
        TargetInfo {
            full_arch: "armv7",
            arch: "arm",
            vendor: "unknown",
            os: "linux",
            env: "uclibc",
            abi: "eabi",
            llvm_target: "armv7-unknown-linux-gnueabi",
        },
    ),
    (
        "armv7-unknown-linux-uclibceabihf",
        TargetInfo {
            full_arch: "armv7",
            arch: "arm",
            vendor: "unknown",
            os: "linux",
            env: "uclibc",
            abi: "eabihf",
            llvm_target: "armv7-unknown-linux-gnueabihf",
        },
    ),
    (
        "armv7-unknown-netbsd-eabihf",
        TargetInfo {
            full_arch: "armv7",
            arch: "arm",
            vendor: "unknown",
            os: "netbsd",
            env: "",
            abi: "eabihf",
            llvm_target: "armv7-unknown-netbsdelf-eabihf",
        },
    ),
    (
        "armv7-unknown-trusty",
        TargetInfo {
            full_arch: "armv7",
            arch: "arm",
            vendor: "unknown",
            os: "trusty",
            env: "",
            abi: "eabi",
            llvm_target: "armv7-unknown-unknown-gnueabi",
        },
    ),
    (
        "armv7-wrs-vxworks-eabihf",
        TargetInfo {
            full_arch: "armv7",
            arch: "arm",
            vendor: "wrs",
            os: "vxworks",
            env: "gnu",
            abi: "eabihf",
            llvm_target: "armv7-unknown-linux-gnueabihf",
        },
    ),
    (
        "armv7a-kmc-solid_asp3-eabi",
        TargetInfo {
            full_arch: "armv7a",
            arch: "arm",
            vendor: "kmc",
            os: "solid_asp3",
            env: "",
            abi: "eabi",
            llvm_target: "armv7a-none-eabi",
        },
    ),
    (
        "armv7a-kmc-solid_asp3-eabihf",
        TargetInfo {
            full_arch: "armv7a",
            arch: "arm",
            vendor: "kmc",
            os: "solid_asp3",
            env: "",
            abi: "eabihf",
            llvm_target: "armv7a-none-eabihf",
        },
    ),
    (
        "armv7a-none-eabi",
        TargetInfo {
            full_arch: "armv7a",
            arch: "arm",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "eabi",
            llvm_target: "armv7a-none-eabi",
        },
    ),
    (
        "armv7a-none-eabihf",
        TargetInfo {
            full_arch: "armv7a",
            arch: "arm",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "eabihf",
            llvm_target: "armv7a-none-eabihf",
        },
    ),
    (
        "armv7a-nuttx-eabi",
        TargetInfo {
            full_arch: "armv7a",
            arch: "arm",
            vendor: "unknown",
            os: "nuttx",
            env: "",
            abi: "eabi",
            llvm_target: "armv7a-none-eabi",
        },
    ),
    (
        "armv7a-nuttx-eabihf",
        TargetInfo {
            full_arch: "armv7a",
            arch: "arm",
            vendor: "unknown",
            os: "nuttx",
            env: "",
            abi: "eabihf",
            llvm_target: "armv7a-none-eabihf",
        },
    ),
    (
        "armv7k-apple-watchos",
        TargetInfo {
            full_arch: "armv7k",
            arch: "arm",
            vendor: "apple",
            os: "watchos",
            env: "",
            abi: "",
            llvm_target: "armv7k-apple-watchos",
        },
    ),
    (
        "armv7r-none-eabi",
        TargetInfo {
            full_arch: "armv7r",
            arch: "arm",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "eabi",
            llvm_target: "armv7r-none-eabi",
        },
    ),
    (
        "armv7r-none-eabihf",
        TargetInfo {
            full_arch: "armv7r",
            arch: "arm",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "eabihf",
            llvm_target: "armv7r-none-eabihf",
        },
    ),
    (
        "armv7s-apple-ios",
        TargetInfo {
            full_arch: "armv7s",
            arch: "arm",
            vendor: "apple",
            os: "ios",
            env: "",
            abi: "",
            llvm_target: "armv7s-apple-ios",
        },
    ),
    (
        "armv8r-none-eabihf",
        TargetInfo {
            full_arch: "armv8r",
            arch: "arm",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "eabihf",
            llvm_target: "armv8r-none-eabihf",
        },
    ),
    (
        "asmjs-unknown-emscripten",
        TargetInfo {
            full_arch: "asmjs",
            arch: "wasm32",
            vendor: "unknown",
            os: "emscripten",
            env: "",
            abi: "",
            llvm_target: "wasm32-unknown-emscripten",
        },
    ),
    (
        "avr-unknown-gnu-atmega328",
        TargetInfo {
            full_arch: "avr",
            arch: "avr",
            vendor: "unknown",
            os: "none",
            env: "gnu",
            abi: "",
            llvm_target: "avr-unknown-unknown",
        },
    ),
    (
        "bpfeb-unknown-none",
        TargetInfo {
            full_arch: "bpfeb",
            arch: "bpf",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "bpfeb",
        },
    ),
    (
        "bpfel-unknown-none",
        TargetInfo {
            full_arch: "bpfel",
            arch: "bpf",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "bpfel",
        },
    ),
    (
        "csky-unknown-linux-gnuabiv2",
        TargetInfo {
            full_arch: "csky",
            arch: "csky",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "abiv2",
            llvm_target: "csky-unknown-linux-gnuabiv2",
        },
    ),
    (
        "csky-unknown-linux-gnuabiv2hf",
        TargetInfo {
            full_arch: "csky",
            arch: "csky",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "abiv2hf",
            llvm_target: "csky-unknown-linux-gnuabiv2",
        },
    ),
    (
        "hexagon-unknown-linux-musl",
        TargetInfo {
            full_arch: "hexagon",
            arch: "hexagon",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "",
            llvm_target: "hexagon-unknown-linux-musl",
        },
    ),
    (
        "hexagon-unknown-none-elf",
        TargetInfo {
            full_arch: "hexagon",
            arch: "hexagon",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "hexagon-unknown-none-elf",
        },
    ),
    (
        "i386-apple-ios",
        TargetInfo {
            full_arch: "i386",
            arch: "x86",
            vendor: "apple",
            os: "ios",
            env: "",
            abi: "sim",
            llvm_target: "i386-apple-ios-simulator",
        },
    ),
    (
        "i586-pc-nto-qnx700",
        TargetInfo {
            full_arch: "i586",
            arch: "x86",
            vendor: "pc",
            os: "nto",
            env: "nto70",
            abi: "",
            llvm_target: "i586-pc-unknown",
        },
    ),
    (
        "i586-pc-windows-msvc",
        TargetInfo {
            full_arch: "i586",
            arch: "x86",
            vendor: "pc",
            os: "windows",
            env: "msvc",
            abi: "",
            llvm_target: "i586-pc-windows-msvc",
        },
    ),
    (
        "i586-unknown-linux-gnu",
        TargetInfo {
            full_arch: "i586",
            arch: "x86",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "",
            llvm_target: "i586-unknown-linux-gnu",
        },
    ),
    (
        "i586-unknown-linux-musl",
        TargetInfo {
            full_arch: "i586",
            arch: "x86",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "",
            llvm_target: "i586-unknown-linux-musl",
        },
    ),
    (
        "i586-unknown-netbsd",
        TargetInfo {
            full_arch: "i586",
            arch: "x86",
            vendor: "unknown",
            os: "netbsd",
            env: "",
            abi: "",
            llvm_target: "i586-unknown-netbsdelf",
        },
    ),
    (
        "i686-apple-darwin",
        TargetInfo {
            full_arch: "i686",
            arch: "x86",
            vendor: "apple",
            os: "macos",
            env: "",
            abi: "",
            llvm_target: "i686-apple-macosx",
        },
    ),
    (
        "i686-linux-android",
        TargetInfo {
            full_arch: "i686",
            arch: "x86",
            vendor: "unknown",
            os: "android",
            env: "",
            abi: "",
            llvm_target: "i686-linux-android",
        },
    ),
    (
        "i686-pc-windows-gnu",
        TargetInfo {
            full_arch: "i686",
            arch: "x86",
            vendor: "pc",
            os: "windows",
            env: "gnu",
            abi: "",
            llvm_target: "i686-pc-windows-gnu",
        },
    ),
    (
        "i686-pc-windows-gnullvm",
        TargetInfo {
            full_arch: "i686",
            arch: "x86",
            vendor: "pc",
            os: "windows",
            env: "gnu",
            abi: "llvm",
            llvm_target: "i686-pc-windows-gnu",
        },
    ),
    (
        "i686-pc-windows-msvc",
        TargetInfo {
            full_arch: "i686",
            arch: "x86",
            vendor: "pc",
            os: "windows",
            env: "msvc",
            abi: "",
            llvm_target: "i686-pc-windows-msvc",
        },
    ),
    (
        "i686-unknown-freebsd",
        TargetInfo {
            full_arch: "i686",
            arch: "x86",
            vendor: "unknown",
            os: "freebsd",
            env: "",
            abi: "",
            llvm_target: "i686-unknown-freebsd",
        },
    ),
    (
        "i686-unknown-haiku",
        TargetInfo {
            full_arch: "i686",
            arch: "x86",
            vendor: "unknown",
            os: "haiku",
            env: "",
            abi: "",
            llvm_target: "i686-unknown-haiku",
        },
    ),
    (
        "i686-unknown-hurd-gnu",
        TargetInfo {
            full_arch: "i686",
            arch: "x86",
            vendor: "unknown",
            os: "hurd",
            env: "gnu",
            abi: "",
            llvm_target: "i686-unknown-hurd-gnu",
        },
    ),
    (
        "i686-unknown-linux-gnu",
        TargetInfo {
            full_arch: "i686",
            arch: "x86",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "",
            llvm_target: "i686-unknown-linux-gnu",
        },
    ),
    (
        "i686-unknown-linux-musl",
        TargetInfo {
            full_arch: "i686",
            arch: "x86",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "",
            llvm_target: "i686-unknown-linux-musl",
        },
    ),
    (
        "i686-unknown-netbsd",
        TargetInfo {
            full_arch: "i686",
            arch: "x86",
            vendor: "unknown",
            os: "netbsd",
            env: "",
            abi: "",
            llvm_target: "i686-unknown-netbsdelf",
        },
    ),
    (
        "i686-unknown-openbsd",
        TargetInfo {
            full_arch: "i686",
            arch: "x86",
            vendor: "unknown",
            os: "openbsd",
            env: "",
            abi: "",
            llvm_target: "i686-unknown-openbsd",
        },
    ),
    (
        "i686-unknown-redox",
        TargetInfo {
            full_arch: "i686",
            arch: "x86",
            vendor: "unknown",
            os: "redox",
            env: "relibc",
            abi: "",
            llvm_target: "i686-unknown-redox",
        },
    ),
    (
        "i686-unknown-uefi",
        TargetInfo {
            full_arch: "i686",
            arch: "x86",
            vendor: "unknown",
            os: "uefi",
            env: "",
            abi: "",
            llvm_target: "i686-unknown-windows-gnu",
        },
    ),
    (
        "i686-uwp-windows-gnu",
        TargetInfo {
            full_arch: "i686",
            arch: "x86",
            vendor: "uwp",
            os: "windows",
            env: "gnu",
            abi: "uwp",
            llvm_target: "i686-pc-windows-gnu",
        },
    ),
    (
        "i686-uwp-windows-msvc",
        TargetInfo {
            full_arch: "i686",
            arch: "x86",
            vendor: "uwp",
            os: "windows",
            env: "msvc",
            abi: "uwp",
            llvm_target: "i686-pc-windows-msvc",
        },
    ),
    (
        "i686-win7-windows-gnu",
        TargetInfo {
            full_arch: "i686",
            arch: "x86",
            vendor: "win7",
            os: "windows",
            env: "gnu",
            abi: "",
            llvm_target: "i686-pc-windows-gnu",
        },
    ),
    (
        "i686-win7-windows-msvc",
        TargetInfo {
            full_arch: "i686",
            arch: "x86",
            vendor: "win7",
            os: "windows",
            env: "msvc",
            abi: "",
            llvm_target: "i686-pc-windows-msvc",
        },
    ),
    (
        "i686-wrs-vxworks",
        TargetInfo {
            full_arch: "i686",
            arch: "x86",
            vendor: "wrs",
            os: "vxworks",
            env: "gnu",
            abi: "",
            llvm_target: "i686-unknown-linux-gnu",
        },
    ),
    (
        "loongarch64-unknown-linux-gnu",
        TargetInfo {
            full_arch: "loongarch64",
            arch: "loongarch64",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "",
            llvm_target: "loongarch64-unknown-linux-gnu",
        },
    ),
    (
        "loongarch64-unknown-linux-musl",
        TargetInfo {
            full_arch: "loongarch64",
            arch: "loongarch64",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "",
            llvm_target: "loongarch64-unknown-linux-musl",
        },
    ),
    (
        "loongarch64-unknown-linux-ohos",
        TargetInfo {
            full_arch: "loongarch64",
            arch: "loongarch64",
            vendor: "unknown",
            os: "linux",
            env: "ohos",
            abi: "",
            llvm_target: "loongarch64-unknown-linux-ohos",
        },
    ),
    (
        "loongarch64-unknown-none",
        TargetInfo {
            full_arch: "loongarch64",
            arch: "loongarch64",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "loongarch64-unknown-none",
        },
    ),
    (
        "loongarch64-unknown-none-softfloat",
        TargetInfo {
            full_arch: "loongarch64",
            arch: "loongarch64",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "softfloat",
            llvm_target: "loongarch64-unknown-none",
        },
    ),
    (
        "m68k-unknown-linux-gnu",
        TargetInfo {
            full_arch: "m68k",
            arch: "m68k",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "",
            llvm_target: "m68k-unknown-linux-gnu",
        },
    ),
    (
        "m68k-unknown-none-elf",
        TargetInfo {
            full_arch: "m68k",
            arch: "m68k",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "m68k",
        },
    ),
    (
        "mips-mti-none-elf",
        TargetInfo {
            full_arch: "mips",
            arch: "mips",
            vendor: "mti",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "mips",
        },
    ),
    (
        "mips-unknown-linux-gnu",
        TargetInfo {
            full_arch: "mips",
            arch: "mips",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "",
            llvm_target: "mips-unknown-linux-gnu",
        },
    ),
    (
        "mips-unknown-linux-musl",
        TargetInfo {
            full_arch: "mips",
            arch: "mips",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "",
            llvm_target: "mips-unknown-linux-musl",
        },
    ),
    (
        "mips-unknown-linux-uclibc",
        TargetInfo {
            full_arch: "mips",
            arch: "mips",
            vendor: "unknown",
            os: "linux",
            env: "uclibc",
            abi: "",
            llvm_target: "mips-unknown-linux-uclibc",
        },
    ),
    (
        "mips64-openwrt-linux-musl",
        TargetInfo {
            full_arch: "mips64",
            arch: "mips64",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "abi64",
            llvm_target: "mips64-unknown-linux-musl",
        },
    ),
    (
        "mips64-unknown-linux-gnuabi64",
        TargetInfo {
            full_arch: "mips64",
            arch: "mips64",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "abi64",
            llvm_target: "mips64-unknown-linux-gnuabi64",
        },
    ),
    (
        "mips64-unknown-linux-muslabi64",
        TargetInfo {
            full_arch: "mips64",
            arch: "mips64",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "abi64",
            llvm_target: "mips64-unknown-linux-musl",
        },
    ),
    (
        "mips64el-unknown-linux-gnuabi64",
        TargetInfo {
            full_arch: "mips64el",
            arch: "mips64",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "abi64",
            llvm_target: "mips64el-unknown-linux-gnuabi64",
        },
    ),
    (
        "mips64el-unknown-linux-muslabi64",
        TargetInfo {
            full_arch: "mips64el",
            arch: "mips64",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "abi64",
            llvm_target: "mips64el-unknown-linux-musl",
        },
    ),
    (
        "mipsel-mti-none-elf",
        TargetInfo {
            full_arch: "mipsel",
            arch: "mips",
            vendor: "mti",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "mipsel",
        },
    ),
    (
        "mipsel-sony-psp",
        TargetInfo {
            full_arch: "mipsel",
            arch: "mips",
            vendor: "sony",
            os: "psp",
            env: "",
            abi: "",
            llvm_target: "mipsel-sony-psp",
        },
    ),
    (
        "mipsel-sony-psx",
        TargetInfo {
            full_arch: "mipsel",
            arch: "mips",
            vendor: "sony",
            os: "psx",
            env: "",
            abi: "",
            llvm_target: "mipsel-sony-psx",
        },
    ),
    (
        "mipsel-unknown-linux-gnu",
        TargetInfo {
            full_arch: "mipsel",
            arch: "mips",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "",
            llvm_target: "mipsel-unknown-linux-gnu",
        },
    ),
    (
        "mipsel-unknown-linux-musl",
        TargetInfo {
            full_arch: "mipsel",
            arch: "mips",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "",
            llvm_target: "mipsel-unknown-linux-musl",
        },
    ),
    (
        "mipsel-unknown-linux-uclibc",
        TargetInfo {
            full_arch: "mipsel",
            arch: "mips",
            vendor: "unknown",
            os: "linux",
            env: "uclibc",
            abi: "",
            llvm_target: "mipsel-unknown-linux-uclibc",
        },
    ),
    (
        "mipsel-unknown-netbsd",
        TargetInfo {
            full_arch: "mipsel",
            arch: "mips",
            vendor: "unknown",
            os: "netbsd",
            env: "",
            abi: "",
            llvm_target: "mipsel-unknown-netbsd",
        },
    ),
    (
        "mipsel-unknown-none",
        TargetInfo {
            full_arch: "mipsel",
            arch: "mips",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "mipsel-unknown-none",
        },
    ),
    (
        "mipsisa32r6-unknown-linux-gnu",
        TargetInfo {
            full_arch: "mipsisa32r6",
            arch: "mips32r6",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "",
            llvm_target: "mipsisa32r6-unknown-linux-gnu",
        },
    ),
    (
        "mipsisa32r6el-unknown-linux-gnu",
        TargetInfo {
            full_arch: "mipsisa32r6el",
            arch: "mips32r6",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "",
            llvm_target: "mipsisa32r6el-unknown-linux-gnu",
        },
    ),
    (
        "mipsisa64r6-unknown-linux-gnuabi64",
        TargetInfo {
            full_arch: "mipsisa64r6",
            arch: "mips64r6",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "abi64",
            llvm_target: "mipsisa64r6-unknown-linux-gnuabi64",
        },
    ),
    (
        "mipsisa64r6el-unknown-linux-gnuabi64",
        TargetInfo {
            full_arch: "mipsisa64r6el",
            arch: "mips64r6",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "abi64",
            llvm_target: "mipsisa64r6el-unknown-linux-gnuabi64",
        },
    ),
    (
        "msp430-none-elf",
        TargetInfo {
            full_arch: "msp430",
            arch: "msp430",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "msp430-none-elf",
        },
    ),
    (
        "nvptx64-nvidia-cuda",
        TargetInfo {
            full_arch: "nvptx64",
            arch: "nvptx64",
            vendor: "nvidia",
            os: "cuda",
            env: "",
            abi: "",
            llvm_target: "nvptx64-nvidia-cuda",
        },
    ),
    (
        "powerpc-unknown-freebsd",
        TargetInfo {
            full_arch: "powerpc",
            arch: "powerpc",
            vendor: "unknown",
            os: "freebsd",
            env: "",
            abi: "",
            llvm_target: "powerpc-unknown-freebsd13.0",
        },
    ),
    (
        "powerpc-unknown-linux-gnu",
        TargetInfo {
            full_arch: "powerpc",
            arch: "powerpc",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "",
            llvm_target: "powerpc-unknown-linux-gnu",
        },
    ),
    (
        "powerpc-unknown-linux-gnuspe",
        TargetInfo {
            full_arch: "powerpc",
            arch: "powerpc",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "spe",
            llvm_target: "powerpc-unknown-linux-gnuspe",
        },
    ),
    (
        "powerpc-unknown-linux-musl",
        TargetInfo {
            full_arch: "powerpc",
            arch: "powerpc",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "",
            llvm_target: "powerpc-unknown-linux-musl",
        },
    ),
    (
        "powerpc-unknown-linux-muslspe",
        TargetInfo {
            full_arch: "powerpc",
            arch: "powerpc",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "spe",
            llvm_target: "powerpc-unknown-linux-muslspe",
        },
    ),
    (
        "powerpc-unknown-netbsd",
        TargetInfo {
            full_arch: "powerpc",
            arch: "powerpc",
            vendor: "unknown",
            os: "netbsd",
            env: "",
            abi: "",
            llvm_target: "powerpc-unknown-netbsd",
        },
    ),
    (
        "powerpc-unknown-openbsd",
        TargetInfo {
            full_arch: "powerpc",
            arch: "powerpc",
            vendor: "unknown",
            os: "openbsd",
            env: "",
            abi: "",
            llvm_target: "powerpc-unknown-openbsd",
        },
    ),
    (
        "powerpc-wrs-vxworks",
        TargetInfo {
            full_arch: "powerpc",
            arch: "powerpc",
            vendor: "wrs",
            os: "vxworks",
            env: "gnu",
            abi: "",
            llvm_target: "powerpc-unknown-linux-gnu",
        },
    ),
    (
        "powerpc-wrs-vxworks-spe",
        TargetInfo {
            full_arch: "powerpc",
            arch: "powerpc",
            vendor: "wrs",
            os: "vxworks",
            env: "gnu",
            abi: "spe",
            llvm_target: "powerpc-unknown-linux-gnuspe",
        },
    ),
    (
        "powerpc64-ibm-aix",
        TargetInfo {
            full_arch: "powerpc64",
            arch: "powerpc64",
            vendor: "ibm",
            os: "aix",
            env: "",
            abi: "vec-extabi",
            llvm_target: "powerpc64-ibm-aix",
        },
    ),
    (
        "powerpc64-unknown-freebsd",
        TargetInfo {
            full_arch: "powerpc64",
            arch: "powerpc64",
            vendor: "unknown",
            os: "freebsd",
            env: "",
            abi: "",
            llvm_target: "powerpc64-unknown-freebsd",
        },
    ),
    (
        "powerpc64-unknown-linux-gnu",
        TargetInfo {
            full_arch: "powerpc64",
            arch: "powerpc64",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "",
            llvm_target: "powerpc64-unknown-linux-gnu",
        },
    ),
    (
        "powerpc64-unknown-linux-musl",
        TargetInfo {
            full_arch: "powerpc64",
            arch: "powerpc64",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "",
            llvm_target: "powerpc64-unknown-linux-musl",
        },
    ),
    (
        "powerpc64-unknown-openbsd",
        TargetInfo {
            full_arch: "powerpc64",
            arch: "powerpc64",
            vendor: "unknown",
            os: "openbsd",
            env: "",
            abi: "",
            llvm_target: "powerpc64-unknown-openbsd",
        },
    ),
    (
        "powerpc64-wrs-vxworks",
        TargetInfo {
            full_arch: "powerpc64",
            arch: "powerpc64",
            vendor: "wrs",
            os: "vxworks",
            env: "gnu",
            abi: "",
            llvm_target: "powerpc64-unknown-linux-gnu",
        },
    ),
    (
        "powerpc64le-unknown-freebsd",
        TargetInfo {
            full_arch: "powerpc64le",
            arch: "powerpc64",
            vendor: "unknown",
            os: "freebsd",
            env: "",
            abi: "",
            llvm_target: "powerpc64le-unknown-freebsd",
        },
    ),
    (
        "powerpc64le-unknown-linux-gnu",
        TargetInfo {
            full_arch: "powerpc64le",
            arch: "powerpc64",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "",
            llvm_target: "powerpc64le-unknown-linux-gnu",
        },
    ),
    (
        "powerpc64le-unknown-linux-musl",
        TargetInfo {
            full_arch: "powerpc64le",
            arch: "powerpc64",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "",
            llvm_target: "powerpc64le-unknown-linux-musl",
        },
    ),
    (
        "riscv32-wrs-vxworks",
        TargetInfo {
            full_arch: "riscv32",
            arch: "riscv32",
            vendor: "wrs",
            os: "vxworks",
            env: "gnu",
            abi: "",
            llvm_target: "riscv32",
        },
    ),
    (
        "riscv32e-unknown-none-elf",
        TargetInfo {
            full_arch: "riscv32e",
            arch: "riscv32",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "ilp32e",
            llvm_target: "riscv32",
        },
    ),
    (
        "riscv32em-unknown-none-elf",
        TargetInfo {
            full_arch: "riscv32em",
            arch: "riscv32",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "ilp32e",
            llvm_target: "riscv32",
        },
    ),
    (
        "riscv32emc-unknown-none-elf",
        TargetInfo {
            full_arch: "riscv32emc",
            arch: "riscv32",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "ilp32e",
            llvm_target: "riscv32",
        },
    ),
    (
        "riscv32gc-unknown-linux-gnu",
        TargetInfo {
            full_arch: "riscv32gc",
            arch: "riscv32",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "",
            llvm_target: "riscv32-unknown-linux-gnu",
        },
    ),
    (
        "riscv32gc-unknown-linux-musl",
        TargetInfo {
            full_arch: "riscv32gc",
            arch: "riscv32",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "",
            llvm_target: "riscv32-unknown-linux-musl",
        },
    ),
    (
        "riscv32i-unknown-none-elf",
        TargetInfo {
            full_arch: "riscv32i",
            arch: "riscv32",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "riscv32",
        },
    ),
    (
        "riscv32im-risc0-zkvm-elf",
        TargetInfo {
            full_arch: "riscv32im",
            arch: "riscv32",
            vendor: "risc0",
            os: "zkvm",
            env: "",
            abi: "",
            llvm_target: "riscv32",
        },
    ),
    (
        "riscv32im-unknown-none-elf",
        TargetInfo {
            full_arch: "riscv32im",
            arch: "riscv32",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "riscv32",
        },
    ),
    (
        "riscv32ima-unknown-none-elf",
        TargetInfo {
            full_arch: "riscv32ima",
            arch: "riscv32",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "riscv32",
        },
    ),
    (
        "riscv32imac-esp-espidf",
        TargetInfo {
            full_arch: "riscv32imac",
            arch: "riscv32",
            vendor: "espressif",
            os: "espidf",
            env: "newlib",
            abi: "",
            llvm_target: "riscv32",
        },
    ),
    (
        "riscv32imac-unknown-none-elf",
        TargetInfo {
            full_arch: "riscv32imac",
            arch: "riscv32",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "riscv32",
        },
    ),
    (
        "riscv32imac-unknown-nuttx-elf",
        TargetInfo {
            full_arch: "riscv32imac",
            arch: "riscv32",
            vendor: "unknown",
            os: "nuttx",
            env: "",
            abi: "",
            llvm_target: "riscv32",
        },
    ),
    (
        "riscv32imac-unknown-xous-elf",
        TargetInfo {
            full_arch: "riscv32imac",
            arch: "riscv32",
            vendor: "unknown",
            os: "xous",
            env: "",
            abi: "",
            llvm_target: "riscv32",
        },
    ),
    (
        "riscv32imafc-esp-espidf",
        TargetInfo {
            full_arch: "riscv32imafc",
            arch: "riscv32",
            vendor: "espressif",
            os: "espidf",
            env: "newlib",
            abi: "",
            llvm_target: "riscv32",
        },
    ),
    (
        "riscv32imafc-unknown-none-elf",
        TargetInfo {
            full_arch: "riscv32imafc",
            arch: "riscv32",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "riscv32",
        },
    ),
    (
        "riscv32imafc-unknown-nuttx-elf",
        TargetInfo {
            full_arch: "riscv32imafc",
            arch: "riscv32",
            vendor: "unknown",
            os: "nuttx",
            env: "",
            abi: "",
            llvm_target: "riscv32",
        },
    ),
    (
        "riscv32imc-esp-espidf",
        TargetInfo {
            full_arch: "riscv32imc",
            arch: "riscv32",
            vendor: "espressif",
            os: "espidf",
            env: "newlib",
            abi: "",
            llvm_target: "riscv32",
        },
    ),
    (
        "riscv32imc-unknown-none-elf",
        TargetInfo {
            full_arch: "riscv32imc",
            arch: "riscv32",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "riscv32",
        },
    ),
    (
        "riscv32imc-unknown-nuttx-elf",
        TargetInfo {
            full_arch: "riscv32imc",
            arch: "riscv32",
            vendor: "unknown",
            os: "nuttx",
            env: "",
            abi: "",
            llvm_target: "riscv32",
        },
    ),
    (
        "riscv64-linux-android",
        TargetInfo {
            full_arch: "riscv64",
            arch: "riscv64",
            vendor: "unknown",
            os: "android",
            env: "",
            abi: "",
            llvm_target: "riscv64-linux-android",
        },
    ),
    (
        "riscv64-wrs-vxworks",
        TargetInfo {
            full_arch: "riscv64",
            arch: "riscv64",
            vendor: "wrs",
            os: "vxworks",
            env: "gnu",
            abi: "",
            llvm_target: "riscv64",
        },
    ),
    (
        "riscv64gc-unknown-freebsd",
        TargetInfo {
            full_arch: "riscv64gc",
            arch: "riscv64",
            vendor: "unknown",
            os: "freebsd",
            env: "",
            abi: "",
            llvm_target: "riscv64-unknown-freebsd",
        },
    ),
    (
        "riscv64gc-unknown-fuchsia",
        TargetInfo {
            full_arch: "riscv64gc",
            arch: "riscv64",
            vendor: "unknown",
            os: "fuchsia",
            env: "",
            abi: "",
            llvm_target: "riscv64-unknown-fuchsia",
        },
    ),
    (
        "riscv64gc-unknown-hermit",
        TargetInfo {
            full_arch: "riscv64gc",
            arch: "riscv64",
            vendor: "unknown",
            os: "hermit",
            env: "",
            abi: "",
            llvm_target: "riscv64-unknown-hermit",
        },
    ),
    (
        "riscv64gc-unknown-linux-gnu",
        TargetInfo {
            full_arch: "riscv64gc",
            arch: "riscv64",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "",
            llvm_target: "riscv64-unknown-linux-gnu",
        },
    ),
    (
        "riscv64gc-unknown-linux-musl",
        TargetInfo {
            full_arch: "riscv64gc",
            arch: "riscv64",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "",
            llvm_target: "riscv64-unknown-linux-musl",
        },
    ),
    (
        "riscv64gc-unknown-netbsd",
        TargetInfo {
            full_arch: "riscv64gc",
            arch: "riscv64",
            vendor: "unknown",
            os: "netbsd",
            env: "",
            abi: "",
            llvm_target: "riscv64-unknown-netbsd",
        },
    ),
    (
        "riscv64gc-unknown-none-elf",
        TargetInfo {
            full_arch: "riscv64gc",
            arch: "riscv64",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "riscv64",
        },
    ),
    (
        "riscv64gc-unknown-nuttx-elf",
        TargetInfo {
            full_arch: "riscv64gc",
            arch: "riscv64",
            vendor: "unknown",
            os: "nuttx",
            env: "",
            abi: "",
            llvm_target: "riscv64",
        },
    ),
    (
        "riscv64gc-unknown-openbsd",
        TargetInfo {
            full_arch: "riscv64gc",
            arch: "riscv64",
            vendor: "unknown",
            os: "openbsd",
            env: "",
            abi: "",
            llvm_target: "riscv64-unknown-openbsd",
        },
    ),
    (
        "riscv64imac-unknown-none-elf",
        TargetInfo {
            full_arch: "riscv64imac",
            arch: "riscv64",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "riscv64",
        },
    ),
    (
        "riscv64imac-unknown-nuttx-elf",
        TargetInfo {
            full_arch: "riscv64imac",
            arch: "riscv64",
            vendor: "unknown",
            os: "nuttx",
            env: "",
            abi: "",
            llvm_target: "riscv64",
        },
    ),
    (
        "s390x-unknown-linux-gnu",
        TargetInfo {
            full_arch: "s390x",
            arch: "s390x",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "",
            llvm_target: "s390x-unknown-linux-gnu",
        },
    ),
    (
        "s390x-unknown-linux-musl",
        TargetInfo {
            full_arch: "s390x",
            arch: "s390x",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "",
            llvm_target: "s390x-unknown-linux-musl",
        },
    ),
    (
        "sparc-unknown-linux-gnu",
        TargetInfo {
            full_arch: "sparc",
            arch: "sparc",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "",
            llvm_target: "sparc-unknown-linux-gnu",
        },
    ),
    (
        "sparc-unknown-none-elf",
        TargetInfo {
            full_arch: "sparc",
            arch: "sparc",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "sparc-unknown-none-elf",
        },
    ),
    (
        "sparc64-unknown-linux-gnu",
        TargetInfo {
            full_arch: "sparc64",
            arch: "sparc64",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "",
            llvm_target: "sparc64-unknown-linux-gnu",
        },
    ),
    (
        "sparc64-unknown-netbsd",
        TargetInfo {
            full_arch: "sparc64",
            arch: "sparc64",
            vendor: "unknown",
            os: "netbsd",
            env: "",
            abi: "",
            llvm_target: "sparc64-unknown-netbsd",
        },
    ),
    (
        "sparc64-unknown-openbsd",
        TargetInfo {
            full_arch: "sparc64",
            arch: "sparc64",
            vendor: "unknown",
            os: "openbsd",
            env: "",
            abi: "",
            llvm_target: "sparc64-unknown-openbsd",
        },
    ),
    (
        "sparcv9-sun-solaris",
        TargetInfo {
            full_arch: "sparcv9",
            arch: "sparc64",
            vendor: "sun",
            os: "solaris",
            env: "",
            abi: "",
            llvm_target: "sparcv9-sun-solaris",
        },
    ),
    (
        "thumbv4t-none-eabi",
        TargetInfo {
            full_arch: "thumbv4t",
            arch: "arm",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "eabi",
            llvm_target: "thumbv4t-none-eabi",
        },
    ),
    (
        "thumbv5te-none-eabi",
        TargetInfo {
            full_arch: "thumbv5te",
            arch: "arm",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "eabi",
            llvm_target: "thumbv5te-none-eabi",
        },
    ),
    (
        "thumbv6m-none-eabi",
        TargetInfo {
            full_arch: "thumbv6m",
            arch: "arm",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "eabi",
            llvm_target: "thumbv6m-none-eabi",
        },
    ),
    (
        "thumbv6m-nuttx-eabi",
        TargetInfo {
            full_arch: "thumbv6m",
            arch: "arm",
            vendor: "unknown",
            os: "nuttx",
            env: "",
            abi: "eabi",
            llvm_target: "thumbv6m-none-eabi",
        },
    ),
    (
        "thumbv7a-nuttx-eabi",
        TargetInfo {
            full_arch: "thumbv7a",
            arch: "arm",
            vendor: "unknown",
            os: "nuttx",
            env: "",
            abi: "eabi",
            llvm_target: "thumbv7a-none-eabi",
        },
    ),
    (
        "thumbv7a-nuttx-eabihf",
        TargetInfo {
            full_arch: "thumbv7a",
            arch: "arm",
            vendor: "unknown",
            os: "nuttx",
            env: "",
            abi: "eabihf",
            llvm_target: "thumbv7a-none-eabihf",
        },
    ),
    (
        "thumbv7a-pc-windows-msvc",
        TargetInfo {
            full_arch: "thumbv7a",
            arch: "arm",
            vendor: "pc",
            os: "windows",
            env: "msvc",
            abi: "",
            llvm_target: "thumbv7a-pc-windows-msvc",
        },
    ),
    (
        "thumbv7a-uwp-windows-msvc",
        TargetInfo {
            full_arch: "thumbv7a",
            arch: "arm",
            vendor: "uwp",
            os: "windows",
            env: "msvc",
            abi: "uwp",
            llvm_target: "thumbv7a-pc-windows-msvc",
        },
    ),
    (
        "thumbv7em-none-eabi",
        TargetInfo {
            full_arch: "thumbv7em",
            arch: "arm",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "eabi",
            llvm_target: "thumbv7em-none-eabi",
        },
    ),
    (
        "thumbv7em-none-eabihf",
        TargetInfo {
            full_arch: "thumbv7em",
            arch: "arm",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "eabihf",
            llvm_target: "thumbv7em-none-eabihf",
        },
    ),
    (
        "thumbv7em-nuttx-eabi",
        TargetInfo {
            full_arch: "thumbv7em",
            arch: "arm",
            vendor: "unknown",
            os: "nuttx",
            env: "",
            abi: "eabi",
            llvm_target: "thumbv7em-none-eabi",
        },
    ),
    (
        "thumbv7em-nuttx-eabihf",
        TargetInfo {
            full_arch: "thumbv7em",
            arch: "arm",
            vendor: "unknown",
            os: "nuttx",
            env: "",
            abi: "eabihf",
            llvm_target: "thumbv7em-none-eabihf",
        },
    ),
    (
        "thumbv7m-none-eabi",
        TargetInfo {
            full_arch: "thumbv7m",
            arch: "arm",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "eabi",
            llvm_target: "thumbv7m-none-eabi",
        },
    ),
    (
        "thumbv7m-nuttx-eabi",
        TargetInfo {
            full_arch: "thumbv7m",
            arch: "arm",
            vendor: "unknown",
            os: "nuttx",
            env: "",
            abi: "eabi",
            llvm_target: "thumbv7m-none-eabi",
        },
    ),
    (
        "thumbv7neon-linux-androideabi",
        TargetInfo {
            full_arch: "thumbv7neon",
            arch: "arm",
            vendor: "unknown",
            os: "android",
            env: "",
            abi: "eabi",
            llvm_target: "armv7-none-linux-android",
        },
    ),
    (
        "thumbv7neon-unknown-linux-gnueabihf",
        TargetInfo {
            full_arch: "thumbv7neon",
            arch: "arm",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "eabihf",
            llvm_target: "armv7-unknown-linux-gnueabihf",
        },
    ),
    (
        "thumbv7neon-unknown-linux-musleabihf",
        TargetInfo {
            full_arch: "thumbv7neon",
            arch: "arm",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "eabihf",
            llvm_target: "armv7-unknown-linux-musleabihf",
        },
    ),
    (
        "thumbv8m.base-none-eabi",
        TargetInfo {
            full_arch: "thumbv8m.base",
            arch: "arm",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "eabi",
            llvm_target: "thumbv8m.base-none-eabi",
        },
    ),
    (
        "thumbv8m.base-nuttx-eabi",
        TargetInfo {
            full_arch: "thumbv8m.base",
            arch: "arm",
            vendor: "unknown",
            os: "nuttx",
            env: "",
            abi: "eabi",
            llvm_target: "thumbv8m.base-none-eabi",
        },
    ),
    (
        "thumbv8m.main-none-eabi",
        TargetInfo {
            full_arch: "thumbv8m.main",
            arch: "arm",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "eabi",
            llvm_target: "thumbv8m.main-none-eabi",
        },
    ),
    (
        "thumbv8m.main-none-eabihf",
        TargetInfo {
            full_arch: "thumbv8m.main",
            arch: "arm",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "eabihf",
            llvm_target: "thumbv8m.main-none-eabihf",
        },
    ),
    (
        "thumbv8m.main-nuttx-eabi",
        TargetInfo {
            full_arch: "thumbv8m.main",
            arch: "arm",
            vendor: "unknown",
            os: "nuttx",
            env: "",
            abi: "eabi",
            llvm_target: "thumbv8m.main-none-eabi",
        },
    ),
    (
        "thumbv8m.main-nuttx-eabihf",
        TargetInfo {
            full_arch: "thumbv8m.main",
            arch: "arm",
            vendor: "unknown",
            os: "nuttx",
            env: "",
            abi: "eabihf",
            llvm_target: "thumbv8m.main-none-eabihf",
        },
    ),
    (
        "wasm32-unknown-emscripten",
        TargetInfo {
            full_arch: "wasm32",
            arch: "wasm32",
            vendor: "unknown",
            os: "emscripten",
            env: "",
            abi: "",
            llvm_target: "wasm32-unknown-emscripten",
        },
    ),
    (
        "wasm32-unknown-unknown",
        TargetInfo {
            full_arch: "wasm32",
            arch: "wasm32",
            vendor: "unknown",
            os: "unknown",
            env: "",
            abi: "",
            llvm_target: "wasm32-unknown-unknown",
        },
    ),
    (
        "wasm32-wasi",
        TargetInfo {
            full_arch: "wasm32",
            arch: "wasm32",
            vendor: "unknown",
            os: "wasi",
            env: "",
            abi: "",
            llvm_target: "wasm32-wasi",
        },
    ),
    (
        "wasm32-wasip1",
        TargetInfo {
            full_arch: "wasm32",
            arch: "wasm32",
            vendor: "unknown",
            os: "wasi",
            env: "p1",
            abi: "",
            llvm_target: "wasm32-wasip1",
        },
    ),
    (
        "wasm32-wasip1-threads",
        TargetInfo {
            full_arch: "wasm32",
            arch: "wasm32",
            vendor: "unknown",
            os: "wasi",
            env: "p1",
            abi: "",
            llvm_target: "wasm32-wasi",
        },
    ),
    (
        "wasm32-wasip2",
        TargetInfo {
            full_arch: "wasm32",
            arch: "wasm32",
            vendor: "unknown",
            os: "wasi",
            env: "p2",
            abi: "",
            llvm_target: "wasm32-wasip2",
        },
    ),
    (
        "wasm32v1-none",
        TargetInfo {
            full_arch: "wasm32v1",
            arch: "wasm32",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "wasm32-unknown-unknown",
        },
    ),
    (
        "wasm64-unknown-unknown",
        TargetInfo {
            full_arch: "wasm64",
            arch: "wasm64",
            vendor: "unknown",
            os: "unknown",
            env: "",
            abi: "",
            llvm_target: "wasm64-unknown-unknown",
        },
    ),
    (
        "x86_64-apple-darwin",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "apple",
            os: "macos",
            env: "",
            abi: "",
            llvm_target: "x86_64-apple-macosx",
        },
    ),
    (
        "x86_64-apple-ios",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "apple",
            os: "ios",
            env: "",
            abi: "sim",
            llvm_target: "x86_64-apple-ios-simulator",
        },
    ),
    (
        "x86_64-apple-ios-macabi",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "apple",
            os: "ios",
            env: "",
            abi: "macabi",
            llvm_target: "x86_64-apple-ios-macabi",
        },
    ),
    (
        "x86_64-apple-tvos",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "apple",
            os: "tvos",
            env: "",
            abi: "sim",
            llvm_target: "x86_64-apple-tvos-simulator",
        },
    ),
    (
        "x86_64-apple-watchos-sim",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "apple",
            os: "watchos",
            env: "",
            abi: "sim",
            llvm_target: "x86_64-apple-watchos-simulator",
        },
    ),
    (
        "x86_64-fortanix-unknown-sgx",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "fortanix",
            os: "unknown",
            env: "sgx",
            abi: "fortanix",
            llvm_target: "x86_64-elf",
        },
    ),
    (
        "x86_64-fuchsia",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "fuchsia",
            env: "",
            abi: "",
            llvm_target: "x86_64-fuchsia",
        },
    ),
    (
        "x86_64-linux-android",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "android",
            env: "",
            abi: "",
            llvm_target: "x86_64-linux-android",
        },
    ),
    (
        "x86_64-pc-nto-qnx710",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "pc",
            os: "nto",
            env: "nto71",
            abi: "",
            llvm_target: "x86_64-pc-unknown",
        },
    ),
    (
        "x86_64-pc-nto-qnx710_iosock",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "pc",
            os: "nto",
            env: "nto71_iosock",
            abi: "",
            llvm_target: "x86_64-pc-unknown",
        },
    ),
    (
        "x86_64-pc-nto-qnx800",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "pc",
            os: "nto",
            env: "nto80",
            abi: "",
            llvm_target: "x86_64-pc-unknown",
        },
    ),
    (
        "x86_64-pc-solaris",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "pc",
            os: "solaris",
            env: "",
            abi: "",
            llvm_target: "x86_64-pc-solaris",
        },
    ),
    (
        "x86_64-pc-windows-gnu",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "pc",
            os: "windows",
            env: "gnu",
            abi: "",
            llvm_target: "x86_64-pc-windows-gnu",
        },
    ),
    (
        "x86_64-pc-windows-gnullvm",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "pc",
            os: "windows",
            env: "gnu",
            abi: "llvm",
            llvm_target: "x86_64-pc-windows-gnu",
        },
    ),
    (
        "x86_64-pc-windows-msvc",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "pc",
            os: "windows",
            env: "msvc",
            abi: "",
            llvm_target: "x86_64-pc-windows-msvc",
        },
    ),
    (
        "x86_64-sun-solaris",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "sun",
            os: "solaris",
            env: "",
            abi: "",
            llvm_target: "x86_64-pc-solaris",
        },
    ),
    (
        "x86_64-unikraft-linux-musl",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unikraft",
            os: "linux",
            env: "musl",
            abi: "",
            llvm_target: "x86_64-unknown-linux-musl",
        },
    ),
    (
        "x86_64-unknown-dragonfly",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "dragonfly",
            env: "",
            abi: "",
            llvm_target: "x86_64-unknown-dragonfly",
        },
    ),
    (
        "x86_64-unknown-freebsd",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "freebsd",
            env: "",
            abi: "",
            llvm_target: "x86_64-unknown-freebsd",
        },
    ),
    (
        "x86_64-unknown-fuchsia",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "fuchsia",
            env: "",
            abi: "",
            llvm_target: "x86_64-unknown-fuchsia",
        },
    ),
    (
        "x86_64-unknown-haiku",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "haiku",
            env: "",
            abi: "",
            llvm_target: "x86_64-unknown-haiku",
        },
    ),
    (
        "x86_64-unknown-hermit",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "hermit",
            env: "",
            abi: "",
            llvm_target: "x86_64-unknown-hermit",
        },
    ),
    (
        "x86_64-unknown-hurd-gnu",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "hurd",
            env: "gnu",
            abi: "",
            llvm_target: "x86_64-unknown-hurd-gnu",
        },
    ),
    (
        "x86_64-unknown-illumos",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "illumos",
            env: "",
            abi: "",
            llvm_target: "x86_64-pc-solaris",
        },
    ),
    (
        "x86_64-unknown-l4re-uclibc",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "l4re",
            env: "uclibc",
            abi: "",
            llvm_target: "x86_64-unknown-l4re-uclibc",
        },
    ),
    (
        "x86_64-unknown-linux-gnu",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "",
            llvm_target: "x86_64-unknown-linux-gnu",
        },
    ),
    (
        "x86_64-unknown-linux-gnux32",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "linux",
            env: "gnu",
            abi: "x32",
            llvm_target: "x86_64-unknown-linux-gnux32",
        },
    ),
    (
        "x86_64-unknown-linux-musl",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "linux",
            env: "musl",
            abi: "",
            llvm_target: "x86_64-unknown-linux-musl",
        },
    ),
    (
        "x86_64-unknown-linux-none",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "linux",
            env: "",
            abi: "",
            llvm_target: "x86_64-unknown-linux-none",
        },
    ),
    (
        "x86_64-unknown-linux-ohos",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "linux",
            env: "ohos",
            abi: "",
            llvm_target: "x86_64-unknown-linux-ohos",
        },
    ),
    (
        "x86_64-unknown-netbsd",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "netbsd",
            env: "",
            abi: "",
            llvm_target: "x86_64-unknown-netbsd",
        },
    ),
    (
        "x86_64-unknown-none",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "x86_64-unknown-none-elf",
        },
    ),
    (
        "x86_64-unknown-none-linuxkernel",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "none",
            env: "gnu",
            abi: "",
            llvm_target: "x86_64-unknown-none-elf",
        },
    ),
    (
        "x86_64-unknown-openbsd",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "openbsd",
            env: "",
            abi: "",
            llvm_target: "x86_64-unknown-openbsd",
        },
    ),
    (
        "x86_64-unknown-redox",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "redox",
            env: "relibc",
            abi: "",
            llvm_target: "x86_64-unknown-redox",
        },
    ),
    (
        "x86_64-unknown-trusty",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "trusty",
            env: "",
            abi: "",
            llvm_target: "x86_64-unknown-unknown-musl",
        },
    ),
    (
        "x86_64-unknown-uefi",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "unknown",
            os: "uefi",
            env: "",
            abi: "",
            llvm_target: "x86_64-unknown-windows-gnu",
        },
    ),
    (
        "x86_64-uwp-windows-gnu",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "uwp",
            os: "windows",
            env: "gnu",
            abi: "uwp",
            llvm_target: "x86_64-pc-windows-gnu",
        },
    ),
    (
        "x86_64-uwp-windows-msvc",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "uwp",
            os: "windows",
            env: "msvc",
            abi: "uwp",
            llvm_target: "x86_64-pc-windows-msvc",
        },
    ),
    (
        "x86_64-win7-windows-gnu",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "win7",
            os: "windows",
            env: "gnu",
            abi: "",
            llvm_target: "x86_64-pc-windows-gnu",
        },
    ),
    (
        "x86_64-win7-windows-msvc",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "win7",
            os: "windows",
            env: "msvc",
            abi: "",
            llvm_target: "x86_64-pc-windows-msvc",
        },
    ),
    (
        "x86_64-wrs-vxworks",
        TargetInfo {
            full_arch: "x86_64",
            arch: "x86_64",
            vendor: "wrs",
            os: "vxworks",
            env: "gnu",
            abi: "",
            llvm_target: "x86_64-unknown-linux-gnu",
        },
    ),
    (
        "x86_64h-apple-darwin",
        TargetInfo {
            full_arch: "x86_64h",
            arch: "x86_64",
            vendor: "apple",
            os: "macos",
            env: "",
            abi: "",
            llvm_target: "x86_64h-apple-macosx",
        },
    ),
    (
        "xtensa-esp32-espidf",
        TargetInfo {
            full_arch: "xtensa",
            arch: "xtensa",
            vendor: "espressif",
            os: "espidf",
            env: "newlib",
            abi: "",
            llvm_target: "xtensa-none-elf",
        },
    ),
    (
        "xtensa-esp32-none-elf",
        TargetInfo {
            full_arch: "xtensa",
            arch: "xtensa",
            vendor: "espressif",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "xtensa-none-elf",
        },
    ),
    (
        "xtensa-esp32s2-espidf",
        TargetInfo {
            full_arch: "xtensa",
            arch: "xtensa",
            vendor: "espressif",
            os: "espidf",
            env: "newlib",
            abi: "",
            llvm_target: "xtensa-none-elf",
        },
    ),
    (
        "xtensa-esp32s2-none-elf",
        TargetInfo {
            full_arch: "xtensa",
            arch: "xtensa",
            vendor: "espressif",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "xtensa-none-elf",
        },
    ),
    (
        "xtensa-esp32s3-espidf",
        TargetInfo {
            full_arch: "xtensa",
            arch: "xtensa",
            vendor: "espressif",
            os: "espidf",
            env: "newlib",
            abi: "",
            llvm_target: "xtensa-none-elf",
        },
    ),
    (
        "xtensa-esp32s3-none-elf",
        TargetInfo {
            full_arch: "xtensa",
            arch: "xtensa",
            vendor: "espressif",
            os: "none",
            env: "",
            abi: "",
            llvm_target: "xtensa-none-elf",
        },
    ),
];
