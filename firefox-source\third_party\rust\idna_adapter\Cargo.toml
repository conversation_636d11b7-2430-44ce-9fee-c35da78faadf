# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.82"
name = "idna_adapter"
version = "1.2.1"
authors = ["The rust-url developers"]
build = false
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "Back end adapter for idna"
homepage = "https://docs.rs/crate/idna_adapter/latest"
documentation = "https://docs.rs/idna_adapter/latest/idna_adapter/"
readme = "README.md"
keywords = [
    "unicode",
    "dns",
    "idna",
]
categories = [
    "no-std",
    "internationalization",
]
license = "Apache-2.0 OR MIT"
repository = "https://github.com/hsivonen/idna_adapter"

[features]
compiled_data = [
    "icu_normalizer/compiled_data",
    "icu_properties/compiled_data",
]

[lib]
name = "idna_adapter"
path = "src/lib.rs"

[dependencies.icu_normalizer]
version = "2"
default-features = false

[dependencies.icu_properties]
version = "2"
default-features = false
