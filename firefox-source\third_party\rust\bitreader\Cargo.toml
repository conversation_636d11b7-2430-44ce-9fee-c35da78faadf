# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
name = "bitreader"
version = "0.3.6"
authors = ["Ilkka Rauta <<EMAIL>>"]
description = "BitReader helps reading individual bits from a slice of bytes.\n\nYou can read \"unusual\" numbers of bits from the byte slice, for example 13 bits\nat once. The reader internally keeps track of position within the buffer.\n"
homepage = "https://github.com/irauta/bitreader"
documentation = "https://docs.rs/bitreader"
keywords = ["bit", "bits", "bitstream"]
license = "MIT OR Apache-2.0"
repository = "https://github.com/irauta/bitreader"
[dependencies.cfg-if]
version = "1"

[features]
default = ["std"]
std = []
