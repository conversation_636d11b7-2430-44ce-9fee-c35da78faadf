# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
name = "cachemap2"
version = "0.3.0"
authors = ["<PERSON> <<EMAIL>>"]
description = "A concurrent insert-only hashmap for caching values"
homepage = "https://github.com/afranchuk/cachemap2"
readme = "README.md"
keywords = [
    "sync",
    "data-structure",
    "cache",
    "hash-map",
]
categories = [
    "concurrency",
    "data-structures",
    "caching",
]
license = "MIT"

[dependencies.abi_stable]
version = ">=0.9"
optional = true

[dependencies.dashmap]
version = "5.1"
optional = true
