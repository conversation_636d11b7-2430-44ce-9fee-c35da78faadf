<script>
function jsfuzzer() {
  try { htmlvar00016.appendChild(htmlvar00017); } catch(e) { }
  try { htmlvar00016.form.setAttribute("novalidate", "novalidate"); } catch(e) { }
  try { htmlvar00017.appendChild(htmlvar00036); } catch(e) { }
  try { svgvar00007.appendChild(htmlvar00008); } catch(e) { }
}
</script>
<body onload=jsfuzzer()>
<form id="htmlvar00007">
<legend id="htmlvar00008">
<output id="htmlvar00016"></output>
</legend>
<link id="htmlvar00017"></link>
<svg>
<path id="svgvar00006">
<animateTransform id="svgvar00007"/>
</path>
<use xlink:href="#svgvar00006">
<table id="htmlvar00036">
<th>
<output form="htmlvar00007">
