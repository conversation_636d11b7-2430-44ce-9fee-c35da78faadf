# Megisto Browser

A Firefox fork focused on clean browsing and enhanced media experience.

## Features

- **Native anti-cookie and anti-popup system**: Built-in, invisible to the user, always enabled
- **Advanced YouTube embed manager**: Replace or extend embedded YouTube players with Video.js wrapper
- **Cross-platform build**: Windows primary development, later Linux/macOS

## Project Structure

```
megisto-browser/
│
├─ browser/                    # Firefox browser source (Gecko fork)
│  └─ extensions/
│     └─ megisto-addon/       # System add-on folder
│        ├─ manifest.json
│        ├─ background.js
│        ├─ content/
│        │  ├─ cookieblocker.js
│        │  └─ videomanager.js
│        └─ rules/
│           └─ default-rules.json
│
├─ docs/                      # Documentation
│  └─ PRD.md                 # Product Requirements Document
│
├─ tools/                     # Build and development tools
│  └─ build.ps1             # PowerShell build helper script
│
└─ tests/                     # Test suites
   └─ playwright/            # Playwright tests
      ├─ test-cookieblocker.spec.js
      └─ test-videomanager.spec.js
```

## Development Environment

### Requirements (Windows)
- Windows 10/11 (64-bit)
- Visual Studio Build Tools (C++ build, MSVC toolchain)
- MozillaBuild (bash-like shell + dependencies)
- Rust (stable)
- Python 3.10+
- Node.js + npm
- Git
- Mercurial (hg) or GitHub Gecko mirror

### Setup
1. Run the setup script: `.\tools\setup-dev-env.ps1`
2. Clone Firefox source: `.\tools\clone-firefox.ps1`
3. Build the browser: `.\tools\build.ps1`

## License

- Firefox (Gecko): MPL 2.0
- Video.js: Apache-2.0
- videojs-youtube: MIT
- Consent-O-Matic: MIT

## References

- [Firefox Source Docs](https://firefox-source-docs.mozilla.org/)
- [System Add-ons](https://firefox-source-docs.mozilla.org/toolkit/mozapps/extensions/addon-manager/SystemAddons.html)
- [Video.js Docs](https://docs.videojs.com/)
- [YouTube IFrame API](https://developers.google.com/youtube/iframe_api_reference)
