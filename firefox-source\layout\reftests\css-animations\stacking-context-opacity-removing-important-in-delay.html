<!DOCTYPE html>
<html class="reftest-wait">
<title>
Removing !important rule during delay phase of animation creates
a stack context for correct style
</title>
<style>
span {
  height: 100px;
  width: 100px;
  position: fixed;
  background: green;
  top: 50px;
}
@keyframes Opacity0 {
  from, to { opacity: 0 }
}
#test {
  width: 100px; height: 100px;
  background: blue;
  animation: Opacity0 100s 100s;
}
</style>
<span></span>
<div id="test"></div>
<script>
window.addEventListener("load", () => {
  var target = document.getElementById("test");
  target.style.setProperty("opacity", "0.1", "important");

  requestAnimationFrame(() => {
    // Now the target opacity style should be 0.1 because of !important rule.

    // Apply 100% opacity without important directive.
    target.style.setProperty("opacity", "1", "");
    requestAnimationFrame(() => {
      // The CSS animation is no longer overridden but it's still in delay
      // phase, so we should create a stacking context for 100% opacity style.
      document.documentElement.classList.remove("reftest-wait");
    });
  });
});
</script>
