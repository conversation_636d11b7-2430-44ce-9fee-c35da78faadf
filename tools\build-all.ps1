# Megisto Browser - Complete Build Automation Script
# Automates the entire build, test, and packaging process

param(
    [string]$Version = "1.0.0",
    [string]$Architecture = "x64",
    [switch]$Clean,
    [switch]$SkipTests,
    [switch]$SkipPackaging,
    [switch]$CreatePortable,
    [switch]$CreateInstaller,
    [switch]$Sign,
    [string]$SigningCert = "",
    [switch]$Verbose
)

$ErrorActionPreference = "Stop"

Write-Host "=== Megisto Browser Complete Build Process ===" -ForegroundColor Green
Write-Host "Version: $Version" -ForegroundColor Yellow
Write-Host "Architecture: $Architecture" -ForegroundColor Yellow
Write-Host "Clean Build: $Clean" -ForegroundColor Yellow
Write-Host "Skip Tests: $SkipTests" -ForegroundColor Yellow
Write-Host "Skip Packaging: $SkipPackaging" -ForegroundColor Yellow

$rootDir = Get-Location
$logDir = Join-Path $rootDir "logs"
$distDir = Join-Path $rootDir "dist"

# Ensure directories exist
@($logDir, $distDir) | ForEach-Object {
    if (-not (Test-Path $_)) {
        New-Item -ItemType Directory -Path $_ -Force | Out-Null
    }
}

$buildLog = Join-Path $logDir "build-all-$(Get-Date -Format 'yyyyMMdd-HHmmss').log"
$buildStartTime = Get-Date

function Write-LogMessage {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry
    Add-Content -Path $buildLog -Value $logEntry
}

function Invoke-BuildStep {
    param(
        [string]$StepName,
        [scriptblock]$ScriptBlock,
        [switch]$ContinueOnError
    )
    
    Write-LogMessage "Starting: $StepName" "INFO"
    $stepStartTime = Get-Date
    
    try {
        & $ScriptBlock
        $stepDuration = (Get-Date) - $stepStartTime
        Write-LogMessage "Completed: $StepName (Duration: $($stepDuration.TotalSeconds)s)" "SUCCESS"
        return $true
    } catch {
        $stepDuration = (Get-Date) - $stepStartTime
        Write-LogMessage "Failed: $StepName - $($_.Exception.Message) (Duration: $($stepDuration.TotalSeconds)s)" "ERROR"
        
        if (-not $ContinueOnError) {
            throw
        }
        return $false
    }
}

# Step 1: Environment Validation
Invoke-BuildStep "Environment Validation" {
    Write-LogMessage "Validating build environment..."
    
    # Check required tools
    $requiredTools = @(
        @{ Name = "Git"; Command = "git" },
        @{ Name = "Python"; Command = "python" },
        @{ Name = "Node.js"; Command = "node" },
        @{ Name = "Rust"; Command = "rustc" }
    )
    
    foreach ($tool in $requiredTools) {
        if (-not (Get-Command $tool.Command -ErrorAction SilentlyContinue)) {
            throw "$($tool.Name) is not installed or not in PATH"
        }
        Write-LogMessage "$($tool.Name) found"
    }
    
    # Check MozillaBuild
    if (-not (Test-Path "C:\mozilla-build")) {
        throw "MozillaBuild not found at C:\mozilla-build"
    }
    Write-LogMessage "MozillaBuild found"
    
    # Check Firefox source
    $firefoxDir = Join-Path $rootDir "firefox-source"
    if (-not (Test-Path $firefoxDir)) {
        throw "Firefox source not found. Run .\tools\clone-firefox.ps1 first"
    }
    Write-LogMessage "Firefox source found"
}

# Step 2: Clean Build (if requested)
if ($Clean) {
    Invoke-BuildStep "Clean Build" {
        Write-LogMessage "Cleaning previous build artifacts..."
        
        $cleanPaths = @(
            "firefox-source\obj-megisto",
            "dist\*",
            "logs\build-*.log"
        )
        
        foreach ($cleanPath in $cleanPaths) {
            $fullPath = Join-Path $rootDir $cleanPath
            if (Test-Path $fullPath) {
                Remove-Item $fullPath -Recurse -Force
                Write-LogMessage "Cleaned: $cleanPath"
            }
        }
    }
}

# Step 3: Build Browser
Invoke-BuildStep "Build Browser" {
    Write-LogMessage "Building Megisto Browser..."
    
    $buildArgs = @()
    if ($Verbose) { $buildArgs += "-Verbose" }
    
    & "$rootDir\tools\build.ps1" @buildArgs
}

# Step 4: Run Tests (if not skipped)
if (-not $SkipTests) {
    Invoke-BuildStep "Run Tests" {
        Write-LogMessage "Running test suite..."
        
        $testArgs = @("-Browser", "megisto")
        if ($Verbose) { $testArgs += "-Verbose" }
        
        & "$rootDir\tools\run-tests.ps1" @testArgs
    } -ContinueOnError
}

# Step 5: Create Packages (if not skipped)
if (-not $SkipPackaging) {
    
    # Create Portable Package
    if ($CreatePortable) {
        Invoke-BuildStep "Create Portable Package" {
            Write-LogMessage "Creating portable package..."
            
            $portableArgs = @(
                "-Version", $Version,
                "-Architecture", $Architecture,
                "-Compress"
            )
            if ($Verbose) { $portableArgs += "-Verbose" }
            
            & "$rootDir\tools\package-portable.ps1" @portableArgs
        }
    }
    
    # Create Installer
    if ($CreateInstaller) {
        Invoke-BuildStep "Create Installer" {
            Write-LogMessage "Creating installer package..."
            
            $installerArgs = @(
                "-Version", $Version,
                "-Architecture", $Architecture
            )
            if ($Sign -and $SigningCert) {
                $installerArgs += "-Sign", "-SigningCert", $SigningCert
            }
            if ($Verbose) { $installerArgs += "-Verbose" }
            
            & "$rootDir\tools\create-installer.ps1" @installerArgs
        }
    }
}

# Step 6: Generate Build Report
Invoke-BuildStep "Generate Build Report" {
    Write-LogMessage "Generating build report..."
    
    $buildDuration = (Get-Date) - $buildStartTime
    $firefoxDir = Join-Path $rootDir "firefox-source"
    $objDir = Join-Path $firefoxDir "obj-megisto"
    $buildDir = Join-Path $objDir "dist"
    
    $report = @{
        BuildInfo = @{
            Version = $Version
            Architecture = $Architecture
            BuildDate = $buildStartTime.ToString("yyyy-MM-dd HH:mm:ss")
            Duration = $buildDuration.ToString()
            LogFile = $buildLog
        }
        Environment = @{
            OS = [System.Environment]::OSVersion.ToString()
            PowerShell = $PSVersionTable.PSVersion.ToString()
            WorkingDirectory = $rootDir
        }
        Artifacts = @()
    }
    
    # Check for build artifacts
    if (Test-Path $buildDir) {
        $browserExe = Join-Path $buildDir "bin\megisto.exe"
        if (Test-Path $browserExe) {
            $exeInfo = Get-Item $browserExe
            $report.Artifacts += @{
                Type = "Browser Executable"
                Path = $browserExe
                Size = "$([math]::Round($exeInfo.Length / 1MB, 2)) MB"
                Created = $exeInfo.CreationTime.ToString()
            }
        }
    }
    
    # Check for packages
    $distFiles = Get-ChildItem $distDir -File -ErrorAction SilentlyContinue
    foreach ($file in $distFiles) {
        $report.Artifacts += @{
            Type = "Package"
            Path = $file.FullName
            Size = "$([math]::Round($file.Length / 1MB, 2)) MB"
            Created = $file.CreationTime.ToString()
        }
    }
    
    # Save report
    $reportPath = Join-Path $distDir "build-report-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
    $report | ConvertTo-Json -Depth 3 | Out-File -FilePath $reportPath -Encoding UTF8
    
    Write-LogMessage "Build report saved: $reportPath"
}

# Final Summary
$totalDuration = (Get-Date) - $buildStartTime
Write-Host "`n=== Build Process Complete ===" -ForegroundColor Green
Write-Host "Total Duration: $($totalDuration.ToString())" -ForegroundColor Cyan
Write-Host "Build Log: $buildLog" -ForegroundColor Cyan

# Show artifacts
$distFiles = Get-ChildItem $distDir -ErrorAction SilentlyContinue
if ($distFiles) {
    Write-Host "`nBuild Artifacts:" -ForegroundColor Yellow
    foreach ($file in $distFiles) {
        $size = if ($file.PSIsContainer) { "Folder" } else { "$([math]::Round($file.Length / 1MB, 2)) MB" }
        Write-Host "  $($file.Name) ($size)" -ForegroundColor White
    }
}

# Show next steps
Write-Host "`nNext Steps:" -ForegroundColor Yellow
Write-Host "1. Test the browser: firefox-source\obj-megisto\dist\bin\megisto.exe" -ForegroundColor White

if ($distFiles | Where-Object { $_.Name -like "*.exe" }) {
    Write-Host "2. Install using the created installer" -ForegroundColor White
}

if ($distFiles | Where-Object { $_.Name -like "*Portable*" }) {
    Write-Host "3. Use the portable version for testing" -ForegroundColor White
}

Write-Host "4. Review the build log for any issues: $buildLog" -ForegroundColor White

Write-LogMessage "Build process completed successfully" "SUCCESS"
