// Copyright (c) 2012 Ecma International.  All rights reserved.
// This code is governed by the BSD license found in the LICENSE file.

/*---
es5id: ********-6-a-242
description: >
    Object.defineProperties - TypeError is thrown if 'O' is an Array,
    'P' is an array index named property that already exists on 'O' is
    accessor property with  [[Configurable]] false, 'desc' is accessor
    descriptor, the [[Set]] field of 'desc' is present, and the
    [[Set]] field of 'desc' is an object and the [[Set]] attribute
    value of 'P' is undefined   (******** step 4.c)
includes: [propertyHelper.js]
---*/

var arr = [];

function set_fun(value) {
  arr.setVerifyHelpProp = value;
}
Object.defineProperty(arr, "1", {
  set: set_fun
});

try {
  Object.defineProperties(arr, {
    "1": {
      set: undefined
    }
  });

  throw new Test262Error("Expected an exception.");
} catch (e) {
  verifyWritable(arr, "1", "setVerifyHelpProp");

  if (!(e instanceof TypeError)) {
    throw new Test262Error("Expected TypeError, got " + e);
  }
}

verifyProperty(arr, "1", {
  enumerable: false,
  configurable: false,
});

reportCompare(0, 0);
