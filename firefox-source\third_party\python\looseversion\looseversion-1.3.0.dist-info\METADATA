Metadata-Version: 2.1
Name: looseversion
Version: 1.3.0
Summary: Version numbering for anarchists and software realists
Project-URL: Homepage, https://github.com/effigies/looseversion
Maintainer-email: <PERSON> <<EMAIL>>
License: PYTHON SOFTWARE FOUNDATION LICENSE VERSION 2
        --------------------------------------------
        
        1. This LICENSE AGREEMENT is between the Python Software Foundation
        ("PSF"), and the Individual or Organization ("Licensee") accessing and
        otherwise using this software ("Python") in source or binary form and
        its associated documentation.
        
        2. Subject to the terms and conditions of this License Agreement, PSF hereby
        grants Licensee a nonexclusive, royalty-free, world-wide license to reproduce,
        analyze, test, perform and/or display publicly, prepare derivative works,
        distribute, and otherwise use Python alone or in any derivative version,
        provided, however, that PSF's License Agreement and PSF's notice of copyright,
        i.e., "Copyright (c) 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010,
        2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022 Python Software Foundation;
        All Rights Reserved" are retained in Python alone or in any derivative version
        prepared by Licensee.
        
        3. In the event Licensee prepares a derivative work that is based on
        or incorporates Python or any part thereof, and wants to make
        the derivative work available to others as provided herein, then
        Licensee hereby agrees to include in any such work a brief summary of
        the changes made to Python.
        
        4. PSF is making Python available to Licensee on an "AS IS"
        basis.  PSF MAKES NO REPRESENTATIONS OR WARRANTIES, EXPRESS OR
        IMPLIED.  BY WAY OF EXAMPLE, BUT NOT LIMITATION, PSF MAKES NO AND
        DISCLAIMS ANY REPRESENTATION OR WARRANTY OF MERCHANTABILITY OR FITNESS
        FOR ANY PARTICULAR PURPOSE OR THAT THE USE OF PYTHON WILL NOT
        INFRINGE ANY THIRD PARTY RIGHTS.
        
        5. PSF SHALL NOT BE LIABLE TO LICENSEE OR ANY OTHER USERS OF PYTHON
        FOR ANY INCIDENTAL, SPECIAL, OR CONSEQUENTIAL DAMAGES OR LOSS AS
        A RESULT OF MODIFYING, DISTRIBUTING, OR OTHERWISE USING PYTHON,
        OR ANY DERIVATIVE THEREOF, EVEN IF ADVISED OF THE POSSIBILITY THEREOF.
        
        6. This License Agreement will automatically terminate upon a material
        breach of its terms and conditions.
        
        7. Nothing in this License Agreement shall be deemed to create any
        relationship of agency, partnership, or joint venture between PSF and
        Licensee.  This License Agreement does not grant permission to use PSF
        trademarks or trade name in a trademark sense to endorse or promote
        products or services of Licensee, or any third party.
        
        8. By copying, installing or otherwise using Python, Licensee
        agrees to be bound by the terms and conditions of this License
        Agreement.
License-File: LICENSE
Classifier: Development Status :: 6 - Mature
Classifier: License :: OSI Approved :: Python Software Foundation License
Classifier: Programming Language :: Python :: 3
Description-Content-Type: text/markdown

# looseversion - Version numbering for anarchists and software realists

A backwards/forwards-compatible fork of `distutils.version.LooseVersion`,
for times when PEP-440 isn't what you need.

The goal of this package is to be a drop-in replacement for the original `LooseVersion`.
It implements an identical interface and comparison logic to `LooseVersion`.
The only major change is that a `looseversion.LooseVersion` is comparable to a
`distutils.version.LooseVersion`, which means tools should not need to worry whether
all dependencies that use LooseVersion have migrated.

If you are simply comparing versions of Python packages, consider moving to
[packaging.version.Version](https://packaging.pypa.io/en/latest/version.html#packaging.version.Version),
which follows [PEP-440](https://peps.python.org/pep-0440).
`LooseVersion` is better suited to interacting with heterogeneous version schemes that
do not follow PEP-440.

## Installation

### From PyPI

```
pip install looseversion
```

### From source

```
git clone https://github.com/effigies/looseversion.git
pip install looseversion/
```

## Usage

```Python
>>> from looseversion import LooseVersion
>>> LooseVersion("1.0.0") < LooseVersion("2.0.0")
True
>>> LooseVersion("1.0.0") < "2"
True
```
