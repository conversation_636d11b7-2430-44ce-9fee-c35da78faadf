/**
 * Megisto Browser - System Add-on Bootstrap
 * Bootstrap script for the Megisto Core system add-on
 */

const { XPCOMUtils } = ChromeUtils.import("resource://gre/modules/XPCOMUtils.jsm");
const { Services } = ChromeUtils.import("resource://gre/modules/Services.jsm");

XPCOMUtils.defineLazyModuleGetter(this, "AddonManager",
  "resource://gre/modules/AddonManager.jsm");

const ADDON_ID = "megisto@browser";
const WEBEXT_STARTUP_SCRIPT = "chrome://megisto/content/webext-startup.js";

let gExtension;

/**
 * Called when the add-on is installed or enabled
 */
function startup(data, reason) {
  console.log("Megisto Core: Starting up...");
  
  // Register chrome manifest
  registerChromeManifest();
  
  // Initialize WebExtension
  initializeWebExtension(data);
  
  // Set up preferences
  setupPreferences();
  
  console.log("Megisto Core: Startup complete");
}

/**
 * Called when the add-on is uninstalled or disabled
 */
function shutdown(data, reason) {
  console.log("Megisto Core: Shutting down...");
  
  if (gExtension) {
    gExtension.shutdown();
    gExtension = null;
  }
  
  console.log("Megisto Core: Shutdown complete");
}

/**
 * Called when the add-on is installed
 */
function install(data, reason) {
  console.log("Megisto Core: Installing...");
}

/**
 * Called when the add-on is uninstalled
 */
function uninstall(data, reason) {
  console.log("Megisto Core: Uninstalling...");
  
  // Clean up preferences if completely removing
  if (reason === ADDON_UNINSTALL) {
    cleanupPreferences();
  }
}

/**
 * Register chrome manifest for resource access
 */
function registerChromeManifest() {
  try {
    const manifestURI = Services.io.newURI("chrome://megisto/content/chrome.manifest");
    Components.manager.addBootstrappedManifestLocation(manifestURI);
  } catch (error) {
    console.error("Megisto Core: Failed to register chrome manifest:", error);
  }
}

/**
 * Initialize the WebExtension component
 */
function initializeWebExtension(data) {
  try {
    const { ExtensionParent } = ChromeUtils.import("resource://gre/modules/ExtensionParent.jsm");
    
    // Create extension instance
    gExtension = ExtensionParent.GlobalManager.getExtension(ADDON_ID);
    
    if (!gExtension) {
      // Load the WebExtension manifest
      const manifestURI = data.resourceURI.resolve("manifest.json");
      gExtension = ExtensionParent.GlobalManager.loadExtension({
        id: ADDON_ID,
        resourceURI: data.resourceURI,
        manifestURI: manifestURI,
        isPrivileged: true,
        isSystem: true
      });
    }
    
    // Start the extension
    if (gExtension && !gExtension.hasStarted) {
      gExtension.startup();
    }
    
  } catch (error) {
    console.error("Megisto Core: Failed to initialize WebExtension:", error);
  }
}

/**
 * Set up default preferences for Megisto Browser
 */
function setupPreferences() {
  const prefs = Services.prefs;
  
  try {
    // Megisto-specific preferences
    if (!prefs.prefHasUserValue("megisto.cookieBlocking.enabled")) {
      prefs.setBoolPref("megisto.cookieBlocking.enabled", true);
    }
    
    if (!prefs.prefHasUserValue("megisto.youtube.enhancement.enabled")) {
      prefs.setBoolPref("megisto.youtube.enhancement.enabled", true);
    }
    
    if (!prefs.prefHasUserValue("megisto.youtube.autoplay")) {
      prefs.setBoolPref("megisto.youtube.autoplay", false);
    }
    
    if (!prefs.prefHasUserValue("megisto.youtube.defaultQuality")) {
      prefs.setStringPref("megisto.youtube.defaultQuality", "720p");
    }
    
    if (!prefs.prefHasUserValue("megisto.youtube.blockRelated")) {
      prefs.setBoolPref("megisto.youtube.blockRelated", true);
    }
    
    // Privacy-focused defaults
    prefs.setBoolPref("privacy.trackingprotection.enabled", true);
    prefs.setBoolPref("privacy.trackingprotection.pbmode.enabled", true);
    prefs.setBoolPref("privacy.donottrackheader.enabled", true);
    
    // Disable telemetry
    prefs.setBoolPref("toolkit.telemetry.enabled", false);
    prefs.setBoolPref("toolkit.telemetry.unified", false);
    prefs.setBoolPref("datareporting.healthreport.uploadEnabled", false);
    prefs.setBoolPref("datareporting.policy.dataSubmissionEnabled", false);
    
    // Disable studies and experiments
    prefs.setBoolPref("app.shield.optoutstudies.enabled", false);
    prefs.setBoolPref("app.normandy.enabled", false);
    
    console.log("Megisto Core: Preferences configured");
    
  } catch (error) {
    console.error("Megisto Core: Failed to setup preferences:", error);
  }
}

/**
 * Clean up preferences on uninstall
 */
function cleanupPreferences() {
  const prefs = Services.prefs;
  
  try {
    // Remove Megisto-specific preferences
    const megistoPrefs = [
      "megisto.cookieBlocking.enabled",
      "megisto.youtube.enhancement.enabled",
      "megisto.youtube.autoplay",
      "megisto.youtube.defaultQuality",
      "megisto.youtube.blockRelated"
    ];
    
    megistoPrefs.forEach(pref => {
      if (prefs.prefHasUserValue(pref)) {
        prefs.clearUserPref(pref);
      }
    });
    
    console.log("Megisto Core: Preferences cleaned up");
    
  } catch (error) {
    console.error("Megisto Core: Failed to cleanup preferences:", error);
  }
}
