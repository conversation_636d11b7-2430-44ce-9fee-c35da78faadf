// @generated
/// Implement `DataProvider<PropertyScriptWithExtensionsV1>` on the given struct using the data
/// hardcoded in this file. This allows the struct to be used with
/// `icu`'s `_unstable` constructors.
///
/// Using this implementation will embed the following data in the binary's data segment:
/// * 28204B[^1] for the singleton data struct
///
/// [^1]: these numbers can be smaller in practice due to linker deduplication
///
/// This macro requires the following crates:
/// * `icu`
/// * `icu_provider`
/// * `zerovec`
#[doc(hidden)]
#[macro_export]
macro_rules! __impl_property_script_with_extensions_v1 {
    ($ provider : ty) => {
        #[clippy::msrv = "1.82"]
        const _: () = <$provider>::MUST_USE_MAKE_PROVIDER_MACRO;
        #[clippy::msrv = "1.82"]
        impl $provider {
            #[doc(hidden)]
            pub const SINGLETON_PROPERTY_SCRIPT_WITH_EXTENSIONS_V1: &'static <icu::properties::provider::PropertyScriptWithExtensionsV1 as icu_provider::DynamicDataMarker>::DataStruct = &icu::properties::provider::ScriptWithExtensionsProperty { trie: icu::collections::codepointtrie::CodePointTrie::from_parts(icu::collections::codepointtrie::CodePointTrieHeader { high_start: 918016u32, shifted12_high_start: 225u16, index3_null_offset: 1104u16, data_null_offset: 2895u32, null_value: 103u32, trie_type: icu::collections::codepointtrie::TrieType::Small }, unsafe { zerovec::ZeroVec::from_bytes_unchecked(b"\0\0@\0{\0\xBB\0\xF3\0\xF3\0\xF3\0\xF3\0\xF3\0\xF3\0\xFA\x007\x01w\x01\xB5\x01\xF5\x01\x18\x02X\x02X\x02\x95\x02X\x02\xA5\x02\xD6\x02\r\x03E\x03\x85\x03\xC5\x03\xF6\x03\"\x04b\x04\x97\x04\xD7\x04\x17\x05W\x05\x97\x05\xC8\x05\xE6\x05&\x06U\x06\x95\x06\xD1\x06\x10\x07N\x07\x8D\x07\xC9\x07\t\x08E\x08\x83\x08\xC1\x08\x01\t=\t}\t\xB9\t\xF9\t4\nt\n\xB4\n\xF3\n3\x0Br\x0B\xB2\x0B\xF2\x0B*\x0C[\x0C\x8B\x0C\x10\x0C*\x0C:\x0CP\x0Cp\x0C\x8F\x0C\xAC\x0C\xCB\x0C\xEB\x0C\xEB\x0C\xF8\x0C\x15\r5\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rG\rg\r\0\0\x10\0 \x000\0@\0P\0`\0p\0{\0\x8B\0\x9B\0\xAB\0\xBB\0\xCB\0\xDB\0\xEB\0\xF3\0\x03\x01\x13\x01#\x01\xF3\0\x03\x01\x13\x01#\x01\xF3\0\x03\x01\x13\x01#\x01\xF3\0\x03\x01\x13\x01#\x01\xFA\0\n\x01\x1A\x01*\x017\x01G\x01W\x01g\x01w\x01\x87\x01\x97\x01\xA7\x01\xB5\x01\xC5\x01\xD5\x01\xE5\x01\xF5\x01\x05\x02\x15\x02%\x02\x18\x02(\x028\x02H\x02X\x02h\x02x\x02\x88\x02X\x02h\x02x\x02\x88\x02\x95\x02\xA5\x02\xB5\x02\xC5\x02X\x02h\x02x\x02\x88\x02\xA5\x02\xB5\x02\xC5\x02\xD5\x02\xD6\x02\xE6\x02\xF6\x02\x06\x03\r\x03\x1D\x03-\x03=\x03E\x03U\x03e\x03u\x03\x85\x03\x95\x03\xA5\x03\xB5\x03\xC5\x03\xD5\x03\xE5\x03\xF5\x03\xF6\x03\x06\x04\x16\x04&\x04\"\x042\x04B\x04R\x04b\x04r\x04\x82\x04\x92\x04\x97\x04\xA7\x04\xB7\x04\xC7\x04\xD7\x04\xE7\x04\xF7\x04\x07\x05\x17\x05'\x057\x05G\x05W\x05g\x05w\x05\x87\x05\x97\x05\xA7\x05\xB7\x05\xC7\x05\xC8\x05\xD8\x05\xE8\x05\xF8\x05\xE6\x05\xF6\x05\x06\x06\x16\x06&\x066\x06F\x06V\x06U\x06e\x06u\x06\x85\x06\x95\x06\xA5\x06\xB5\x06\xC5\x06\xD1\x06\xE1\x06\xF1\x06\x01\x07\x10\x07 \x070\x07@\x07N\x07^\x07n\x07~\x07\x8D\x07\x9D\x07\xAD\x07\xBD\x07\xC9\x07\xD9\x07\xE9\x07\xF9\x07\t\x08\x19\x08)\x089\x08E\x08U\x08e\x08u\x08\x83\x08\x93\x08\xA3\x08\xB3\x08\xC1\x08\xD1\x08\xE1\x08\xF1\x08\x01\t\x11\t!\t1\t=\tM\t]\tm\t}\t\x8D\t\x9D\t\xAD\t\xB9\t\xC9\t\xD9\t\xE9\t\xF9\t\t\n\x19\n)\n4\nD\nT\nd\nt\n\x84\n\x94\n\xA4\n\xB4\n\xC4\n\xD4\n\xE4\n\xF3\n\x03\x0B\x13\x0B#\x0B3\x0BC\x0BS\x0Bc\x0Br\x0B\x82\x0B\x92\x0B\xA2\x0B\xB2\x0B\xC2\x0B\xD2\x0B\xE2\x0B\xF2\x0B\x02\x0C\x12\x0C\"\x0C*\x0C:\x0CJ\x0CZ\x0C[\x0Ck\x0C{\x0C\x8B\x0C\x8B\x0C\x9B\x0C\xAB\x0C\xBB\x0C\xCB\x0C\xCB\x0C\xCB\x0C\xCB\x0C\xDB\x0C\xCB\x0C\xCB\x0C\xCB\x0C\xCB\x0C\xCB\x0C\xEB\x0C\xEB\x0C\xF5\x0C\xEB\x0C\xEB\x0C\x05\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r%\r%\r%\r%\r,\r<\r%\r%\r,\r%\r%\r4\rD\rM\r%\r%\r%\rD\r%\r%\r%\rU\r%\rb\r%\re\ru\ru\ru\ru\ru\r\x7F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x8F\r\x9F\r\xA2\r\xB2\r\xB2\r\xB2\r\xB2\r\xB7\r\xC5\r\xD5\r\xDF\r\xEF\r\xFA\r\n\x0E\x16\x0E&\x0E2\x0EB\x0EB\x0EB\x0EB\x0EB\x0ED\x0EH\x0EH\x0EX\x0E^\x0En\x0En\x0En\x0En\x0En\x0Eu\x0En\x0En\x0Es\x0E\x8F\r\x8F\r\x8F\r\x8F\r\x85\x0E\x95\x0E\x96\x0E\x99\x0E\x99\x0E\xA9\x0E\xB9\x0E\xBB\x0E\xC4\x0E\xD4\x0E\xD4\x0E\xD8\x0E\xD4\x0E\xDA\x0E\xEA\x0EB\x0EB\x0E\xFA\x0E\xFE\x0E\x0E\x0F\x0E\x0F\x0E\x0F\x0F\x0F\x0E\x0F\x11\x0F \x0F \x0F\x10\x0F\xBB\x010\x0FO\x0BO\x0BO\x0B@\x0F@\x0F@\x0F@\x0FC\x0F@\x0F@\x0F@\x0FS\x0FS\x0FS\x0FS\x0Fc\x0Fc\x0Fc\x0Fo\x0F\x7F\x0F\x7F\x0F\x7F\x0F\x87\x0F\x85\x0F\x97\x0F\x97\x0F\x97\x0F\xA7\x0F\xEB\x0C\xEB\x0C\xB7\x0F\xC7\x0F\xD7\x0F\xE7\x0F\xF5\x0F\xF3\0\xF3\0\x05\x10\xF3\0\xF3\0\x11\x10\x1F\x10*\x10\xF3\0\xF3\0\xF3\x003\x10C\x10\xBB\x01\xBB\x01K\x10\xF3\0\xF3\0\xF3\0\xF3\0\xF3\0\xF3\0\xF3\0\xF3\0\xF3\0\xF3\0\xF3\0\xF3\0\xF3\0\xF3\0\xF3\0\xF3\0\x03\x02[\x10\x03\x02\x03\x02[\x10k\x10\x03\x02z\x10\x03\x02\x03\x02\x03\x02\x12\x02\x12\x02\x84\x10\x03\x02\x94\x10\xA4\x10\0\0\xB2\x10\0\0\xC2\x10\xD2\x10\xE0\x10\xEF\x10\xFF\x10\x0F\x11\0\0\0\0\x1F\x11\xBB\x01\xBB\x01/\x11\0\0\0\0?\x11\xFC\x10\x97\0\0\0\xF3\0\xF3\0O\x11\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0_\x11O\x0Bo\x11O\x0B\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x7F\x11\x7F\x11\x7F\x11\x7F\x11\x7F\x11\x7F\x11\x7F\x11\x7F\x11\x7F\x11\x7F\x11\x7F\x11\x7F\x11\x7F\x11\x7F\x11\x7F\x11\x7F\x11\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x8F\x11\0\0\x99\x11\0\0\0\0\0\0\0\0\0\0\0\0\xA9\x11\xA9\x11\xA9\x11\xA9\x11\xA9\x11\xA9\x11\xF3\0\xF3\0\xB9\x11\xB9\x11\xB9\x11\xB9\x11\xB9\x11\xB9\x11\xB9\x11\xC5\x11\xEB\x0C\xEB\x0C\xF5\x0C\xD5\x11\xD5\x11\xD5\x11\xDD\x11\xEC\x11%\r\xFC\x11\x0C\x12\x0C\x12\x0C\x12\x0C\x12X\x02X\x02\0\0\x1C\x12\0\0,\x12;\x12?\x12O\x0BO\x0BO\x12U\x12O\x12O\x12O\x12O\x12O\x12a\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12q\x12O\x0B\x81\x12\x91\x12\x9F\x12\xAF\x12\xBF\x12\xCF\x12\xD0\x12\xD0\x12\xD0\x12\xD0\x12\xD9\x12\xE9\x12\xEA\x12\xEA\x12\xEA\x12\xEA\x12\xEF\x12\xFF\x12\x04\x13\x04\x13\x14\x13\x15\r\x15\r\x15\r\x15\r\x15\x13%\x13\x04\x13\x04\x13%\x13%\x13/\x13\xEA\x12\x15\r\x15\x13%\x13%\x13?\x13\0\0\x15\rO\x13%\x13%\x13%\x13_\x13o\x13\xEA\x12\xEA\x12\x7F\x13\xEA\x12\xEA\x12\xEA\x12\xEA\x12\xEA\x12\x86\x13%\x13\x95\x13\0\0\0\0\0\0\0\0\0\0\0\0%\x13\xA0\x13O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12\0\0\0\0\0\0\0\0\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB0\x13\xB3\x13\xB0\x13\xB0\x13\xB0\x13\xB9\x13\xC9\x13\xC9\x13\xC9\x13\xD9\x13\xD9\x13\xD9\x13\xD9\x13\xD9\x13\xD9\x13\xD9\x13\xD9\x13\xD9\x13\xD9\x13\xD9\x13\xD9\x13\xD9\x13\xD9\x13\xD9\x13\xD9\x13\xDD\x13O\x0BX\x02X\x02\xED\x13X\x02X\x02X\x02\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\x05\x14\x15\x14\0\0?\0\xF3\0\xF3\0\xF3\0\xF3\0\xF3\0%\x14\xF3\0\xF3\0\xF3\x000\x14@\x14O\x0BN\x14^\x14^\x14a\x14q\x14\x81\x14\x81\x14\x81\x14\x89\x14\x99\x14\x99\x14\x99\x14\x99\x14\xA3\x14\x9F\x14&\x06\xB3\x14\xC3\x14\xC3\x14\xC5\x14\xD5\x14\xD5\x14\xE1\x14\x15\r\xF1\x14\x01\x15\x01\x15\x01\x15\x01\x15\x03\x15\x13\x15\xCB\x0C#\x153\x153\x153\x15<\x155\x15L\x15\xCB\x0C\xCB\x0C\\\x15\\\x15\\\x15\\\x15i\x15n\x15~\x15\x87\x15\x96\x15\x9E\x15\x0C\x12\xF3\0\xF3\0\xC7\0\xAE\x15u\ru\ru\ru\ru\r~\x15~\x15\x80\x15\x84\x15\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\x15\r\xBE\x15\x15\r\xCE\x15\x15\r\x15\r\xD9\x15O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12\xE9\x15O\x12O\x12O\x12O\x12O\x12O\x12\xED\x15O\x0BO\x0B\xFD\x15\n\x16\x1E\x03\x17\x16'\x16\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x037\x16\xC4\x05\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x03G\x16\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xC5\x05\xF6\x03\xF6\x03W\x16O\x0BO\x0Bf\x16\xBB\x01_\x11v\x16\0\0\x86\x16\x93\x16\x9C\x16\xAC\x16\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xF6\x03\xB2\x16\xC0\x16\0\0@\0P\0@\0P\0\xCF\x16\xE9\x12\xEA\x12\xD5\x16\x15\r\x15\x13\xE5\x16\xED\x16\xFD\x16\x0C\x17\x1C\x17)\x17!\x17.\x17=\x17=\x17O\x0BO\x0B)\x17)\x17)\x17)\x17)\x17)\x17)\x17@\x17P\x17W\x17W\x17c\x17\x03\x02\x03\x02\x03\x02\x03\x02\x08\x02s\x17\x83\x17O\x0BO\x0B\0\0\0\0\x93\x17O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\xA3\x17\xA6\x17\xB6\x17\xB6\x17\xB6\x17\xC5\x17\xD5\x17\xD9\x17\xE9\x17\xE9\x17\xF5\x17\x05\x18\n\x18\x1A\x18\x1A\x18\x1F\x18/\x181\x18A\x18A\x18M\x18W\x18O\x0BO\x0Bg\x18g\x18g\x18g\x18g\x18w\x18w\x18w\x18\x87\x18\x89\x18\x8D\x18\x9D\x18\x9D\x18\xA9\x18\x9D\x18\xA1\x18\xB9\x18\xB9\x18\xC1\x18\xD1\x18\xD1\x18\xD1\x18\xDD\x18\xED\x18\xED\x18\xFA\x18\xFE\x18\x0C\x19\x1C\x19\x1C\x19\x1C\x19(\x198\x198\x198\x198\x198\x198\x198\x198\x198\x198\x198\x198\x198\x198\x198\x198\x198\x198\x198\x19A\x198\x19B\x19@\x19O\x0BR\x19\xF3\0\xF3\0W\x19O\x0BO\x0BO\x0BO\x0Bg\x19q\x19q\x19{\x19\x8B\x19\x95\x19\xA5\x19\xA5\x19\xB5\x19\xB6\x19\xC5\x19O\x0BO\x0BO\x0B\xD5\x19\xE2\x19\xF2\x19\xF6\x19\x06\x1A\x0C\x1AO\x0BO\x0BO\x0BO\x0B\x1C\x1A\x1C\x1A,\x1A4\x1A,\x1A>\x1A,\x1A,\x1AN\x1AZ\x1Ac\x1Am\x1A|\x1A|\x1A\x8C\x1A\x8C\x1A\x9C\x1A\x9C\x1AO\x0BO\x0B\xAC\x1A\xAC\x1A\xB5\x1A\xC3\x1A\xD3\x1A\xD3\x1A\xD3\x1A\xDD\x1A\xED\x1A\xF7\x1A\x07\x1B\x14\x1B$\x1B2\x1B?\x1BO\x0BO\x0BO\x0BO\x0BO\x0BO\x1BO\x1BO\x1BO\x1BV\x1BO\x0BO\x0BO\x0Bf\x1Bf\x1Bf\x1Bs\x1Bf\x1Bf\x1Bf\x1B\x83\x1B\x93\x1B\x93\x1B\x9B\x1B\x99\x1B\xAB\x1B\xAB\x1B\xB5\x1B\xAB\x1B\xBF\x1BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\xF6\x03\xC8\x05\xCF\x1B\xCF\x1B\xD5\x1B\xE1\x1B\xEF\x1BO\x0BO\x0B;\x16\xFF\x1B\xFF\x1B\x07\x1C\x17\x1C\x17\x1C\x1D\x1CO\x0B-\x1C3\x1CO\x0BO\x0BC\x1CG\x1CO\x0BW\x1C`\x1Cp\x1Cp\x1Cp\x1Cp\x1Cr\x1Cn\x1Cp\x1Cz\x1C\x8A\x1C\x8A\x1C\x8A\x1C\x8A\x1C\x97\x1C\xA7\x1C\xAE\x1C\xAD\x1C\xBE\x1C\xBE\x1C\xBE\x1C\xC9\x1C\xD1\x1C\xE1\x1C\xE1\x1C\xEA\x1C\xFA\x1C\xFA\x1C\xFA\x1C\xFA\x1C\xFA\x1C\xFA\x1Cx\n\n\x1D\x1A\x1D(\x1D\x1A\x1D\x1A\x1D6\x1DO\x0BO\x0BO\x0BF\x1DU\x1Dd\x1Dt\x1Dt\x1Dt\x1Dy\x1Dz\x1D\x8A\x1D\x96\x1D\x9D\x1D\xAC\x1D\xB9\x1D\xC6\x1D\xD3\x1D\xDB\x1D\xEB\x1D\xFB\x1D\xFB\x1D\x05\x1E\x14\x1E \x1E&\x1EO\x0B6\x1E6\x1E6\x1E6\x1E6\x1E:\x1EH\x1EO\x0BX\x1EX\x1EX\x1EX\x1E`\x1E^\x1EO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0Bp\x1Ep\x1Ep\x1Ez\x1Ep\x1Er\x1EO\x0BO\x0B\x8A\x1E\x8A\x1E\x8A\x1E\x8A\x1E\x95\x1E\x90\x1Eq\x0EO\x0B\xA5\x1E\xA5\x1E\xA5\x1E\xAB\x1E\xAB\x1E\xCB\x0C\xBB\x1EO\x0B\xCB\x1E\xD0\x1E\xDD\x1E\xCB\x1E\xE2\x1EO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\xF2\x1E\xF2\x1E\xF2\x1E\xF6\x1EO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\x06\x1F\x06\x1F\x06\x1F\x06\x1F\x06\x1F\x13\x1F#\x1F/\x1F7\x1FA\x1FL\x1F\\\x1FO\x0BO\x0BO\x0BO\x0Bl\x1Fv\x1Fv\x1Fl\x1F\x81\x1FO\x0B\x91\x1F\x91\x1F\x91\x1F\x91\x1F\x99\x1F\xA9\x1F\xA9\x1F\xA9\x1F\xA9\x1F\xA9\x1F\xB6\x1F\x8F\r\xC6\x1F\xC6\x1F\xC6\x1F\xCD\x1F\xDD\x1FO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\xED\x1F\xED\x1F\xFB\x1F\xF3\x1F\x0B \x15 \x15 \r \x1F \x15 \x18 / / - 7 @ O\x0BO\x0BO\x0BO\x0BP [ [ d s \x83 \x93 \x9D \x9E \xAB \xBB O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\xCB \xD2 \xE2 \xF1 \xE2 \xF6 \xE2 \x04!O\x0BO\x0BO\x0BO\x0BO\x0B\x14!$!4!$!B!R!R!R!R!R!R!R!R!R!R!R!R!R!R!R!R!R!R!R!R!R!R!R!R!R!R!R!R!R!R!R!R!X!O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BR!R!R!R!R!R!S!]!R!R!R!R!R!R!R!R!R!R!R!R!^!O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0Bn!n!n!n!n!n!{!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x95!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x8B!\x90!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xA5!\xAE!O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\xBE!\xBE!\xBE!\xC4!O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\xFD\x13\x04\x14\xD4!\xD5!\xDA!\xEA!\xEA!\xEA!\xEA!\xEB!\xF0!\0\"\x02\"\n\"\x1A\"\x1A\"\x1A\"\x1A\"$\"4\"<\"D\"\x1A\"O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BT\"T\"T\"Z\"O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0Bj\"j\"j\"j\"j\"o\"O\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\x7F\"\x7F\"\x7F\"\x7F\"\x84\"\x7F\"\x7F\"\x7F\"\x93\"\x7F\"O\x0BO\x0BO\x0BO\x0B\xA3\"\xB3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xCB\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xC3\"\xDB\"\xDB\"\xDB\"\xDB\"\xDB\"\xDB\"\xDB\"\xDB\"\xDB\"\xDB\"\xDB\"\xDB\"\xDB\"\xDB\"\xDB\"\xDB\"\xE5\"O\x0B\xEB\"\xCA\"O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\xFB\"\x0B#\xD0\x12\xD0\x12\xD0\x12\xD0\x12\xD0\x12\xD0\x12\xD0\x12\xD0\x12\xD0\x12\xD0\x12\xD0\x12\xD0\x12\xD0\x12\xD0\x12\xD0\x12\xD0\x12\xD0\x12\x1B#)#O\x0B9#E#U#U#U#U#U#U#U#U#U#U#U#U#U#U#U#Y#O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0Bi#i#i#i#i#i#n#l#p#\x80#\x90#O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0_\x11\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xA0#O\x0BO\x0BO\x0BO\x0B\xBB\x01\xBB\x01\xB0#\xBB\x01\xB7#\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xA0#O\x0BO\x0BO\x0B\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xC7#\0\0\0\0\xD7#\0\0\0\0\0\0\xE0#\xEA#\xF7#\0\0\x03$\0\0\0\0\0\0o\x11O\x0B\x03\x02\x03\x02\x03\x02\x03\x02\x13$O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\0\0\xA0#\0\0\xA0#\0\0\0\0\0\0\0\0\0\0\x05\x17%\x13#$O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\0\0\0\0\0\0\0\0\0\0\xE0\x10\0\0\0\0\0\x003$C$Q$\x92\x16\0\0\0\0\0\0^$k$\0\0\x99\x16y$\x88$\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x92$\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x8C$\0\0\0\0\0\0\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA2$\xA6$\xB2$\xBC$O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\xF3\0\xCC$\xDB$O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\xEB$\xF3$\x01%X\x02X\x02X\x02\x11%O\x0B\x1F%O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B/%/%2%1%5%O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BE%F%O\x0BV%V%V%\\%O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0Bl%l%r%O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\x82%\x82%\x87%O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\x97%&\r\xA7%\xA7%\xA7%\xA7%\xA7%\xA7%\xA7%\xA7%\xA7%\xA7%\xA7%\xA7%\xB2%\xBB%O\x0BO\x0B\xCB%\xCB%\xCB%\xCB%\xCF%\xD1%O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\xC0\x16\0\0\0\0\0\0\xE1%O\x0BO\x0BO\x0BO\x0B\xC0\x16\0\0\0\0?\x12O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\xAD\x16\xF6\x03\xF0%\xFD%\x0B&\x1B&)&1&A&L&[&L&O\x0BO\x0BO\x0Bi&O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\0\0\0\0y&\0\0\0\0\0\0\0\0\0\0\0\0\xA0#\xFF\x10\xC0\x16\xC0\x16\xC0\x16\0\0\xC7#\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0?\x12O\x0BO\x0BO\x0Bi\x11\0\0\x89&\0\0\0\0y&\x99&\xA9&\xC7#O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xB9&s\x17s\x17\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xBA&\0\0\0\0\0\0\0\0\0\0_\x11y&\x1F\x11y&\0\0\0\0\0\0\xC5&_\x11\0\0\0\0\xC5&\0\0?\x12y&\xCB&O\x0BO\x0BO\x0B\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xA0#?\x12s\x17\xDB&\0\0\0\0\0\0%$\x8B$_\x11\x99&\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x93\x16\0\0\0\0\0\0\0\0\0\0_\x11O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x0BO\x0BO\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12\xED\x15O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12\xE9\x15O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12\xB3\"O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12\xEB&O\x12O\x12O\x12O\x12O\x12\xE9\x15O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12\xEC\x15O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x12O\x0BO\x0BO\x0BO\x0BO\x0B\x1E\x11O\x0B\0\0\0\0\0\0\0\0\0\0\0\0O\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0BO\x0B\xBB\x01\xBB\x01\xBB\x01\xBB\x01\xBB\x01\xBB\x01\xBB\x01\xBB\x01\xBB\x01\xBB\x01\xBB\x01\xBB\x01\xBB\x01\xBB\x01\xBB\x01O\x0By\0\x91\0\xB1\0\xD1\0\xF1\0\x11\x011\x01Q\x01q\x01\x91\x01\xB1\x01\xC9\x01\xE9\x01\t\x02)\x02I\x02i\x02\x82\x02\xA0\x02\x82\x02\xC0\x02\xD0\x02\xF0\x02\x10\x030\x03P\x03p\x03p\x03p\x03p\x03p\x03p\x03t\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03\x94\x03\x94\x03\xAC\x03\xCA\x03\xEA\x03\n\x04*\x04*\x04*\x04*\x04*\x04*\x04*\x04*\x04*\x04*\x04*\x040\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04`\x04z\x04\x98\x04\xB8\x04\xD8\x04\xF8\x04\x18\x058\x05X\x05x\x05\x98\x05\xB2\x05\xD2\x05\xF2\x05\x12\x062\x06R\x06r\x06\x92\x06\xAD\x06\xCD\x06\xD4\x06\xF4\x06P\x04P\x04P\x04P\x04\t\x07)\x07)\x07D\x07)\x07)\x07)\x07)\x07)\x07J\x07j\x07\x86\x07P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04\x96\x07P\x04P\x04P\x04\xB6\x07\xD3\x07\xEC\x07\x08\x08(\x08(\x08(\x08(\x08(\x08(\x08(\x08(\x08)\x08(\x08I\x08\\\x08P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04m\x08\x8D\x08\xA4\x08P\x04P\x04P\x04P\x04\xC4\x08P\x04P\x04P\x04P\x04P\x04P\x04\xE4\x08\xF9\x08\x19\t9\tY\to\t\x8F\t\xA7\tP\x04\xB7\t\xD7\t\xEE\t\x01\n!\nA\nP\x04Z\nz\n\x9A\n\xBA\n\x82\x02\xCD\n\xED\n\x08\x0BP\x04P\x04p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03(\x0Bp\x03p\x03p\x03p\x03p\x03p\x03p\x038\x0BW\x0Bp\x03p\x03p\x03p\x03p\x03p\x03p\x03m\x0Bp\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03x\x0Bp\x03\x97\x0BP\x04P\x04P\x04P\x04p\x03\x9B\x0BP\x04P\x04p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03p\x03\xBB\x0Bp\x03p\x03p\x03p\x03p\x03p\x03p\x03\xD0\x0BP\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04P\x04\xF0\x0B") }, unsafe { zerovec::ZeroVec::from_bytes_unchecked(b"\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\0\0\0\0\0\0\0\0\0\0\0\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x19\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x04\0\0\0\0\x19\0\0\0\0\0\0\0\0\0\0\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\0\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\0\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\0\0\0\0\0\0\x01\x04\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x02\x04\0\0\x02\x04\x02\x04\x02\x04\0\0\x03\x04\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x04\x04\0\0\x02\x04\0\0\0\0\0\0\0\0\0\0\0\0\x19\0\x19\0\x19\0\x19\0\x19\0\0\0\0\0\0\0\0\0\0\0\x05\0\x05\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x05\x08\x06\x08\x07\x08\x08\x08\t\x08\n\x08\x0B\x08\x0C\x08\r\x08\x0E\x08\x0F\x08\x10\x08\x11\x08\x12\x08\x13\x08\x01\0\x12\x08\x14\x08\x01\0\x15\x08\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x16\x08\x01\0\x01\0\x17\x08\x18\x08\x16\x08\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x19\x08\x16\x08\x01\0\x1A\x08\x1B\x08\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x1C\x08\x01\0\x01\0\x1C\x08\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x1D\x08\x01\0\x01\0\x01\0\x01\0\x01\0\x1E\x08\x01\0\x01\0\x01\0\x01\0\x1F\x08\x1F\x08\x1F\x08\x1F\x08\x1F\x08\x1F\x08\x1F\x08\x1F\x08\x1F\x08\x1F\x08\x1F\x08\x1F\x08\x1F\x08\x0E\0\x0E\0\x0E\0\x0E\0 \x04!\x0C\x0E\0\x0E\0g\0g\0\x0E\0\x0E\0\x0E\0\x0E\0\0\0\x0E\0g\0g\0g\0g\0\x0E\0\0\0\x0E\0\0\0\x0E\0\x0E\0\x0E\0g\0\x0E\0g\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0g\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\"\x0C#\x0C$\x08$\x08#\x0C\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0g\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0g\0g\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0%\x0C\x03\0g\0g\0\x03\0\x03\0\x03\0g\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0g\0g\0g\0g\0g\0g\0g\0g\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0g\0g\0g\0g\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x02\0\x02\0\x02\0\x02\0\x02\0\0\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0&\x04\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0&\x04'\x0C\x02\0\x02\0(\x04\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0)\x04\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0*\x08*\x08*\x08*\x08*\x08*\x08*\x08*\x08*\x08*\x08*\x08\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0+\x0C+\x0C+\x0C+\x0C+\x0C+\x0C+\x0C+\x0C+\x0C+\x0C\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0*\x08\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0,\x0C\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\0\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0g\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0g\0g\0\"\0\"\0\"\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0W\0g\0g\0W\0W\0W\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0g\0g\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0~\0g\0T\0T\0T\0T\0T\0T\0T\0T\0T\0T\0T\0T\0T\0T\0T\0T\0T\0T\0T\0T\0T\0T\0T\0T\0T\0T\0T\0T\0g\0g\0T\0g\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0\"\0g\0g\0g\0g\0g\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0g\0\x02\0\x02\0g\0g\0g\0g\0g\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\0\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0-\x08.\x08\x01\0\x01\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0/\x040\x041\x0C1\x0C1\x0C1\x0C1\x0C1\x0C1\x0C1\x0C1\x0C1\x0C\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\x04\0\x04\0\x04\0\x04\0g\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0g\0g\0\x04\0\x04\0g\0g\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0g\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0g\0\x04\0g\0g\0g\0\x04\0\x04\0\x04\0\x04\0g\0g\0\x04\0\x04\0\x04\0\x04\0\x04\0g\0g\0\x04\0\x04\0g\0g\0\x04\0\x04\0\x04\0\x04\0g\0g\0g\0g\0g\0g\0g\0g\0\x04\0g\0g\0g\0g\0\x04\0\x04\0g\0\x04\0\x04\0\x04\0\x04\0\x04\0g\0g\x002\x0C2\x0C2\x0C2\x0C2\x0C2\x0C2\x0C2\x0C2\x0C2\x0C\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0g\0\x10\0\x10\0\x10\0g\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0g\0g\0g\0g\0\x10\0\x10\0g\0g\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0g\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0g\0\x10\0\x10\0g\0\x10\0\x10\0g\0\x10\0\x10\0g\0g\0\x10\0g\0\x10\0\x10\0\x10\0g\0g\0g\0g\0\x10\0\x10\0g\0g\0\x10\0\x10\0\x10\0g\0g\0g\0\x10\0g\0g\0g\0g\0g\0g\0g\0\x10\0\x10\0\x10\0\x10\0g\0\x10\0g\0g\0g\0g\0g\0g\0g\x003\x0C3\x0C3\x0C3\x0C3\x0C3\x0C3\x0C3\x0C3\x0C3\x0C\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x0F\0\x0F\0\x0F\0g\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0g\0\x0F\0\x0F\0\x0F\0g\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0g\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0g\0\x0F\0\x0F\0g\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0g\0g\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0g\0\x0F\0\x0F\0\x0F\0g\0\x0F\0\x0F\0\x0F\0g\0g\0\x0F\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x0F\0\x0F\0\x0F\0\x0F\0g\0g\x004\x0C4\x0C4\x0C4\x0C4\x0C4\x0C4\x0C4\x0C4\x0C4\x0C\x0F\0\x0F\0g\0g\0g\0g\0g\0g\0g\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0\x0F\0g\0\x1F\0\x1F\0\x1F\0g\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0g\0g\0\x1F\0\x1F\0g\0g\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0g\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0g\0\x1F\0\x1F\0g\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0g\0g\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0g\0g\0\x1F\0\x1F\0g\0g\0\x1F\0\x1F\0\x1F\0g\0g\0g\0g\0g\0g\0g\0\x1F\0\x1F\0\x1F\0g\0g\0g\0g\0\x1F\0\x1F\0g\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0g\0g\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0\x1F\0g\0g\0g\0g\0g\0g\0g\0g\0#\0#\0g\0#\0#\0#\0#\0#\0#\0g\0g\0g\0#\0#\0#\0g\0#\0#\0#\0#\0g\0g\0g\0#\0#\0g\0#\0g\0#\0#\0g\0g\0g\0#\0#\0g\0g\0g\0#\0#\0#\0g\0g\0g\0#\0#\0#\0#\0#\0#\0#\0#\0#\0#\0#\0#\0g\0g\0g\0g\0#\0#\0#\0g\0g\0g\0#\0#\0#\0g\0#\0#\0#\0#\0g\0g\0#\0g\0g\0g\0g\0g\0g\0#\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\x005\x0C5\x0C5\x0C5\x0C5\x0C5\x0C5\x0C5\x0C5\x0C5\x0C5\x0C5\x0C5\x0C5\x0C#\0#\0#\0#\0#\0#\0#\0g\0g\0g\0g\0g\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0g\0$\0$\0$\0g\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0g\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0g\0g\0$\0$\0$\0$\0$\0g\0$\0$\0$\0g\0$\0$\0$\0$\0g\0g\0g\0g\0g\0g\0g\0$\0$\0g\0$\0$\0$\0g\0g\0$\0g\0g\0$\0$\0$\0$\0g\0g\0$\0$\0$\0$\0$\0$\0$\0$\0$\0$\0g\0g\0g\0g\0g\0g\0g\0$\0$\0$\0$\0$\0$\0$\0$\0$\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0g\0\x15\0\x15\0\x15\0g\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0g\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0\x15\0g\0\x15\0\x15\0\x15\0\x15\0\x15\0g\0g\0\x15\0\x15\0\x15\0\x15\0\x15\0g\0\x15\0\x15\0\x15\0g\0\x15\0\x15\0\x15\0\x15\0g\0g\0g\0g\0g\0g\0g\0\x15\0\x15\0g\0g\0g\0g\0g\0g\0\x15\0\x15\0g\0\x15\0\x15\0\x15\0\x15\0g\0g\x006\x0C6\x0C6\x0C6\x0C6\x0C6\x0C6\x0C6\x0C6\x0C6\x0Cg\0\x15\0\x15\0\x15\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0g\0\x1A\0\x1A\0\x1A\0g\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0g\0\x1A\0\x1A\0\x1A\0g\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0g\0g\0g\0g\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0g\0g\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0\x1A\0g\0!\0!\0!\0g\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0g\0g\0g\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0g\0!\0!\0!\0!\0!\0!\0!\0!\0!\0g\0!\0g\0g\0!\0!\0!\0!\0!\0!\0!\0g\0g\0g\0!\0g\0g\0g\0g\0!\0!\0!\0!\0!\0!\0g\0!\0g\0!\0!\0!\0!\0!\0!\0!\0!\0g\0g\0g\0g\0g\0g\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0g\0g\0!\0!\0!\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0g\0g\0g\0g\0\0\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x18\0\x18\0g\0\x18\0g\0\x18\0\x18\0\x18\0\x18\0\x18\0g\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0g\0\x18\0g\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0g\0g\0\x18\0\x18\0\x18\0\x18\0\x18\0g\0\x18\0g\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0g\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0\x18\0g\0g\0\x18\0\x18\0\x18\0\x18\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0g\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0g\0g\0g\0g\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0g\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0g\0'\0'\0'\0'\0'\0'\0'\0\0\0\0\0\0\0\0\0'\0'\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\x007\x0C7\x0C7\x0C7\x0C7\x0C7\x0C7\x0C7\x0C7\x0C7\x0C\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0g\0\x0C\0g\0g\0g\0g\0g\0\x0C\0g\0g\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\08\x04\x0C\0\x0C\0\x0C\0\x0C\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0g\0\x0B\0\x0B\0\x0B\0\x0B\0g\0g\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0g\0\x0B\0g\0\x0B\0\x0B\0\x0B\0\x0B\0g\0g\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0g\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0g\0g\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0g\0g\0g\0g\0g\0g\0\x06\0\x06\0\x06\0\x06\0\x06\0\x06\0\x06\0\x06\0\x06\0\x06\0\x06\0\x06\0\x06\0\x06\0\x06\0\x06\0g\0g\0\x06\0\x06\0\x06\0\x06\0\x06\0\x06\0g\0g\0(\0(\0(\0(\0(\0(\0(\0(\0(\0(\0(\0(\0(\0(\0(\0(\0\x1D\0\x1D\0\x1D\0\x1D\0\x1D\0\x1D\0\x1D\0\x1D\0\x1D\0\x1D\0\x1D\0\x1D\0\x1D\0\x1D\0\x1D\0\x1D\0g\0g\0g\0 \0 \0 \0 \0 \0 \0 \0 \0 \0 \0 \0 \0 \0 \0 \0 \09\x049\x049\x04 \0 \0 \0 \0 \0 \0 \0 \0 \0g\0g\0g\0g\0g\0g\0g\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0g\0g\0g\0g\0g\0g\0g\0g\0g\0*\0+\0+\0+\0+\0+\0+\0+\0+\0+\0+\0+\0+\0+\0+\0+\0+\0:\x04:\x04g\0g\0g\0g\0g\0g\0g\0g\0g\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0-\0-\0-\0-\0-\0-\0-\0-\0-\0-\0-\0-\0-\0g\0-\0-\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x17\0\x17\0\x17\0\x17\0\x17\0\x17\0\x17\0\x17\0\x17\0\x17\0\x17\0\x17\0\x17\0\x17\0\x17\0\x17\0g\0g\0g\0g\0g\0g\0\x1B\0\x1B\0;\x04;\x04\x1B\0;\x04\x1B\0\x1B\0\x1B\0\x1B\0\x1B\0\x1B\0\x1B\0\x1B\0\x1B\0\x1B\0g\0g\0g\0g\0g\0g\0\x1B\0\x1B\0\x1B\0\x1B\0\x1B\0\x1B\0\x1B\0\x1B\0\x1B\0\x1B\0\x1B\0\x1B\0\x1B\0\x1B\0\x1B\0\x1B\0g\0g\0g\0g\0g\0g\0g\0(\0(\0(\0(\0(\0(\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\x000\x000\x000\x000\x000\x000\x000\x000\x000\x000\x000\x000\x000\x000\x000\x000\0g\0g\0g\0g\x000\0g\0g\0g\x000\x000\x000\x000\x000\x000\x000\x000\x000\x000\x000\x000\x004\x004\x004\x004\x004\x004\x004\x004\x004\x004\x004\x004\x004\x004\x004\x004\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0;\0;\0;\0;\0;\0;\0;\0;\0;\0;\0;\0;\0;\0;\0;\0;\0g\0g\0g\0g\0g\0g\0;\0;\0;\0;\0;\0;\0;\0;\0;\0;\0;\0g\0g\0g\0;\0;\x007\x007\x007\x007\x007\x007\x007\x007\x007\x007\x007\x007\x007\x007\x007\x007\0g\0g\x007\x007\0j\0j\0j\0j\0j\0j\0j\0j\0j\0j\0j\0j\0j\0j\0j\0j\0g\0g\0j\0j\0j\0j\0j\0j\0j\0j\0j\0j\0g\0g\0g\0g\0g\0g\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0g\0>\0>\0>\0>\0>\0>\0>\0>\0>\0>\0>\0>\0>\0>\0>\0>\0g\0>\0>\0q\0q\0q\0q\0q\0q\0q\0q\0q\0q\0q\0q\0q\0q\0q\0q\0?\0?\0?\0?\0?\0?\0?\0?\0?\0?\0?\0?\0?\0?\0?\0?\0g\0g\0g\0g\0g\0g\0g\0g\0?\0?\0?\0?\0R\0R\0R\0R\0R\0R\0R\0R\0R\0R\0R\0R\0R\0R\0R\0R\0g\0g\0g\0R\0R\0R\0R\0R\0m\0m\0m\0m\0m\0m\0m\0m\0m\0m\0m\0m\0m\0m\0m\0m\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0g\0g\0g\0g\0g\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0\x0C\0g\0g\0\x0C\0\x0C\0\x0C\0q\0q\0q\0q\0q\0q\0q\0q\0g\0g\0g\0g\0g\0g\0g\0g\0<\x08=\x08<\x08>\x04=\x08?\x08?\x08@\x08?\x08@\x08A\x08=\x08@\x08@\x08=\x08=\x08@\x08?\x04=\x08=\x08=\x08=\x08=\x08=\x08=\x08B\x04?\x04=\x04=\x04?\x08=\x04=\x04C\x04D\x04E\x08?\x04?\x04F\x04D\x08D\x08G\x04g\0g\0g\0g\0g\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x08\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x0E\0\x0E\0\x0E\0\x19\0\x19\0\x19\0\x19\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x08\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x0E\0\x1C\x08\x1C\x08\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0H\x08\x01\0I\x08\x01\0\x01\0\x01\0\x01\0\x01\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0g\0g\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0g\0g\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0g\0\x0E\0g\0\x0E\0g\0\x0E\0g\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0g\0g\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0g\0\x0E\0\x0E\0\x0E\0g\0g\0\x0E\0\x0E\0\x0E\0g\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x01\0\x01\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0J\x04\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0K\x04\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0L\x04\0\0\0\0M\x04\0\0\0\0\0\0\0\0\0\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x19\0g\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x19\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0g\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0g\0g\0g\0\0\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0N\x08g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\x0E\0\0\0\0\0\0\0\x19\0\x19\0\0\0\0\0\0\0\0\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\0\0\0\0\0\0g\0g\0g\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0g\0g\0g\0g\0g\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0g\0g\0g\0g\0g\0.\0.\0.\0.\0.\0.\0.\0.\0.\0.\0.\0.\0.\0.\0.\0.\0\0\0\0\0\0\0\0\0g\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\08\08\08\08\08\08\08\08\08\08\08\08\08\08\08\08\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0g\0g\0g\0g\0g\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0\x07\0<\0<\0<\0<\0<\0<\0<\0<\0<\0<\0<\0<\0<\0<\0<\0<\0g\0g\0g\0g\0g\0g\0g\0<\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0<\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0g\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0O\x04\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0P\x04Q\x04\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0R\x04\0\0\0\0\0\0S\x04\0\0T\x04\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0g\0g\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0g\0\x11\0\x11\0\x11\0\x11\0\x11\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0U\x04U\x04U\x04U\x04U\x04U\x04U\x04U\x04U\x04U\x04U\x04U\x04U\x04U\x04U\x04U\x04\0\0V\x04W\x04X\x04\0\0\x11\0Y\x04\x11\0Z\x04Z\x04[\x04[\x04\\\x04\\\x04\\\x04\\\x04\0\0X\x04\\\x04\\\x04\\\x04\\\x04\\\x04\\\x04\\\x04\\\x04X\x04X\x04X\x04X\x04\0\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0]\x08]\x08]\x08]\x08\x12\0\x12\0X\x04^\x04^\x04^\x04^\x04^\x04\0\0X\x04\x11\0\x11\0\x11\0\x11\0_\x04_\x04Y\x04Y\x04g\0\x14\0\x14\0\x14\0\x14\0\x14\0\x14\0\x14\0\x14\0\x14\0\x14\0\x14\0\x14\0\x14\0\x14\0\x14\0\x14\0g\0g\0^\x08^\x08^\x04^\x04\x14\0\x14\0\x14\0^\x04\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\\\x04^\x04\x16\0\x16\0\x16\0g\0g\0g\0g\0g\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0g\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0g\0Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04g\0g\0g\0g\0g\0g\0g\0g\0g\0U\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\0\0Y\x04\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04\0\0\0\0\0\0\0\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04Y\x04\0\0)\0)\0)\0)\0)\0)\0)\0)\0)\0)\0)\0)\0)\0)\0)\0)\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x83\0\x83\0\x83\0\x83\0\x83\0\x83\0\x83\0\x83\0\x83\0\x83\0\x83\0\x83\0\x83\0\x83\0\x83\0\x83\0c\0c\0c\0c\0c\0c\0c\0c\0c\0c\0c\0c\0c\0c\0c\0c\0g\0g\0g\0g\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0#\x0C\x82\0\x82\0\x82\0\x82\0\x82\0\x82\0\x82\0\x82\0\x82\0\x82\0\x82\0\x82\0\x82\0\x82\0\x82\0\x82\0g\0g\0g\0g\0g\0g\0g\0g\0`\x04`\x04`\x04`\x04`\x04`\x04`\x04`\x04\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\0\0\0\0\0\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0g\0g\0\x19\0\x19\0g\0\x19\0g\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0g\0g\0g\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0:\0:\0:\0:\0:\0:\0:\0:\0:\0:\0:\0:\0:\0:\0:\0:\0g\0g\0g\0a\x04a\x04a\x04b\x04b\x04b\x04c\x04c\x04d\x04c\x04g\0g\0g\0g\0g\0g\0Z\0Z\0Z\0Z\0Z\0Z\0Z\0Z\0Z\0Z\0Z\0Z\0Z\0Z\0Z\0Z\0g\0g\0g\0g\0g\0g\0g\0g\0o\0o\0o\0o\0o\0o\0o\0o\0o\0o\0o\0o\0o\0o\0o\0o\0g\0g\0g\0g\0g\0g\0g\0g\0o\0o\0\n\0e\x0C\n\0f\x0C\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0O\0O\0O\0O\0O\0O\0O\0O\0O\0O\0O\0O\0O\0O\0O\0O\0g\x04O\0n\0n\0n\0n\0n\0n\0n\0n\0n\0n\0n\0n\0n\0n\0n\0n\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0n\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0g\0g\0g\0N\0N\0N\0N\0N\0N\0N\0N\0N\0N\0N\0N\0N\0N\0N\0N\0g\0h\x04N\0N\0N\0N\0N\0N\0N\0N\0N\0N\0g\0g\0g\0g\0N\0N\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0\x1C\0g\0B\0B\0B\0B\0B\0B\0B\0B\0B\0B\0B\0B\0B\0B\0B\0B\0g\0g\0g\0g\0g\0g\0g\0g\0g\0B\0B\0B\0B\0B\0B\0B\0B\0B\0B\0g\0g\0B\0B\0B\0B\0\x7F\0\x7F\0\x7F\0\x7F\0\x7F\0\x7F\0\x7F\0\x7F\0\x7F\0\x7F\0\x7F\0\x7F\0\x7F\0\x7F\0\x7F\0\x7F\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x7F\0\x7F\0\x7F\0\x7F\0\x7F\0s\0s\0s\0s\0s\0s\0s\0s\0s\0s\0s\0s\0s\0s\0s\0s\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0g\0g\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x19\0\x19\0\x19\0\x19\0\x19\0\x0E\0\x19\0\x19\0\x19\0\x19\0\0\0\0\0g\0g\0g\0g\0\x12\0\x12\0\x12\0\x12\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0g\0g\0g\0g\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0g\0g\0g\0g\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0g\0g\0g\0g\0g\0g\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x03\0\x03\0\x03\0\x03\0\x03\0g\0g\0g\0g\0g\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0g\0\x13\0\x13\0\x13\0\x13\0\x13\0g\0\x13\0g\0\x13\0\x13\0g\0\x13\0\x13\0g\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x13\0\x02\0\x02\0\x02\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0i\x04i\x04\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0g\0g\0g\0g\0g\0g\0g\0\x02\0\x02\0j\x0C\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0j\x0C\x02\0\x02\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x08\0\x08\0\0\0\0\0\0\0\0\0\0\0X\x04X\x04\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0g\0\0\0\0\0\0\0\0\0g\0g\0g\0g\0\x02\0\x02\0\x02\0\x02\0\x02\0g\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0g\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\\\x04\\\x04\\\x04\\\x04\\\x04\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0^\x04^\x04g\0g\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0g\0g\0\x12\0\x12\0\x12\0\x12\0\x12\0\x12\0g\0g\0\x12\0\x12\0\x12\0g\0g\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\0\0\0\0\0\0\0\0\0\0g\0g\x001\x001\x001\x001\x001\x001\x001\x001\x001\x001\x001\x001\0g\x001\x001\x001\x001\x001\x001\x001\x001\x001\x001\x001\x001\x001\x001\x001\x001\0g\x001\x001\0g\x001\x001\x001\x001\x001\x001\x001\x001\x001\x001\x001\x001\x001\x001\0g\0g\0g\0g\0g\0k\x04k\x04l\x04g\0g\0g\0g\0m\x04m\x04m\x04m\x04m\x04m\x04m\x04m\x04m\x04m\x04m\x04m\x04m\x04m\x04m\x04m\x04g\0g\0g\0l\x04l\x04l\x04l\x04l\x04l\x04l\x04l\x04l\x04\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0g\0g\0g\0\x0E\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x01\0g\0g\0k\0k\0k\0k\0k\0k\0k\0k\0k\0k\0k\0k\0k\0k\0k\0k\0g\0g\0g\0h\0h\0h\0h\0h\0h\0h\0h\0h\0h\0h\0h\0h\0h\0h\0h\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0n\x08n\x04n\x04n\x04n\x04n\x04n\x04n\x04n\x04n\x04n\x04n\x04n\x04n\x04n\x04n\x04g\0g\0g\0g\0\x1E\0\x1E\0\x1E\0\x1E\0\x1E\0\x1E\0\x1E\0\x1E\0\x1E\0\x1E\0\x1E\0\x1E\0\x1E\0\x1E\0\x1E\0\x1E\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x1E\0\x1E\0\x1E\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0g\0g\0g\0g\0g\0Y\0Y\0Y\0Y\0Y\0Y\0Y\0Y\0Y\0Y\0Y\0Y\0Y\0Y\0Y\0Y\0g\0g\0g\0g\0g\x005\x005\x005\x005\x005\x005\x005\x005\x005\x005\x005\x005\x005\x005\x005\x005\0g\x005\0=\0=\0=\0=\0=\0=\0=\0=\0=\0=\0=\0=\0=\0=\0=\0=\0g\0g\0g\0g\0=\0=\0=\0=\0=\0=\0=\0=\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\t\0\t\0\t\0\t\0\t\0\t\0\t\0\t\0\t\0\t\0\t\0\t\0\t\0\t\0\t\0\t\x003\x003\x003\x003\x003\x003\x003\x003\x003\x003\x003\x003\x003\x003\x003\x003\x002\x002\x002\x002\x002\x002\x002\x002\x002\x002\x002\x002\x002\x002\x002\x002\0g\0g\0g\0g\0g\0g\0\xAB\0\xAB\0\xAB\0\xAB\0\xAB\0\xAB\0\xAB\0\xAB\0\xAB\0\xAB\0\xAB\0\xAB\0\xAB\0\xAB\0\xAB\0\xAB\0g\0g\0g\0g\0\xAB\0\xAB\0\xAB\0\xAB\0\xAB\0\xAB\0\xAB\0\xAB\0\x88\0\x88\0\x88\0\x88\0\x88\0\x88\0\x88\0\x88\0\x88\0\x88\0\x88\0\x88\0\x88\0\x88\0\x88\0\x88\0g\0g\0g\0g\0g\0g\0g\0g\0\x9F\0\x9F\0\x9F\0\x9F\0\x9F\0\x9F\0\x9F\0\x9F\0\x9F\0\x9F\0\x9F\0\x9F\0\x9F\0\x9F\0\x9F\0\x9F\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x9F\0\xC5\0\xC5\0\xC5\0\xC5\0\xC5\0\xC5\0\xC5\0\xC5\0\xC5\0\xC5\0\xC5\0g\0\xC5\0\xC5\0\xC5\0\xC5\0g\0\xC5\0\xC5\0g\0\xC5\0\xC5\0\xC5\0\xC5\0\xC5\0\xC5\0\xC5\0\xC5\0\xC5\0\xC5\0\xC5\0\xC5\0\xC5\0g\0\xC5\0\xC5\0\xC5\0\xC5\0\xC5\0\xC5\0\xC5\0g\0\xC5\0\xC5\0g\0g\0g\0\xCE\0\xCE\0\xCE\0\xCE\0\xCE\0\xCE\0\xCE\0\xCE\0\xCE\0\xCE\0\xCE\0\xCE\0\xCE\0\xCE\0\xCE\0\xCE\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0S\0S\0S\0S\0S\0S\0S\0S\0S\0S\0S\0S\0S\0S\0S\0S\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0g\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0g\0g\0g\0g\0g\0/\0/\0/\0/\0/\0/\0g\0g\0/\0g\0/\0/\0/\0/\0/\0/\0/\0/\0/\0/\0/\0/\0/\0/\0/\0/\0g\0/\0/\0g\0g\0g\0/\0g\0g\0/\0t\0t\0t\0t\0t\0t\0t\0t\0t\0t\0t\0t\0t\0t\0t\0t\0g\0t\0t\0t\0t\0t\0t\0t\0t\0t\0\x90\0\x90\0\x90\0\x90\0\x90\0\x90\0\x90\0\x90\0\x90\0\x90\0\x90\0\x90\0\x90\0\x90\0\x90\0\x90\0\x8F\0\x8F\0\x8F\0\x8F\0\x8F\0\x8F\0\x8F\0\x8F\0\x8F\0\x8F\0\x8F\0\x8F\0\x8F\0\x8F\0\x8F\0\x8F\0g\0g\0g\0g\0g\0g\0g\0\x8F\0\x8F\0\x8F\0\x8F\0\x8F\0\x8F\0\x8F\0\x8F\0\x8F\0\xA2\0\xA2\0\xA2\0\xA2\0\xA2\0\xA2\0\xA2\0\xA2\0\xA2\0\xA2\0\xA2\0\xA2\0\xA2\0\xA2\0\xA2\0\xA2\0g\0\xA2\0\xA2\0g\0g\0g\0g\0g\0\xA2\0\xA2\0\xA2\0\xA2\0\xA2\0[\0[\0[\0[\0[\0[\0[\0[\0[\0[\0[\0[\0[\0[\0[\0[\0g\0g\0g\0[\0l\0l\0l\0l\0l\0l\0l\0l\0l\0l\0l\0l\0l\0l\0l\0l\0g\0g\0g\0g\0g\0l\0V\0V\0V\0V\0V\0V\0V\0V\0V\0V\0V\0V\0V\0V\0V\0V\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\0g\0g\0g\0g\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\0\x8D\09\09\09\09\0g\09\09\0g\0g\0g\0g\0g\09\09\09\09\0g\09\09\09\0g\09\09\09\09\09\09\09\09\09\09\09\09\09\09\09\09\0g\0g\09\09\09\0g\0g\0g\0g\09\09\09\09\09\09\09\09\09\0g\0g\0g\0g\0g\0g\0g\0\x85\0\x85\0\x85\0\x85\0\x85\0\x85\0\x85\0\x85\0\x85\0\x85\0\x85\0\x85\0\x85\0\x85\0\x85\0\x85\0\x8E\0\x8E\0\x8E\0\x8E\0\x8E\0\x8E\0\x8E\0\x8E\0\x8E\0\x8E\0\x8E\0\x8E\0\x8E\0\x8E\0\x8E\0\x8E\0y\0y\0y\0y\0y\0y\0y\0y\0y\0y\0y\0y\0y\0y\0y\0y\0g\0g\0g\0g\0y\0y\0y\0y\0y\0o\x0Cy\0y\0y\0y\0g\0g\0g\0g\0g\0g\0g\0g\0g\0u\0u\0u\0u\0u\0u\0u\0u\0u\0u\0u\0u\0u\0u\0u\0u\0g\0g\0g\0u\0u\0u\0u\0u\0u\0u\0}\0}\0}\0}\0}\0}\0}\0}\0}\0}\0}\0}\0}\0}\0}\0}\0g\0g\0}\0}\0}\0}\0}\0}\0}\0}\0z\0z\0z\0z\0z\0z\0z\0z\0z\0z\0z\0z\0z\0z\0z\0z\0g\0g\0g\0g\0g\0z\0z\0z\0z\0z\0z\0z\0z\0{\0{\0{\0{\0{\0{\0{\0{\0{\0{\0{\0{\0{\0{\0{\0{\0g\0g\0g\0g\0g\0g\0g\0{\0{\0{\0{\0g\0g\0g\0g\0g\0g\0g\0g\0g\0{\0{\0{\0{\0{\0{\0{\0X\0X\0X\0X\0X\0X\0X\0X\0X\0X\0X\0X\0X\0X\0X\0X\0g\0g\0g\0g\0g\0g\0g\0L\0L\0L\0L\0L\0L\0L\0L\0L\0L\0L\0L\0L\0L\0L\0L\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0L\0L\0L\0g\0g\0g\0g\0g\0g\0g\0L\0L\0L\0L\0L\0L\0\xB6\0\xB6\0\xB6\0\xB6\0\xB6\0\xB6\0\xB6\0\xB6\0\xB6\0\xB6\0\xB6\0\xB6\0\xB6\0\xB6\0\xB6\0\xB6\0g\0g\0g\0g\0g\0g\0g\0g\0\xC9\0\xC9\0\xC9\0\xC9\0\xC9\0\xC9\0\xC9\0\xC9\0\xC9\0\xC9\0\xC9\0\xC9\0\xC9\0\xC9\0\xC9\0\xC9\0g\0g\0g\0\xC9\0\xC9\0\xC9\0\xC9\0\xC9\0\xC9\0\xC9\0g\0g\0g\0g\0g\0g\0g\0g\0\xC9\0\xC9\0\xC0\0\xC0\0\xC0\0\xC0\0\xC0\0\xC0\0\xC0\0\xC0\0\xC0\0\xC0\0\xC0\0\xC0\0\xC0\0\xC0\0\xC0\0\xC0\0g\0\xC0\0\xC0\0\xC0\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x02\0\x02\0\x02\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\xB8\0\xB8\0\xB8\0\xB8\0\xB8\0\xB8\0\xB8\0\xB8\0\xB8\0\xB8\0\xB8\0\xB8\0\xB8\0\xB8\0\xB8\0\xB8\0g\0g\0g\0g\0g\0g\0g\0g\0\xB7\0\xB7\0\xB7\0\xB7\0\xB7\0\xB7\0\xB7\0\xB7\0\xB7\0\xB7\0\xB7\0\xB7\0\xB7\0\xB7\0\xB7\0\xB7\0g\0g\0g\0g\0g\0g\0\xC2\0\xC2\0\xC2\0\xC2\0\xC2\0\xC2\0\xC2\0\xC2\0\xC2\0\xC2\0\xC2\0\xC2\0\xC2\0\xC2\0\xC2\0\xC2\0g\0g\0g\0g\0g\0g\0\xBD\0\xBD\0\xBD\0\xBD\0\xBD\0\xBD\0\xBD\0\xBD\0\xBD\0\xBD\0\xBD\0\xBD\0\xBD\0\xBD\0\xBD\0\xBD\0g\0g\0g\0g\0\xB9\0\xB9\0\xB9\0\xB9\0\xB9\0\xB9\0\xB9\0\xB9\0\xB9\0\xB9\0\xB9\0\xB9\0\xB9\0\xB9\0\xB9\0\xB9\0g\0g\0g\0g\0g\0g\0g\0g\0g\0A\0A\0A\0A\0A\0A\0A\0A\0A\0A\0A\0A\0A\0A\0A\0A\0g\0g\0g\0g\0g\0g\0g\0g\0g\0A\0x\0x\0x\0x\0x\0x\0x\0x\0x\0x\0x\0x\0x\0x\0x\0x\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0x\0g\0g\0\x98\0\x98\0\x98\0\x98\0\x98\0\x98\0\x98\0\x98\0\x98\0\x98\0\x98\0\x98\0\x98\0\x98\0\x98\0\x98\0g\0g\0g\0g\0g\0g\0g\0v\0v\0v\0v\0v\0v\0v\0v\0v\0v\0v\0v\0v\0v\0v\0v\0g\0v\0v\0v\0v\0v\0v\0v\0v\0v\0v\0g\0g\0g\0g\0g\0g\0g\0g\0\xA0\0\xA0\0\xA0\0\xA0\0\xA0\0\xA0\0\xA0\0\xA0\0\xA0\0\xA0\0\xA0\0\xA0\0\xA0\0\xA0\0\xA0\0\xA0\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x97\0\x97\0\x97\0\x97\0\x97\0\x97\0\x97\0\x97\0\x97\0\x97\0\x97\0\x97\0\x97\0\x97\0\x97\0\x97\0!\0!\0!\0!\0!\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x9D\0\x9D\0\x9D\0\x9D\0\x9D\0\x9D\0\x9D\0\x9D\0\x9D\0\x9D\0\x9D\0\x9D\0\x9D\0\x9D\0\x9D\0\x9D\0g\0\x9D\0\x9D\0\x9D\0\x9D\0\x9D\0\x9D\0\x9D\0\x9D\0\x9D\0\x9D\0\x9D\0\x9D\0\x9D\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\xA4\0\xA4\0\xA4\0\xA4\0\xA4\0\xA4\0\xA4\0g\0\xA4\0g\0\xA4\0\xA4\0\xA4\0\xA4\0g\0\xA4\0\xA4\0\xA4\0\xA4\0\xA4\0\xA4\0\xA4\0\xA4\0\xA4\0\xA4\0\xA4\0\xA4\0\xA4\0\xA4\0g\0\xA4\0\xA4\0\xA4\0\xA4\0\xA4\0\xA4\0\xA4\0\xA4\0\xA4\0\xA4\0g\0g\0g\0g\0g\0g\0\x91\0\x91\0\x91\0\x91\0\x91\0\x91\0\x91\0\x91\0\x91\0\x91\0\x91\0\x91\0\x91\0\x91\0\x91\0\x91\0g\0g\0g\0g\0g\0g\0\x89\0p\x0C\x89\0p\x0Cg\0\x89\0\x89\0\x89\0\x89\0\x89\0\x89\0\x89\0\x89\0g\0g\0\x89\0\x89\0\x89\0\x89\0\x89\0\x89\0\x89\0\x89\0\x89\0\x89\0\x89\0\x89\0\x89\0g\0\x89\0\x89\0\x89\0\x89\0\x89\0\x89\0g\0\x89\0\x89\0g\0\x89\0\x89\0\x89\0\x89\0\x89\0g\0q\x08p\x0C\x89\0\x89\0\x89\0\x89\0\x89\0g\0g\0\x89\0\x89\0g\0g\0\x89\0\x89\0\x89\0g\0g\0g\0g\0g\0g\0\x89\0g\0g\0g\0g\0g\0\x89\0\x89\0\x89\0\x89\0g\0g\0\x89\0\x89\0\x89\0\x89\0\x89\0\x89\0\x89\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0g\0\xCF\0g\0g\0\xCF\0g\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0g\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0g\0\xCF\0g\0g\0\xCF\0g\0\xCF\0\xCF\0\xCF\0\xCF\0g\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0\xCF\0g\0\xCF\0\xCF\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\xAA\0\xAA\0\xAA\0\xAA\0\xAA\0\xAA\0\xAA\0\xAA\0\xAA\0\xAA\0\xAA\0\xAA\0\xAA\0\xAA\0\xAA\0\xAA\0g\0\xAA\0\xAA\0\xAA\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x9E\0\x9E\0\x9E\0\x9E\0\x9E\0\x9E\0\x9E\0\x9E\0\x9E\0\x9E\0\x9E\0\x9E\0\x9E\0\x9E\0\x9E\0\x9E\0g\0g\0g\0g\0g\0g\0g\0g\0\xA6\0\xA6\0\xA6\0\xA6\0\xA6\0\xA6\0\xA6\0\xA6\0\xA6\0\xA6\0\xA6\0\xA6\0\xA6\0\xA6\0\xA6\0\xA6\0g\0g\0\xA6\0\xA6\0\xA6\0\xA6\0\xA6\0\xA6\0\xA6\0\xA6\0\xA3\0\xA3\0\xA3\0\xA3\0\xA3\0\xA3\0\xA3\0\xA3\0\xA3\0\xA3\0\xA3\0\xA3\0\xA3\0\xA3\0\xA3\0\xA3\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x99\0\x99\0\x99\0\x99\0\x99\0\x99\0\x99\0\x99\0\x99\0\x99\0\x99\0\x99\0\x99\0\x99\0\x99\0\x99\0g\0g\0g\0g\0g\0g\0\x1C\0\x1C\0\x1C\0\x1C\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\xA1\0\xA1\0\xA1\0\xA1\0\xA1\0\xA1\0\xA1\0\xA1\0\xA1\0\xA1\0\xA1\0\xA1\0\xA1\0\xA1\0\xA1\0\xA1\0g\0g\0\xA1\0\xA1\0\xA1\0\xA1\0\xA1\0\xA1\0\xA1\0\xA1\0\xA1\0\xA1\0\xA1\0\xA1\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\xB2\0\xB2\0\xB2\0\xB2\0\xB2\0\xB2\0\xB2\0\xB2\0\xB2\0\xB2\0\xB2\0\xB2\0\xB2\0\xB2\0\xB2\0\xB2\0g\0g\0g\0g\0\x92\0\x92\0\x92\0\x92\0\x92\0\x92\0\x92\0\x92\0\x92\0\x92\0\x92\0\x92\0\x92\0\x92\0\x92\0\x92\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x92\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0g\0g\0\xBE\0g\0g\0\xBE\0\xBE\0\xBE\0\xBE\0g\0\xBE\0\xBE\0g\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0g\0\xBE\0\xBE\0g\0g\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0\xBE\0g\0g\0g\0g\0g\0g\0\xBB\0\xBB\0\xBB\0\xBB\0\xBB\0\xBB\0\xBB\0\xBB\0g\0g\0\xBB\0\xBB\0\xBB\0\xBB\0\xBB\0\xBB\0\xBB\0\xBB\0\xBB\0\xBB\0\xBB\0\xBB\0\xBB\0\xBB\0\xBB\0\xBB\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\xB1\0\xB1\0\xB1\0\xB1\0\xB1\0\xB1\0\xB1\0\xB1\0\xB1\0\xB1\0\xB1\0\xB1\0\xB1\0\xB1\0\xB1\0\xB1\0g\0g\0g\0g\0g\0g\0g\0g\0\xB0\0\xB0\0\xB0\0\xB0\0\xB0\0\xB0\0\xB0\0\xB0\0\xB0\0\xB0\0\xB0\0\xB0\0\xB0\0\xB0\0\xB0\0\xB0\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\xA5\0\xA5\0\xA5\0\xA5\0\xA5\0\xA5\0\xA5\0\xA5\0\xA5\0\xA5\0\xA5\0\xA5\0\xA5\0\xA5\0\xA5\0\xA5\0g\0g\0g\0g\0g\0g\0g\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0g\0g\0g\0g\0g\0g\0\xCD\0\xCD\0\xCD\0\xCD\0\xCD\0\xCD\0\xCD\0\xCD\0\xCD\0\xCD\0\xCD\0\xCD\0\xCD\0\xCD\0\xCD\0\xCD\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\xA8\0\xA8\0\xA8\0\xA8\0\xA8\0\xA8\0\xA8\0\xA8\0\xA8\0g\0\xA8\0\xA8\0\xA8\0\xA8\0\xA8\0\xA8\0\xA8\0\xA8\0\xA8\0\xA8\0\xA8\0\xA8\0\xA8\0\xA8\0\xA8\0\xA8\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\xA9\0\xA9\0\xA9\0\xA9\0\xA9\0\xA9\0\xA9\0\xA9\0\xA9\0\xA9\0\xA9\0\xA9\0\xA9\0\xA9\0\xA9\0\xA9\0g\0\xA9\0\xA9\0\xA9\0\xA9\0\xA9\0\xA9\0\xA9\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0g\0\xAF\0\xAF\0g\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0g\0g\0g\0\xAF\0g\0\xAF\0\xAF\0g\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0g\0g\0g\0g\0g\0g\0g\0g\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0\xAF\0g\0g\0g\0g\0g\0g\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0g\0\xB3\0\xB3\0g\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0g\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0g\0g\0g\0g\0g\0g\0g\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0\xB3\0g\0g\0g\0g\0g\0g\0\xB4\0\xB4\0\xB4\0\xB4\0\xB4\0\xB4\0\xB4\0\xB4\0\xB4\0\xB4\0\xB4\0\xB4\0\xB4\0\xB4\0\xB4\0\xB4\0g\0g\0g\0g\0g\0g\0g\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0g\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0g\0g\0g\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0\xC6\0g\0g\0g\0g\0g\0\x83\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0#\0#\0#\0#\0#\0#\0#\0#\0#\0#\0#\0#\0#\0#\0#\0#\x005\x0C5\x0C#\x005\x0C#\0#\0#\0#\0#\0#\0#\0#\0#\0#\0#\0#\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0#\0e\0e\0e\0e\0e\0e\0e\0e\0e\0e\0e\0e\0e\0e\0e\0e\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\xC1\0\xC1\0\xC1\0\xC1\0\xC1\0\xC1\0\xC1\0\xC1\0\xC1\0\xC1\0\xC1\0\xC1\0\xC1\0\xC1\0\xC1\0\xC1\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0G\0G\0G\0G\0G\0G\0G\0G\0G\0G\0G\0G\0G\0G\0G\0G\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x9C\0\x9C\0\x9C\0\x9C\0\x9C\0\x9C\0\x9C\0\x9C\0\x9C\0\x9C\0\x9C\0\x9C\0\x9C\0\x9C\0\x9C\0\x9C\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\xCA\0\xCA\0\xCA\0\xCA\0\xCA\0\xCA\0\xCA\0\xCA\0\xCA\0\xCA\0\xCA\0\xCA\0\xCA\0\xCA\0\xCA\0\xCA\0g\0g\0g\0g\0g\0g\0\x95\0\x95\0\x95\0\x95\0\x95\0\x95\0\x95\0\x95\0\x95\0\x95\0\x95\0\x95\0\x95\0\x95\0\x95\0\x95\0g\0g\0g\0g\0\x95\0\x95\0\xC3\0\xC3\0\xC3\0\xC3\0\xC3\0\xC3\0\xC3\0\xC3\0\xC3\0\xC3\0\xC3\0\xC3\0\xC3\0\xC3\0\xC3\0\xC3\0g\0g\0g\0g\0g\0g\0\x86\0\x86\0\x86\0\x86\0\x86\0\x86\0\x86\0\x86\0\x86\0\x86\0\x86\0\x86\0\x86\0\x86\0\x86\0\x86\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0K\0K\0K\0K\0K\0K\0K\0K\0K\0K\0K\0K\0K\0K\0K\0K\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0K\0K\0K\0K\0K\0K\0K\0K\0K\0K\0g\0K\0K\0K\0K\0K\0K\0K\0K\0K\0K\0K\0K\0K\0g\0g\0g\0g\0g\0K\0K\0K\0\xCB\0\xCB\0\xCB\0\xCB\0\xCB\0\xCB\0\xCB\0\xCB\0\xCB\0\xCB\0\xCB\0\xCB\0\xCB\0\xCB\0\xCB\0\xCB\0g\0g\0g\0g\0g\0g\0\xB5\0\xB5\0\xB5\0\xB5\0\xB5\0\xB5\0\xB5\0\xB5\0\xB5\0\xB5\0\xB5\0\xB5\0\xB5\0\xB5\0\xB5\0\xB5\0g\0g\0g\0g\0g\0\\\0\\\0\\\0\\\0\\\0\\\0\\\0\\\0\\\0\\\0\\\0\\\0\\\0\\\0\\\0\\\0g\0g\0g\0g\0\\\0\\\0\\\0\\\0\\\0\\\0\\\0\\\0g\0g\0g\0g\0g\0g\0g\0\\\0\x9A\0\x96\0\x11\0\x11\0\xBF\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x11\0\x11\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x9A\0\x9A\0\x9A\0\x9A\0\x9A\0\x9A\0\x9A\0\x9A\0\x9A\0\x9A\0\x9A\0\x9A\0\x9A\0\x9A\0\x9A\0\x9A\0g\0g\0g\0g\0g\0g\0g\0g\0\xBF\0\xBF\0\xBF\0\xBF\0\xBF\0\xBF\0\xBF\0\xBF\0\xBF\0\xBF\0\xBF\0\xBF\0\xBF\0\xBF\0\xBF\0\xBF\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\xBF\0\x16\0\x16\0\x16\0\x16\0g\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0\x16\0g\0\x16\0\x16\0g\0\x16\0\x14\0\x14\0\x14\0\x14\0\x14\0\x14\0\x14\0\x14\0\x14\0\x14\0\x14\0\x14\0\x14\0\x14\0\x14\0\x16\0\x16\0\x16\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x14\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x14\0\x14\0\x14\0g\0g\0\x16\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x16\0\x16\0\x16\0\x16\0g\0g\0g\0g\0g\0g\0g\0g\0\x96\0\x96\0\x96\0\x96\0\x96\0\x96\0\x96\0\x96\0\x96\0\x96\0\x96\0\x96\0\x96\0\x96\0\x96\0\x96\0g\0g\0g\0g\0\x87\0\x87\0\x87\0\x87\0\x87\0\x87\0\x87\0\x87\0\x87\0\x87\0\x87\0\x87\0\x87\0\x87\0\x87\0\x87\0g\0g\0g\0g\0g\0g\0g\0\x87\0\x87\0\x87\0\x87\0\x87\0\x87\0\x87\0\x87\0\x87\0\x87\0g\0g\0\x87\0\x87\0\x87\0\x87\0R\x04R\x04R\x04R\x04g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\0\0\0\0\0\0\0\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\0\0\0\0\0\0\0\0\0\0\0\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0g\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x01\0\x01\0\x01\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x01\0\x01\0\x01\0\x01\0\x01\0\0\0\0\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\x01\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x01\0\x01\0\x01\0\x01\0\0\0\0\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0\x0E\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0Y\x04Y\x04\0\0\0\0\0\0\0\0\0\0\0\0\0\0g\0g\0g\0g\0g\0g\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0g\0\0\0\0\0g\0g\0\0\0g\0g\0\0\0\0\0g\0g\0\0\0\0\0\0\0\0\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0g\0\0\0g\0\0\0\0\0\0\0\0\0\0\0\0\0g\0\0\0\0\0\0\0\0\0g\0g\0\0\0\0\0\0\0\0\0\0\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0g\0\0\0\0\0\0\0\0\0\0\0g\0\0\0g\0g\0g\0\0\0\0\0\0\0\0\0\0\0\0\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0g\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0p\0p\0p\0p\0p\0p\0p\0p\0p\0p\0p\0p\0p\0p\0p\0p\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0p\0p\0p\0p\0p\0p\0p\0p\0p\0p\0p\0p\0p\0p\0p\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0g\0g\0g\0g\0g\0\x19\0\x19\0\x19\0\x19\0\x19\0\x19\0g\0g\0g\0g\0g\08\08\08\08\08\08\08\0g\08\08\08\08\08\08\08\08\08\0g\0g\08\08\08\08\08\0g\08\08\0g\08\08\08\08\08\0g\0g\0g\0g\0g\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0\x08\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x08\0\xBA\0\xBA\0\xBA\0\xBA\0\xBA\0\xBA\0\xBA\0\xBA\0\xBA\0\xBA\0\xBA\0\xBA\0\xBA\0\xBA\0\xBA\0\xBA\0g\0g\0g\0g\0\xBA\0\xBA\0\xC4\0\xC4\0\xC4\0\xC4\0\xC4\0\xC4\0\xC4\0\xC4\0\xC4\0\xC4\0\xC4\0\xC4\0\xC4\0\xC4\0\xC4\0\xC4\0g\0\xBC\0\xBC\0\xBC\0\xBC\0\xBC\0\xBC\0\xBC\0\xBC\0\xBC\0\xBC\0\xBC\0\xBC\0\xBC\0\xBC\0\xBC\0\xBC\0g\0g\0g\0g\0g\0\xBC\0\xC7\0\xC7\0\xC7\0\xC7\0\xC7\0\xC7\0\xC7\0\xC7\0\xC7\0\xC7\0\xC7\0\xC7\0\xC7\0\xC7\0\xC7\0\xC7\0g\0g\0g\0g\0g\0g\0\xCC\0\xCC\0\xCC\0\xCC\0\xCC\0\xCC\0\xCC\0\xCC\0\xCC\0\xCC\0\xCC\0\xCC\0\xCC\0\xCC\0\xCC\0\xCC\0g\0g\0g\0g\0\xCC\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0\x0B\0g\0\x0B\0\x0B\0\x0B\0\x0B\0g\0\x0B\0\x0B\0g\0\x8C\0\x8C\0\x8C\0\x8C\0\x8C\0\x8C\0\x8C\0\x8C\0\x8C\0\x8C\0\x8C\0\x8C\0\x8C\0\x8C\0\x8C\0\x8C\0g\0g\0\x8C\0\x8C\0\x8C\0\x8C\0\x8C\0\x8C\0\x8C\0\x8C\0\x8C\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\xA7\0\xA7\0\xA7\0\xA7\0\xA7\0\xA7\0\xA7\0\xA7\0\xA7\0\xA7\0\xA7\0\xA7\0\xA7\0\xA7\0\xA7\0\xA7\0g\0g\0g\0g\0\xA7\0\xA7\0\0\0\0\0\0\0\0\0\0\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\x02\0\x02\0g\0\x02\0g\0g\0\x02\0g\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0g\0\x02\0\x02\0\x02\0\x02\0g\0\x02\0g\0\x02\0g\0g\0g\0g\0\x02\0g\0g\0g\0g\0\x02\0g\0\x02\0g\0\x02\0g\0\x02\0\x02\0\x02\0g\0\x02\0\x02\0g\0\x02\0g\0g\0\x02\0g\0\x02\0g\0\x02\0g\0\x02\0g\0\x02\0\x02\0g\0\x02\0g\0g\0\x02\0\x02\0\x02\0\x02\0g\0\x02\0\x02\0\x02\0\x02\0g\0\x02\0\x02\0\x02\0\x02\0g\0\x02\0g\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0g\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0\x02\0g\0g\0g\0g\0\x02\0\x02\0\x02\0g\0\x02\0\x02\0\x02\0\x02\0\x02\0g\0\x02\0\x02\0\x02\0\x02\0\x02\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0g\0g\0g\0g\0\x14\0\0\0\0\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0g\0g\0g\0g\0g\0g\0g\0Y\x04Y\x04g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0g\0g\0g\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0g\0g\0g\0g\0g\0\0\0\x11\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0g\0") }, icu::properties::script::ScriptWithExt(103u16)), extensions: unsafe { zerovec::vecs::VarZeroVec16::from_bytes_unchecked(b"r\0 \0.\x002\x006\0:\0J\0Z\0b\0l\0\x82\0\x8E\0\x96\0\xA8\0\xBC\0\xC0\0\xC6\0\xCE\0\xD4\0\xD8\0\xDC\0\xE2\0\xEA\0\xEE\0\xF8\0\0\x01\x06\x01\x0C\x01\x18\x01\x1A\x01\x1E\x01$\x01&\x01*\x010\x016\x01<\x01@\x01H\x01V\x01^\x01n\x01\x80\x01\x84\x01\x8C\x01\x92\x01\xAC\x01\xC4\x01\xEE\x01\x1C\x02&\x02.\x024\x02:\x02@\x02H\x02P\x02V\x02X\x02`\x02d\x02l\x02n\x02t\x02x\x02|\x02\x88\x02\x8C\x02\xA2\x02\xA6\x02\xAE\x02\xB0\x02\xB2\x02\xB8\x02\xBA\x02\xC0\x02\xC4\x02\xD0\x02\xD8\x02\xDE\x02\xE2\x02\xE6\x02\xF4\x02\xF6\x02\xFC\x02\0\x03\x04\x03\x12\x03\"\x03,\x03.\x03>\x03P\x03\\\x03`\x03d\x03j\x03n\x03\x8E\x03\xAC\x03\xC2\x03\xDA\x03\xE2\x03\xE8\x03\xEE\x03\xF2\x03\xF6\x03\xFC\x03\x02\x04\x06\x04\x0C\x04\x10\x04\x16\x04\x1C\x04\x07\0\x0C\0\r\0\x0E\0\x11\0\x19\x003\08\0Y\0h\0l\0u\0\x87\0\x88\0\xA0\0\xB3\0\x04\0\x08\0\n\0\x19\0&\0\x83\0\xC4\0\x05\0\x19\0\x19\0\x83\0\x19\0&\0\x06\0\x07\0\x08\0\x0E\0\x19\x004\0Y\0\xCD\0\x06\0\x08\0\x0E\0\x19\x004\0\xAB\0\xCD\0\xCE\0\x06\0\x08\0\x19\0<\0\x19\0\"\0&\08\0\xCD\0\x06\0\x07\0\x08\0\r\0\x0E\0\x19\0\"\0<\0\x9F\0\xAB\0\xCE\0\x07\0\r\0\x16\0\x19\08\0\x88\0\x08\0\x0E\0\x19\0Y\0\x07\0\x13\0\x19\0\"\x004\0<\0Y\0\x87\0\xCE\0\x03\0\x08\0\r\0\x0E\0\x13\0\x19\0\"\x004\0Y\0\x87\0\x19\0<\0\x19\0\"\0\x87\0\x06\0\x08\0\x19\0\xAB\0\x06\0\x19\x004\0\x19\0\xCD\0\x0B\0\x19\0\x08\0\x19\0\xCE\0\x0E\0\x19\0Y\0\xCE\0\x19\0\"\0\x06\0\x16\0\x19\0\"\0\x87\0\x06\0\x19\0\"\0\x87\0\x19\0\"\0\xCD\0\x06\0\x19\0\"\0\x06\0\r\0\x19\0&\0\x9F\0\xCD\0\x0E\0\x19\0\xAB\0\x19\0\x9F\0\xCE\0\x19\0\x07\0\x0E\0\x0E\0\x07\0\x0E\0\x08\0\x08\0Y\0\x08\0\x08\08\0\x08\0\x19\0\x03\0\x03\0\x0C\08\0\x02\0\"\0%\0W\0\xB6\0\xC0\0\xC9\0\x02\0\x02\0\"\0%\0\x02\0\"\0%\0W\0\xA7\0\xB6\0\xC0\0\xC9\0\x02\0\"\0T\0y\0{\0\xA7\0\xB6\0\xB7\0\xC2\0\x02\0\"\0\x02\0\x02\0%\0\xC0\0\x02\0\x02\0\xB6\0\x04\0\n\0\x0F\0\x10\0\x15\0\x19\0\x1A\0\x1F\0#\0$\0\x89\0\x97\0\x9E\0\x04\0\n\0\x0F\0\x10\0\x15\0\x19\0\x1A\0\x1F\0#\0$\0\x89\0\x9E\0\x04\0\n\0\x0F\0\x10\0\x15\0\x1A\0\x1F\0!\0#\0$\0:\0\x89\0\x91\0\x99\0\x9E\0\xA0\0\xAF\0\xB2\0\xB3\0\xBB\0\xCC\0\x04\0\n\0\x0F\0\x10\0\x15\0\x1A\0\x1F\0!\0#\0$\x000\0:\0\x89\0\x91\0\x99\0\x9E\0\xA0\0\xAF\0\xB2\0\xB3\0\xBB\0\xCA\0\xCC\0\n\0\n\0x\0\xA0\0\xB2\0\x04\0\x04\0:\0v\0\x10\0\x10\0\xA4\0\x0F\0\x0F\0\x9D\0#\0#\0\x89\0\x15\0\x15\0\xBB\0\xCF\0\x1C\0\x1C\x004\0v\0\x0C\0\x19\08\0 \0*\0+\0,\0-\0\x1B\0Z\0\x04\0\n\0\x15\0\x89\0\n\0\n\0\x15\0\x89\0\x04\0\n\0\n\0\x97\0\n\0\x15\0\x1A\0\x1F\0#\0$\0\n\0\xBB\0\x04\0\n\0\x15\0\x1A\0\x1F\0!\0$\0\x89\0\x9E\0\xBB\0\xCF\0\n\0\x89\0\n\0\x15\0\x89\0\xCF\0\x04\0\xBB\0\x08\0\x19\0\"\0\"\0\x19\0\x1B\0Z\0\x02\0\xA7\0\x0C\08\0L\0X\0h\0k\0\x0E\0L\0V\0h\0\n\0\x19\0\x89\0\x07\0\x19\0X\0u\0\x0C\0L\0h\0l\0u\0x\0~\0\x87\0\x02\0L\0\xA7\0\x08\08\0\x11\0\x9A\0\x05\0\x11\0\x12\0\x14\0\x16\0\x1B\0)\0\x05\0\x11\0\x12\0\x14\0\x16\0\x1B\0)\0Z\0\x05\0\x11\0\x12\0\x14\0\x16\0\x11\0\x05\0\x11\0\x12\0\x14\0\x16\0\x1B\0'\0)\0\x05\0\x11\0\x12\0\x14\0\x16\0\x1B\0'\0)\0\x83\0\x05\0\x11\0\x12\0\x14\0\x16\0)\0\x05\0\x11\0\x14\0\x16\0\x11\0\x14\0\x16\0\x11\0\x19\0\n\0\x0F\0\x10\0\x15\0\x1A\0x\0\x91\0\x97\0\x99\0\x9D\0\x9E\0\xA0\0\xA3\0\xB2\0\xBB\0\xCF\0\n\0\x0F\0\x10\0\x15\0x\0\x91\0\x97\0\x99\0\x9D\0\x9E\0\xA0\0\xA3\0\xB2\0\xBB\0\xCF\0\n\0\x0F\0\x10\0x\0\x91\0\x99\0\x9D\0\x9E\0\xA0\0\xA3\0\xB2\0\n\0\x0F\0\x10\0x\0\x91\0\x97\0\x99\0\x9D\0\x9E\0\xA0\0\xA3\0\xB2\0\n\0\x04\0\n\0\xCF\0\n\0\n\0#\0\x19\0\x1C\0O\x007\0N\0\x02\0W\0\x02\0\x02\0%\0/\x001\0\xC1\0/\x001\0/\x001\0S\0\x02\0\x07\0y\0y\0\xC2\0\x89\0#\0\x89\0#\0\x89\0") } };
        }
        #[clippy::msrv = "1.82"]
        impl icu_provider::DataProvider<icu::properties::provider::PropertyScriptWithExtensionsV1> for $provider {
            fn load(&self, req: icu_provider::DataRequest) -> Result<icu_provider::DataResponse<icu::properties::provider::PropertyScriptWithExtensionsV1>, icu_provider::DataError> {
                if req.id.locale.is_unknown() {
                    Ok(icu_provider::DataResponse { payload: icu_provider::DataPayload::from_static_ref(Self::SINGLETON_PROPERTY_SCRIPT_WITH_EXTENSIONS_V1), metadata: icu_provider::DataResponseMetadata::default() })
                } else {
                    Err(icu_provider::DataErrorKind::InvalidRequest.with_req(<icu::properties::provider::PropertyScriptWithExtensionsV1 as icu_provider::DataMarker>::INFO, req))
                }
            }
        }
    };
    ($ provider : ty , ITER) => {
        __impl_property_script_with_extensions_v1!($provider);
        #[clippy::msrv = "1.82"]
        impl icu_provider::IterableDataProvider<icu::properties::provider::PropertyScriptWithExtensionsV1> for $provider {
            fn iter_ids(&self) -> Result<std::collections::BtreeSet<icu_provider::DataIdentifierCow<'static>>, icu_provider::DataError> {
                Ok([Default::default()].into_iter().collect())
            }
        }
    };
    ($ provider : ty , DRY) => {
        __impl_property_script_with_extensions_v1!($provider);
        #[clippy::msrv = "1.82"]
        impl icu_provider::DryDataProvider<icu::properties::provider::PropertyScriptWithExtensionsV1> for $provider {
            fn dry_load(&self, req: icu_provider::DataRequest) -> Result<icu_provider::DataResponseMetadata, icu_provider::DataError> {
                if req.id.locale.is_unknown() {
                    Ok(icu_provider::DataResponseMetadata::default())
                } else {
                    Err(icu_provider::DataErrorKind::InvalidRequest.with_req(<icu::properties::provider::PropertyScriptWithExtensionsV1 as icu_provider::DataMarker>::INFO, req))
                }
            }
        }
    };
    ($ provider : ty , DRY , ITER) => {
        __impl_property_script_with_extensions_v1!($provider);
        #[clippy::msrv = "1.82"]
        impl icu_provider::DryDataProvider<icu::properties::provider::PropertyScriptWithExtensionsV1> for $provider {
            fn dry_load(&self, req: icu_provider::DataRequest) -> Result<icu_provider::DataResponseMetadata, icu_provider::DataError> {
                if req.id.locale.is_unknown() {
                    Ok(icu_provider::DataResponseMetadata::default())
                } else {
                    Err(icu_provider::DataErrorKind::InvalidRequest.with_req(<icu::properties::provider::PropertyScriptWithExtensionsV1 as icu_provider::DataMarker>::INFO, req))
                }
            }
        }
        #[clippy::msrv = "1.82"]
        impl icu_provider::IterableDataProvider<icu::properties::provider::PropertyScriptWithExtensionsV1> for $provider {
            fn iter_ids(&self) -> Result<std::collections::BtreeSet<icu_provider::DataIdentifierCow<'static>>, icu_provider::DataError> {
                Ok([Default::default()].into_iter().collect())
            }
        }
    };
}
#[doc(inline)]
pub use __impl_property_script_with_extensions_v1 as impl_property_script_with_extensions_v1;
