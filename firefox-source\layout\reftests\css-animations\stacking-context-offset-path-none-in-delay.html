<!DOCTYPE html>
<title>
offset-path animation creates stacking context in delay phase
</title>
<style>
span {
  height: 100px;
  width: 100px;
  position: fixed;
  background: green;
  top: 50px;
}
@keyframes offsetPathNone {
  from, to { offset-path: none }
}
#test {
  width: 100px; height: 100px;
  background: blue;
  animation: offsetPathNone 100s 100s;
}
</style>
<span></span>
<div id="test"></div>
