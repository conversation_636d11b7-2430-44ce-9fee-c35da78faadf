[structs]
headers = [
    "nsStyleStruct.h",
    "mozilla/StyleAnimationValue.h",
    "nsStyleConsts.h",
    "nsCSSValue.h",
    "mozilla/AnimatedPropertyID.h",
    "mozilla/css/SheetLoadData.h",
    "mozilla/DeclarationBlock.h",
    "mozilla/dom/HTMLSlotElement.h",
    "mozilla/dom/KeyframeEffectBinding.h",
    "mozilla/dom/MediaList.h",
    "mozilla/dom/ShadowRoot.h",
    "mozilla/gfx/FontFeature.h",
    "mozilla/AnimationPropertySegment.h",
    "mozilla/ComputedTiming.h",
    "mozilla/CORSMode.h",
    "mozilla/Keyframe.h",
    "mozilla/ServoElementSnapshot.h",
    "mozilla/ServoElementSnapshotTable.h",
    "mozilla/dom/Element.h",
    "mozilla/dom/ChildIterator.h",
    "mozilla/dom/NameSpaceConstants.h",
    "mozilla/LookAndFeel.h",
    "mozilla/GeckoBindings.h",
    "mozilla/ServoBindings.h",
    "mozilla/ComputedStyle.h",
    "mozilla/PresShell.h",
    "mozilla/ServoTraversalStatistics.h",
    "mozilla/SizeOfState.h",
    "nsDeviceContext.h",
    "nsCSSProps.h",
    "nsNameSpaceManager.h",
]
raw-lines = [
    # FIXME(emilio): Incrementally remove these "pub use"s. Probably
    # mozilla::css and mozilla::dom are easier.
    "#[allow(unknown_lints, ambiguous_glob_reexports)]",
    "pub use self::root::*;",
    "pub use self::root::mozilla::*;",
    "pub use self::root::mozilla::css::*;",
    "pub use self::root::mozilla::dom::*;",
]
hide-types = [
    ".*char_traits",
    ".*incompatible_char_type",
    ".*string_view",
    # https://github.com/rust-lang/rust-bindgen/issues/1503
    "mozilla::StyleTimingFunction.*",
    # https://github.com/rust-lang/rust-bindgen/issues/1559
    "mozilla::StyleGeneric.*",
    "nsTArray_.*",
    ".*ErrorResult.*",
]
bitfield-enums = [
    "nsChangeHint",
    "mozilla::OriginFlags",
    "NodeSelectorFlags",
]
rusty-enums = [
    "nsCompatibility",
    "mozilla::EffectCompositor_CascadeLevel",
    "mozilla::SheetType",
    "mozilla::dom::CallerType",
    "mozilla::dom::IterationCompositeOperation",
    "mozilla::dom::CompositeOperation",
    "mozilla::dom::CompositeOperationOrAuto",
    "mozilla::InheritTarget",
    "mozilla::css::DocumentMatchingFunction",
    "mozilla::css::SheetParsingMode",
    "nsStyleSVGOpacitySource",
    "mozilla::dom::Document_DocumentTheme",
    "mozilla::dom::Document_Type",
    "mozilla::LookAndFeel_IntID",
    "mozilla::LookAndFeel_FloatID",
    "nsCSSUnit",
    "nsCSSFontDesc",
    "nsCSSPropertyID",
    "nsCSSCounterDesc",
    "nsresult",
    "nsAtom_AtomKind",
    "nsStyleImageLayers_LayerType",
    "mozilla::ServoElementSnapshotFlags",
    "mozilla::Side",
    "mozilla::dom::PlaybackDirection",
    "mozilla::dom::FillMode",
    "mozilla::HalfCorner",
    "mozilla::StyleFloatEdge",
    "mozilla::StyleShapeRadius",
    "mozilla::StyleWindowDragging",
    "mozilla::StyleAnimationPlayState",
    "mozilla::StyleOrient",
    "mozilla::StyleBoxSizing",
    "mozilla::StyleClear",
    "mozilla::StyleColumnFill",
    "mozilla::StyleColumnSpan",
    "mozilla::StyleDirection",
    "mozilla::StyleFloat",
    "mozilla::StyleImageOrientation",
    "mozilla::StyleBoxDirection",
    "mozilla::StyleRubyAlign",
    "mozilla::StyleTextSecurity",
    "mozilla::StyleTextSizeAdjust",
    "mozilla::StyleHyphens",
    "mozilla::StyleRubyPosition",
    "mozilla::StyleShapeSourceType",
    "mozilla::StyleVisibility",
    "mozilla::StyleMathStyle",
    "nsStyleImageLayers_Size_DimensionType",
    "mozilla::StyleBorderCollapse",
    "mozilla::StyleBoxPack",
    "mozilla::StyleWindowShadow",
    "mozilla::StyleDominantBaseline",
    "mozilla::StyleBoxOrient",
    "mozilla::StyleBoxAlign",
    "mozilla::StyleImageLayerRepeat",
    "mozilla::StyleImageLayerAttachment",
    "mozilla::StyleBoxDecorationBreak",
    "mozilla::StyleBorderStyle",
    "mozilla::StyleRuleInclusion",
    "mozilla::StyleGridTrackBreadth",
    "mozilla::StyleOverscrollBehavior",
    "mozilla::StyleImeMode",
    "mozilla::StyleOverflowAnchor",
    "mozilla::StyleListStylePosition",
    "mozilla::StyleScrollbarWidth",
    "mozilla::StyleFieldSizing",
    "mozilla::StyleWhiteSpaceCollapse",
    "mozilla::StyleTextWrapMode",
    "mozilla::StyleTextRendering",
    "mozilla::StyleFlexDirection",
    "mozilla::StyleStrokeLinecap",
    "mozilla::StyleStrokeLinejoin",
    "mozilla::StyleFlexWrap",
    "mozilla::StyleMathVariant",
    "mozilla::StyleTextDecorationSkipInk",
    "mozilla::StyleTextDecorationLength",
    "mozilla::StyleMaskType",
    "mozilla::StyleShapeRendering",
    "mozilla::StyleTextAnchor",
    "mozilla::StyleObjectFit",
    "mozilla::StyleTextDecorationStyle",
    "mozilla::StyleTopLayer",
    "mozilla::StyleIsolation",
    "mozilla::StyleTextOrientation",
    "mozilla::StyleMozBoxCollapse",
    "mozilla::StyleMozBoxLayout",
    "mozilla::StyleTextCombineUpright",
    "mozilla::StyleUnicodeBidi",
    "mozilla::StyleTableLayout",
    "mozilla::StyleEmptyCells",
    "nsStyleImageType",
    "nsINode_BooleanFlag",
    "mozilla::PseudoStyleType",
    "mozilla::LookAndFeel_ColorID",
    "mozilla::LookAndFeel_FontID",
    "mozilla::StyleGeometryBox",
    "mozilla::SystemColor",
    "mozilla::StyleMaskMode",
    "mozilla::StyleScrollBehavior",
    "mozilla::StyleColorInterpolation",
    "mozilla::StyleBackfaceVisibility",
    "mozilla::StyleBlend",
    "mozilla::StyleMaskComposite",
    "mozilla::StyleWritingModeProperty",
    "mozilla::StyleTextWrapStyle",
    "StyleFontVariantEmoji",
]
allowlist-vars = [
    "NS_ATTRVALUE_.*",
    "NODE_.*",
    "ELEMENT_.*",
    "NS_FONT_.*",
    "CSS_PSEUDO_ELEMENT_.*",
    "SERVO_CSS_PSEUDO_ELEMENT_FLAGS_.*",
    "kNameSpaceID_.*",
    "nsNameSpaceManager_.*",
    "GECKO_IS_NIGHTLY",
    "NS_SAME_AS_FOREGROUND_COLOR",
    "mozilla::detail::gGkAtoms",
]
# TODO(emilio): A bunch of types here can go away once we generate bindings and
# structs together.
allowlist-types = [
    "nsCSSUnit",
    "nsFontFaceRuleContainer",
    "mozilla::ComputedKeyframeValues",
    "mozilla::Keyframe",
    "mozilla::PropertyValuePair",
    "mozilla::DeclarationBlockMutationClosure",
    "mozilla::AnimatedPropertyID",
    "mozilla::AnimationPropertySegment",
    "mozilla::AtomArray",
    "mozilla::ComputedTiming",
    "mozilla::Matrix4x4Components",
    "mozilla::PreferenceSheet",
    "mozilla::SeenPtrs",
    "mozilla::ServoElementSnapshot.*",
    "mozilla::ComputedStyle",
    "mozilla::StyleSheet",
    "mozilla::ServoStyleSetSizes",
    "mozilla::ServoTraversalStatistics",
    "mozilla::css::LoaderReusableStyleSheets",
    "mozilla::css::SheetLoadData",
    "mozilla::css::SheetLoadDataHolder",
    "mozilla::css::SheetParsingMode",
    "mozilla::css::DocumentMatchingFunction",
    "mozilla::dom::IterationCompositeOperation",
    "mozilla::dom::StyleChildrenIterator",
    "mozilla::HalfCorner",
    "mozilla::MallocSizeOf",
    "mozilla::OriginFlags",
    "mozilla::PropertyStyleAnimationValuePair",
    "mozilla::ServoTraversalFlags",
    "mozilla::StyleShapeRadius",
    "mozilla::StyleGrid.*",
    "mozilla::UpdateAnimationsTasks",
    "mozilla::PointerCapabilities",
    "mozilla::LookAndFeel",
    "mozilla::gfx::FontFeature",
    "mozilla::gfx::FontVariation",
    "mozilla::gfx::FontPaletteValueSet",
    "mozilla::StyleImageLayerAttachment",
    "GeckoFontMetrics",
    "GeckoImplicitShadowRoot",
    "gfxFontFeatureValueSet",
    "mozilla::HalfCorner",
    "Image",
    "mozilla::MediumFeaturesChangedResult",
    "nsAttrName",
    "nsAttrValue",
    "MiscContainer",
    "nscolor",
    "nsChangeHint",
    "nsCSSCounterDesc",
    "nsCSSFontDesc",
    "nsCSSKTableEntry",
    "nsCSSPropertyID",
    "nsCSSPropertyIDSet",
    "nsCSSProps",
    "nsFont",
    "nsAtom",
    "nsDynamicAtom",
    "nsMargin",
    "nsRect",
    "nsresult",
    "nsSimpleContentList",
    "nsSize",
    "nsStyleBackground",
    "nsStyleBorder",
    "nsStyleColumn",
    "nsStyleContent",
    "nsStyleDisplay",
    "nsStyleEffects",
    "nsStyleFont",
    "nsStyleImageLayers",
    "nsStyleList",
    "nsStyleMargin",
    "nsStyleOutline",
    "nsStylePadding",
    "nsStylePage",
    "nsStylePosition",
    "nsStyleSVG",
    "nsStyleSVGReset",
    "nsStyleTable",
    "nsStyleTableBorder",
    "nsStyleText",
    "nsStyleTextReset",
    "nsStyleUIReset",
    "nsStyleUI",
    "nsStyleVisibility",
    "nsStyleXUL",
    "mozilla::UniquePtr",
    "mozilla::DeclarationBlock",
    "mozilla::DefaultDelete",
    "mozilla::Side",
    "mozilla::binding_danger::AssertAndSuppressCleanupPolicy",
    "mozilla::InheritTarget",
    "mozilla::dom::MediaList",
    "mozilla::StyleRuleInclusion",
    "NodeSelectorFlags",
    "AnchorPosResolutionParams",
    "AnchorPosOffsetResolutionParams",
]
opaque-types = [
    "mozilla::StyleThinArc", # https://github.com/rust-lang/rust-bindgen/issues/1557
    "mozilla::gfx::.*Point",
    "mozilla::gfx::.*Rect",
    "mozilla::gfx::.*Size",
    "mozilla::gfx::.*Margin",
    "mozilla::gfx::.*Matrix",
    "mozilla::gfx::.*Typed",
    "mozilla::gfx::Matrix4x4TypedFlagged",
    "mozilla::gfx::DrawTarget",
    "mozilla::TouchManager",
    "mozilla::UniquePtr",
    "mozilla::CanvasUsage",
    "std::pair__PCCP",
    "std::namespace::atomic___base", "std::atomic__My_base",
    "std::atomic",
    "std::.*::atomic",
    "std::atomic___base",
    "std::tuple.*", # Causes "Cannot find type _Pred in this scope" error on mac, like rust-skia#571
    "std::.*::tuple.*",
    "std::unique_ptr.*",

    "mozilla::dom::Touch",
    "mozilla::dom::Sequence",
    "mozilla::SmallPointerArray",
    "mozilla::SmallPointerArray_Element",
    "mozilla::dom::Optional",
    "mozilla::dom::OwningNodeOrString_Value",
    "mozilla::dom::Nullable",
    "mozilla::external::AtomicRefCounted",
    "RefPtr_Proxy",
    "RefPtr_Proxy_member_function",
    "nsAutoPtr_Proxy",
    "nsAutoPtr_Proxy_member_function",
    "nsRegion_.*",
    "mozilla::detail::HashTable", # <- We should be able to remove this and
                                  # HashSet below once
                                  # https://github.com/rust-lang/rust-bindgen/pull/1515
                                  # is available
    "mozilla::detail::PointerType",
    "mozilla::HashSet",
    "mozilla::ScrollAxis",  # <- For some reason the alignment of this is 4
                            # for clang.
    "mozilla::SeenPtrs",
    "mozilla::SupportsWeakPtr",
    "SupportsWeakPtr",
    "mozilla::detail::WeakReference",
    "mozilla::WeakPtr",
    "nsWritingIterator_reference", "nsReadingIterator_reference",
    "nsTObserverArray",  # <- Inherits from nsAutoTObserverArray<T, 0>
    "mozilla::DoublyLinkedList",
    "mozilla::SafeDoublyLinkedList",
    "nsTHashtable",  # <- Inheriting from inner typedefs that clang
                     #    doesn't expose properly.
    "nsTBaseHashSet", # <- Ditto
    "nsBaseHashtable", "nsRefCountedHashtable", "nsClassHashtable",  # <- Ditto
    "mozilla::dom::Document_SelectorCache",  # <- Inherits from nsExpirationTracker<.., 4>
    "nsPIDOMWindow",  # <- Takes the vtable from a template parameter, and we can't
                      #    generate it conditionally.
    "JS::Rooted",
    "mozilla::Maybe",
    "mozilla::Variant",
    "mozilla::dom::TreeOrderedArray", # AutoTArray<>
    "gfxSize",  # <- union { struct { T width; T height; }; T components[2] };
    "gfxSize_Super",  # Ditto.
    "StyleAnimationValue", # pulls in a whole bunch of stuff we don't need in the bindings
    "mozilla::dom::.*Callback", # Pulls in ErrorResult and other things that
                                # don't align properly on Linux 32-bit
    "mozilla::SchedulerGroup", # Non-standard-layout packing of field into superclass
    "mozilla::Widget.*Event", # As above
    "mozilla::detail::ThreadLocal.*",
    "std::make_signed_t",
    "mozilla::ProfileChunkedBuffer",
]

# All cbindgen-types are in mod "structs::root::mozilla".
# FIXME(emilio): We probably want to automate this somehow...
cbindgen-types = [
    { gecko = "StyleAnimationIterationCount", servo = "crate::values::computed::AnimationIterationCount" },
    { gecko = "StyleAnimationTimeline", servo = "crate::values::computed::AnimationTimeline" },
    { gecko = "StyleTransitionBehavior", servo = "crate::values::computed::TransitionBehavior" },
    { gecko = "StyleAppearance", servo = "crate::values::specified::Appearance" },
    { gecko = "StyleAspectRatio", servo = "crate::values::computed::position::AspectRatio" },
    { gecko = "StyleAtom", servo = "crate::Atom" },
    { gecko = "StyleComputedFontStretchRange", servo = "crate::font_face::ComputedFontStretchRange" },
    { gecko = "StyleComputedFontStyleDescriptor", servo = "crate::font_face::ComputedFontStyleDescriptor" },
    { gecko = "StyleComputedFontWeightRange", servo = "crate::font_face::ComputedFontWeightRange" },
    { gecko = "StyleComputedTimingFunction", servo = "crate::values::computed::easing::TimingFunction" },
    { gecko = "StylePrefersContrast", servo = "crate::gecko::media_features::PrefersContrast" },
    { gecko = "StyleColorGamut", servo = "crate::gecko::media_features::ColorGamut" },
    { gecko = "StyleCursorKind", servo = "crate::values::computed::ui::CursorKind" },
    { gecko = "StyleDisplay", servo = "crate::values::specified::Display" },
    { gecko = "StyleDisplayMode", servo = "crate::gecko::media_features::DisplayMode" },
    { gecko = "StylePlatform", servo = "crate::gecko::media_features::Platform" },
    { gecko = "StyleGtkThemeFamily", servo = "crate::gecko::media_features::GtkThemeFamily" },
    { gecko = "StylePrefersColorScheme", servo = "crate::queries::values::PrefersColorScheme" },
    { gecko = "StyleScripting", servo = "crate::gecko::media_features::Scripting" },
    { gecko = "StyleDynamicRange", servo = "crate::gecko::media_features::DynamicRange" },
    { gecko = "StyleFillRule", servo = "crate::values::generics::basic_shape::FillRule" },
    { gecko = "StyleFontDisplay", servo = "crate::font_face::FontDisplay" },
    { gecko = "StyleFontFaceSourceListComponent", servo = "crate::font_face::FontFaceSourceListComponent" },
    { gecko = "StyleFontFaceSourceFormatKeyword", servo = "crate::font_face::FontFaceSourceFormatKeyword" },
    { gecko = "StyleFontFaceSourceTechFlags", servo = "crate::font_face::FontFaceSourceTechFlags" },
    { gecko = "StyleFontLanguageOverride", servo = "crate::values::computed::font::FontLanguageOverride" },
    { gecko = "StyleOffsetPath", servo = "crate::values::computed::motion::OffsetPath" },
    { gecko = "StyleOffsetPathFunction", servo = "crate::values::computed::motion::OffsetPathFunction" },
    { gecko = "StyleGenericOffsetPath", servo = "crate::values::generics::motion::OffsetPath" },
    { gecko = "StyleGenericOffsetPathFunction", servo = "crate::values::generics::motion::OffsetPathFunction" },
    { gecko = "StyleComputedMozPrefFeatureValue", servo = "crate::queries::condition::ComputedMozPrefFeatureValue" },
    { gecko = "StyleMozTheme", servo = "crate::values::computed::ui::MozTheme" },
    { gecko = "StyleOffsetPosition", servo = "crate::values::computed::motion::OffsetPosition" },
    { gecko = "StyleOffsetRotate", servo = "crate::values::computed::motion::OffsetRotate" },
    { gecko = "StylePathCommand", servo = "crate::values::specified::svg_path::PathCommand" },
    { gecko = "StyleRayFunction", servo = "crate::values::computed::motion::RayFunction" },
    { gecko = "StyleParserState", servo = "cssparser::ParserState" },
    { gecko = "StyleUnicodeRange", servo = "cssparser::UnicodeRange" },
    { gecko = "StyleOverflowWrap", servo = "crate::values::computed::OverflowWrap" },
    { gecko = "StyleWordBreak", servo = "crate::values::computed::WordBreak" },
    { gecko = "StyleTextJustify", servo = "crate::values::computed::TextJustify" },
    { gecko = "StyleMozControlCharacterVisibility", servo = "crate::values::computed::text::MozControlCharacterVisibility" },
    { gecko = "StyleLineBreak", servo = "crate::values::computed::LineBreak" },
    { gecko = "StyleLineClamp", servo = "crate::values::computed::LineClamp" },
    { gecko = "StyleUserFocus", servo = "crate::values::computed::UserFocus" },
    { gecko = "StyleUserInput", servo = "crate::values::computed::UserInput" },
    { gecko = "StyleUserSelect", servo = "crate::values::computed::UserSelect" },
    { gecko = "StyleBreakBetween", servo = "crate::values::computed::BreakBetween" },
    { gecko = "StyleBreakWithin", servo = "crate::values::computed::BreakWithin" },
    { gecko = "StyleBorderStyle", servo = "crate::values::computed::BorderStyle" },
    { gecko = "StyleOutlineStyle", servo = "crate::values::computed::OutlineStyle" },
    { gecko = "StyleScrollSnapAlign", servo = "crate::values::computed::ScrollSnapAlign" },
    { gecko = "StyleScrollSnapStop", servo = "crate::values::computed::ScrollSnapStop" },
    { gecko = "StyleScrollSnapStrictness", servo = "crate::values::computed::ScrollSnapStrictness" },
    { gecko = "StyleScrollSnapType", servo = "crate::values::computed::ScrollSnapType" },
    { gecko = "StyleAnimationName", servo = "crate::values::computed::AnimationName" },
    { gecko = "StyleAnimationDirection", servo = "crate::values::computed::AnimationDirection" },
    { gecko = "StyleAnimationFillMode", servo = "crate::values::computed::AnimationFillMode" },
    { gecko = "StyleAnimationPlayState", servo = "crate::values::computed::AnimationPlayState" },
    { gecko = "StyleAnimationComposition", servo = "crate::values::computed::AnimationComposition" },
    { gecko = "StyleAnimationDuration", servo = "crate::values::computed::AnimationDuration" },
    { gecko = "StyleTimelineName", servo = "crate::values::computed::TimelineName" },
    { gecko = "StyleScrollAxis", servo = "crate::values::computed::ScrollAxis" },
    { gecko = "StyleViewTimelineInset", servo = "crate::values::computed::ViewTimelineInset" },
    { gecko = "StyleViewTransitionClass", servo = "crate::values::computed::ViewTransitionClass" },
    { gecko = "StyleViewTransitionName", servo = "crate::values::computed::ViewTransitionName" },
    { gecko = "StyleResize", servo = "crate::values::computed::Resize" },
    { gecko = "StyleOverflowClipBox", servo = "crate::values::computed::OverflowClipBox" },
    { gecko = "StyleFloat", servo = "crate::values::computed::Float" },
    { gecko = "StyleClear", servo = "crate::values::computed::Clear" },
    { gecko = "StyleOverscrollBehavior", servo = "crate::values::computed::OverscrollBehavior" },
    { gecko = "StyleTextAlign", servo = "crate::values::computed::TextAlign" },
    { gecko = "StyleTextAlignLast", servo = "crate::values::computed::text::TextAlignLast" },
    { gecko = "StyleTextIndent", servo = "crate::values::computed::text::TextIndent" },
    { gecko = "StyleTextOverflow", servo = "crate::values::computed::TextOverflow" },
    { gecko = "StyleOverflow", servo = "crate::values::computed::Overflow" },
    { gecko = "StyleOverflowAnchor", servo = "crate::values::computed::OverflowAnchor" },
    { gecko = "StyleTextDecorationSkipInk", servo = "crate::values::computed::TextDecorationSkipInk" },
    { gecko = "StyleTextDecorationLength", servo = "crate::values::computed::TextDecorationLength" },
    { gecko = "StyleRubyPosition", servo = "crate::values::computed::RubyPosition" },
    { gecko = "StyleLength", servo = "crate::values::computed::CSSPixelLength" },
    { gecko = "StyleLengthPercentage", servo = "crate::values::computed::LengthPercentage" },
    { gecko = "StyleNonNegativeLengthPercentage", servo = "crate::values::computed::NonNegativeLengthPercentage" },
    { gecko = "StyleGenericLengthPercentageOrAuto", servo = "crate::values::generics::length::LengthPercentageOrAuto" },
    { gecko = "StyleGenericLengthPercentageOrNormal", servo = "crate::values::generics::length::LengthPercentageOrNormal" },
    { gecko = "StyleLengthPercentageOrAuto", servo = "crate::values::computed::LengthPercentageOrAuto" },
    { gecko = "StyleNonNegativeLengthPercentageOrAuto", servo = "crate::values::computed::NonNegativeLengthPercentageOrAuto" },
    { gecko = "StyleInset", servo = "crate::values::computed::position::Inset" },
    { gecko = "StyleRect", servo = "crate::values::generics::rect::Rect" },
    { gecko = "StyleIntersectionObserverMargin", servo = "crate::values::specified::intersection_observer::IntersectionObserverMargin" },
    { gecko = "StyleGenericSize", servo = "crate::values::generics::length::Size" },
    { gecko = "StyleGenericMaxSize", servo = "crate::values::generics::length::MaxSize" },
    { gecko = "StyleGenericFlexBasis", servo = "crate::values::generics::flex::FlexBasis" },
    { gecko = "StyleSize", servo = "crate::values::computed::Size" },
    { gecko = "StyleMaxSize", servo = "crate::values::computed::MaxSize" },
    { gecko = "StyleFlexBasis", servo = "crate::values::computed::FlexBasis" },
    { gecko = "StylePosition", servo = "crate::values::computed::Position" },
    { gecko = "StylePositionOrAuto", servo = "crate::values::computed::PositionOrAuto" },
    { gecko = "StyleGenericPositionOrAuto", servo = "crate::values::generics::position::PositionOrAuto" },
    { gecko = "StyleBackgroundSize", servo = "crate::values::computed::BackgroundSize" },
    { gecko = "StyleGenericBackgroundSize", servo = "crate::values::generics::background::BackgroundSize" },
    { gecko = "StyleBorderImageRepeat", servo = "crate::values::specified::border::BorderImageRepeat" },
    { gecko = "StyleBorderImageRepeatKeyword", servo = "crate::values::specified::border::BorderImageRepeatKeyword" },
    { gecko = "StyleBorderImageSlice", servo = "crate::values::computed::BorderImageSlice" },
    { gecko = "StyleBorderSpacing", servo = "crate::values::computed::BorderSpacing" },
    { gecko = "StyleGenericLengthOrNumber", servo = "crate::values::generics::length::LengthOrNumber" },
    { gecko = "StyleCSSPixelLength", servo = "crate::values::computed::length::CSSPixelLength" },
    { gecko = "StyleNonNegativeLength", servo = "crate::values::computed::NonNegativeLength" },
    { gecko = "StyleNonNegativeNumber", servo = "crate::values::computed::NonNegativeNumber" },
    { gecko = "StyleZeroToOneNumber", servo = "crate::values::computed::ZeroToOneNumber" },
    { gecko = "StylePercentage", servo = "crate::values::computed::Percentage" },
    { gecko = "StylePerspective", servo = "crate::values::computed::Perspective" },
    { gecko = "StyleGenericPerspective", servo = "crate::values::generics::box_::Perspective" },
    { gecko = "StyleZIndex", servo = "crate::values::computed::ZIndex" },
    { gecko = "StyleGenericZIndex", servo = "crate::values::generics::position::ZIndex" },
    { gecko = "StyleTransformBox", servo = "crate::values::computed::TransformBox" },
    { gecko = "StyleTransformOrigin", servo = "crate::values::computed::TransformOrigin" },
    { gecko = "StyleTransformStyle", servo = "crate::values::computed::TransformStyle" },
    { gecko = "StyleGenericBorderRadius", servo = "crate::values::generics::border::BorderRadius" },
    { gecko = "StyleLetterSpacing", servo = "crate::values::computed::text::LetterSpacing" },
    { gecko = "StyleGenericLineHeight", servo = "crate::values::generics::font::LineHeight" },
    { gecko = "StyleCaretColor", servo = "crate::values::computed::color::CaretColor" },
    { gecko = "StyleContain", servo = "crate::values::computed::Contain" },
    { gecko = "StyleContainerType", servo = "crate::values::computed::ContainerType" },
    { gecko = "StyleContainerName", servo = "crate::values::computed::ContainerName" },
    { gecko = "StyleRestyleHint", servo = "crate::invalidation::element::restyle_hints::RestyleHint" },
    { gecko = "StyleTouchAction", servo = "crate::values::computed::TouchAction" },
    { gecko = "StyleWillChange", servo = "crate::values::specified::box_::WillChange" },
    { gecko = "StyleColorScheme", servo = "crate::values::specified::color::ColorScheme" },
    { gecko = "StyleColorSchemeFlags", servo = "crate::values::specified::color::ColorSchemeFlags" },
    { gecko = "StyleTextDecorationLine", servo = "crate::values::computed::TextDecorationLine" },
    { gecko = "StyleMasonryAutoFlow", servo = "crate::values::specified::MasonryAutoFlow" },
    { gecko = "StyleMasonryPlacement", servo = "crate::values::specified::MasonryPlacement" },
    { gecko = "StyleMasonryItemOrder", servo = "crate::values::specified::MasonryItemOrder" },
    { gecko = "StyleTextTransform", servo = "crate::values::computed::TextTransform" },
    { gecko = "StyleTextUnderlinePosition", servo = "crate::values::computed::TextUnderlinePosition" },
    { gecko = "StyleStrong", servo = "crate::gecko_bindings::sugar::ownership::Strong" },
    { gecko = "StyleGenericFontFamily", servo = "crate::values::computed::font::GenericFontFamily" },
    { gecko = "StyleGenericPosition", servo = "crate::values::generics::position::GenericPosition" },
    { gecko = "StyleGenericCounterPair", servo = "crate::values::generics::counters::GenericCounterPair" },
    { gecko = "StyleGenericShapeRadius", servo = "crate::values::generics::basic_shape::GenericShapeRadius" },
    { gecko = "StyleGenericClipRect", servo = "crate::values::generics::GenericClipRect" },
    { gecko = "StyleGenericCursorImage", servo = "crate::values::generics::ui::GenericCursorImage" },
    { gecko = "StyleFontFamily", servo = "crate::values::computed::font::FontFamily" },
    { gecko = "StyleFontSizeAdjust", servo = "crate::values::computed::font::FontSizeAdjust" },
    { gecko = "StyleFontFamilyNameSyntax", servo = "crate::values::computed::font::FontFamilyNameSyntax" },
    { gecko = "StyleGenericColor", servo = "crate::values::generics::color::Color" },
    { gecko = "StyleSystemColor", servo = "crate::values::specified::color::SystemColor" },
    { gecko = "StyleSystemFont", servo = "crate::values::specified::font::SystemFont" },
    { gecko = "StyleGenericColorOrAuto", servo = "crate::values::generics::color::ColorOrAuto" },
    { gecko = "StyleGenericScrollbarColor", servo = "crate::values::generics::ui::ScrollbarColor" },
    { gecko = "StyleColorComponent", servo = "crate::color::component::ColorComponent" },
    { gecko = "StyleAbsoluteColor", servo = "crate::color::AbsoluteColor" },
    { gecko = "StyleOrigin", servo = "crate::stylesheets::Origin" },
    { gecko = "StyleGenericVerticalAlign", servo = "crate::values::generics::box_::VerticalAlign" },
    { gecko = "StyleVerticalAlignKeyword", servo = "crate::values::generics::box_::VerticalAlignKeyword" },
    { gecko = "StyleGenericBasicShape", servo = "crate::values::generics::basic_shape::BasicShape" },
    { gecko = "StyleBasicShape", servo = "crate::values::computed::basic_shape::BasicShape" },
    { gecko = "StyleGenericInsetRect", servo = "crate::values::generics::basic_shape::InsetRect" },
    { gecko = "StyleInsetRect", servo = "crate::values::computed::basic_shape::InsetRect" },
    { gecko = "StyleShape", servo = "crate::values::computed::basic_shape::Shape" },
    { gecko = "StyleShapeCommand", servo = "crate::values::computed::basic_shape::ShapeCommand" },
    { gecko = "StyleGenericShapeCommand", servo = "crate::values::generics::basic_shape::ShapeCommand" },
    { gecko = "StyleArcSlice", servo = "style_traits::arc_slice::ArcSlice" },
    { gecko = "StyleForgottenArcSlicePtr", servo = "style_traits::arc_slice::ForgottenArcSlicePtr" },
    { gecko = "StyleOwnedSlice", servo = "style_traits::owned_slice::OwnedSlice" },
    { gecko = "StyleMozContextProperties", servo = "crate::values::specified::svg::MozContextProperties" },
    { gecko = "StyleQuotes", servo = "crate::values::specified::list::Quotes" },
    { gecko = "StyleOwnedStr", servo = "style_traits::owned_str::OwnedStr" },
    { gecko = "StyleGenericBoxShadow", servo = "crate::values::generics::effects::BoxShadow" },
    { gecko = "StyleGenericSimpleShadow", servo = "crate::values::generics::effects::SimpleShadow" },
    { gecko = "StyleGenericTransformOperation", servo = "crate::values::generics::transform::TransformOperation" },
    { gecko = "StyleGenericTransform", servo = "crate::values::generics::transform::Transform" },
    { gecko = "StyleGenericScale", servo = "crate::values::generics::transform::Scale" },
    { gecko = "StyleGenericRotate", servo = "crate::values::generics::transform::Rotate" },
    { gecko = "StyleGenericTranslate", servo = "crate::values::generics::transform::Translate" },
    { gecko = "StyleAngle", servo = "crate::values::computed::Angle" },
    { gecko = "StyleGenericBorderImageSideWidth", servo = "crate::values::generics::border::BorderImageSideWidth" },
    { gecko = "StyleGenericUrlOrNone", servo = "crate::values::generics::url::UrlOrNone" },
    { gecko = "StyleGenericCalcNode", servo = "crate::values::generics::calc::GenericCalcNode" },
    { gecko = "StyleCssUrl", servo = "crate::gecko::url::CssUrl" },
    { gecko = "StyleSpecifiedUrl", servo = "crate::gecko::url::SpecifiedUrl" },
    { gecko = "StyleComputedUrl", servo = "crate::gecko::url::ComputedUrl" },
    { gecko = "StyleLoadData", servo = "crate::gecko::url::LoadData" },
    { gecko = "StyleGenericFilter", servo = "crate::values::generics::effects::Filter" },
    { gecko = "StyleGenericGradient", servo = "crate::values::generics::image::Gradient" },
    { gecko = "StyleLineDirection", servo = "crate::values::computed::image::LineDirection" },
    { gecko = "StyleGridTemplateAreas", servo = "crate::values::computed::position::GridTemplateAreas" },
    { gecko = "StyleGenericGridLine", servo = "crate::values::generics::grid::GridLine" },
    { gecko = "StyleGenericTrackSize", servo = "crate::values::generics::grid::TrackSize" },
    { gecko = "StyleGenericTrackBreadth", servo = "crate::values::generics::grid::TrackBreadth" },
    { gecko = "StyleGenericImplicitGridTracks", servo = "crate::values::generics::grid::ImplicitGridTracks" },
    { gecko = "StyleImplicitGridTracks", servo = "crate::values::computed::ImplicitGridTracks" },
    { gecko = "StyleNumberOrPercentage", servo = "crate::values::computed::NumberOrPercentage" },
    { gecko = "StyleGenericSVGPaint", servo = "crate::values::generics::svg::SVGPaint" },
    { gecko = "StyleGenericTrackRepeat", servo = "crate::values::generics::grid::TrackRepeat" },
    { gecko = "StyleGenericTrackListValue", servo = "crate::values::generics::grid::TrackListValue" },
    { gecko = "StyleGenericTrackList", servo = "crate::values::generics::grid::TrackList" },
    { gecko = "StyleGenericGridTemplateComponent", servo = "crate::values::generics::grid::GridTemplateComponent" },
    { gecko = "StyleTextEmphasisStyle", servo = "crate::values::computed::text::TextEmphasisStyle" },
    { gecko = "StyleTextEmphasisPosition", servo = "crate::values::computed::TextEmphasisPosition" },
    { gecko = "StyleFontVariantAlternates", servo = "crate::values::specified::font::FontVariantAlternates" },
    { gecko = "StyleSVGPaintOrder", servo = "crate::values::specified::svg::SVGPaintOrder" },
    { gecko = "StyleClipRectOrAuto", servo = "crate::values::computed::ClipRectOrAuto" },
    { gecko = "StyleCounterReset", servo = "crate::values::computed::CounterReset" },
    { gecko = "StyleCounterSet", servo = "crate::values::computed::CounterSet" },
    { gecko = "StyleCounterIncrement", servo = "crate::values::computed::CounterIncrement" },
    { gecko = "StyleContent", servo = "crate::values::computed::counters::Content" },
    { gecko = "StyleSymbolsType", servo = "crate::counter_style::SymbolsType" },
    { gecko = "StyleCounterStyle", servo = "crate::counter_style::CounterStyle" },
    { gecko = "StyleComputedJustifyItems", servo = "crate::values::computed::align::ComputedJustifyItems" },
    { gecko = "StyleAlignItems", servo = "crate::values::computed::AlignItems" },
    { gecko = "StyleJustifySelf", servo = "crate::values::computed::JustifySelf" },
    { gecko = "StyleAlignSelf", servo = "crate::values::computed::AlignSelf" },
    { gecko = "StyleAlignContent", servo = "crate::values::computed::align::AlignContent" },
    { gecko = "StyleJustifyContent", servo = "crate::values::computed::align::JustifyContent" },
    { gecko = "StyleComputedValueFlags", servo = "crate::computed_value_flags::ComputedValueFlags" },
    { gecko = "StyleImage", servo = "crate::values::computed::Image" },
    { gecko = "StyleShapeOutside", servo = "crate::values::computed::basic_shape::ShapeOutside" },
    { gecko = "StyleClipPath", servo = "crate::values::computed::basic_shape::ClipPath" },
    { gecko = "StyleGridAutoFlow", servo = "crate::values::computed::GridAutoFlow" },
    { gecko = "StyleCursor", servo = "crate::values::computed::Cursor" },
    { gecko = "StyleSVGStrokeDashArray", servo = "crate::values::computed::svg::SVGStrokeDashArray" },
    { gecko = "StyleSVGWidth", servo = "crate::values::computed::svg::SVGWidth" },
    { gecko = "StyleSVGOpacity", servo = "crate::values::computed::svg::SVGOpacity" },
    { gecko = "StyleSVGLength", servo = "crate::values::computed::svg::SVGLength" },
    { gecko = "StyleFontSizeKeyword", servo = "crate::values::specified::font::FontSizeKeyword" },
    { gecko = "StyleCaptionSide", servo = "crate::values::computed::table::CaptionSide" },
    { gecko = "StylePageName", servo = "crate::values::specified::page::PageName" },
    { gecko = "StylePageOrientation", servo = "crate::values::generics::page::PageOrientation" },
    { gecko = "StylePageSize", servo = "crate::values::computed::page::PageSize" },
    { gecko = "StyleDProperty", servo = "crate::values::specified::svg::DProperty" },
    { gecko = "StyleVectorEffect", servo = "crate::values::specified::svg::VectorEffect" },
    { gecko = "StyleImageRendering", servo = "crate::values::computed::ImageRendering" },
    { gecko = "StylePrintColorAdjust", servo = "crate::values::computed::PrintColorAdjust" },
    { gecko = "StyleForcedColorAdjust", servo = "crate::values::computed::ForcedColorAdjust" },
    { gecko = "StyleForcedColors", servo = "crate::values::specified::color::ForcedColors" },
    { gecko = "StyleScrollbarGutter", servo = "crate::values::computed::ScrollbarGutter" },
    { gecko = "StyleHyphenateCharacter", servo = "crate::values::computed::HyphenateCharacter" },
    { gecko = "StyleHyphenateLimitChars", servo = "crate::values::computed::HyphenateLimitChars" },
    { gecko = "StyleColumnCount", servo = "crate::values::computed::ColumnCount" },
    { gecko = "StyleContentVisibility", servo = "crate::values::computed::ContentVisibility" },
    { gecko = "StyleContainIntrinsicSize", servo = "crate::values::computed::ContainIntrinsicSize" },
    { gecko = "StyleFontStyle", servo = "crate::values::computed::font::FontStyle" },
    { gecko = "StyleFontWeight", servo = "crate::values::computed::font::FontWeight" },
    { gecko = "StyleFontStretch", servo = "crate::values::computed::font::FontStretch" },
    { gecko = "StyleFontPalette", servo = "crate::values::computed::font::FontPalette" },
    { gecko = "StyleFontSynthesis", servo = "crate::values::computed::font::FontSynthesis" },
    { gecko = "StyleFontSynthesisStyle", servo = "crate::values::computed::font::FontSynthesisStyle" },
    { gecko = "StyleBoolInteger", servo = "crate::values::computed::BoolInteger" },
    { gecko = "StyleTime", servo = "crate::values::computed::Time" },
    { gecko = "StyleXTextScale", servo = "crate::values::computed::XTextScale" },
    { gecko = "StyleZoom", servo = "crate::values::computed::Zoom" },
    { gecko = "StyleTransitionProperty", servo = "crate::values::computed::TransitionProperty" },
    { gecko = "StyleAnimationValueMap", servo = "crate::properties::animated_properties::AnimationValueMap" },
    { gecko = "StyleAuthorStyles", servo = "crate::gecko::data::AuthorStyles" },
    { gecko = "StyleUseCounters", servo = "crate::use_counters::UseCounters" },
    { gecko = "StyleStylesheetContents", servo = "crate::stylesheets::StylesheetContents" },
    { gecko = "StyleAnimationValue", servo = "crate::properties::animated_properties::AnimationValue" },
    { gecko = "StyleLockedDeclarationBlock", servo = "crate::gecko::arc_types::LockedDeclarationBlock" },
    { gecko = "StyleLockedMediaList", servo = "crate::gecko::arc_types::LockedMediaList" },
    { gecko = "StyleLockedImportRule", servo = "crate::gecko::arc_types::LockedImportRule" },
    { gecko = "StyleLockedFontFaceRule", servo = "crate::gecko::arc_types::LockedFontFaceRule" },
    { gecko = "StyleBaselineSource", servo = "crate::values::computed::BaselineSource" },
    { gecko = "StyleListStyleType", servo = "crate::values::computed::ListStyleType" },
    { gecko = "StyleAu", servo = "app_units::Au" },
    { gecko = "StyleAnchorName", servo = "crate::values::computed::position::AnchorName" },
    { gecko = "StyleAnchorScope", servo = "crate::values::computed::position::AnchorScope" },
    { gecko = "StylePositionAnchor", servo = "crate::values::computed::position::PositionAnchor" },
    { gecko = "StylePositionArea", servo = "crate::values::computed::position::PositionArea" },
    { gecko = "StylePositionVisibility", servo = "crate::values::computed::position::PositionVisibility" },
    { gecko = "StylePositionTryFallbacks", servo = "crate::values::computed::position::PositionTryFallbacks" },
    { gecko = "StylePositionTryOrder", servo = "crate::values::computed::position::PositionTryOrder" },
    { gecko = "StyleFontVariantEastAsian", servo = "crate::values::computed::font::FontVariantEastAsian" },
    { gecko = "StyleFontVariantLigatures", servo = "crate::values::computed::font::FontVariantLigatures" },
    { gecko = "StyleFontVariantNumeric", servo = "crate::values::computed::font::FontVariantNumeric" },
    { gecko = "StyleInitialLetter", servo = "crate::values::computed::text::InitialLetter" },
    { gecko = "StylePointerEvents", servo = "crate::values::computed::ui::PointerEvents" },
    { gecko = "StyleInert", servo = "crate::values::computed::ui::Inert" },
    { gecko = "StyleMargin", servo = "crate::values::computed::length::Margin" },
    { gecko = "StyleGenericAnchorFunction", servo = "crate::values::generics::position::GenericAnchorFunction" },
    { gecko = "StylePositionProperty", servo = "crate::values::computed::PositionProperty" },
    { gecko = "StyleQueryFontMetricsFlags", servo = "crate::values::specified::font::QueryFontMetricsFlags" },
    { gecko = "StyleWritingModeProperty", servo = "crate::values::computed::box_::WritingModeProperty" },
    { gecko = "StyleGenericAnchorSide", servo = "crate::values::generics::position::GenericAnchorSide" },
]

mapped-generic-types = [
    { generic = true, gecko = "mozilla::RustCell", servo = "::std::cell::Cell" },
    { generic = false, gecko = "ServoNodeData", servo = "atomic_refcell::AtomicRefCell<crate::data::ElementData>" },
    { generic = false, gecko = "mozilla::ServoWritingMode", servo = "crate::logical_geometry::WritingMode" },
    { generic = false, gecko = "mozilla::ServoComputedCustomProperties", servo = "crate::custom_properties::ComputedCustomProperties" },
    { generic = false, gecko = "mozilla::ServoRuleNode", servo = "Option<crate::rule_tree::StrongRuleNode>" },
    { generic = false, gecko = "nsACString", servo = "nsstring::nsACString" },
    { generic = false, gecko = "nsAString", servo = "nsstring::nsAString" },
    { generic = false, gecko = "nsCString", servo = "nsstring::nsCString" },
    { generic = false, gecko = "nsString", servo = "nsstring::nsString" },
    { generic = true, gecko = "nsTArray", servo = "thin_vec::ThinVec" },
    { generic = true, gecko = "CopyableTArray", servo = "thin_vec::ThinVec" },
]

allowlist-functions = ["Servo_.*", "Gecko_.*"]
