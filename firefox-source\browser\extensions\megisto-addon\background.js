/**
 * <PERSON><PERSON><PERSON> Browser - Background Script
 * Handles system-level operations for cookie blocking and YouTube management
 */

class MegistoCore {
  constructor() {
    this.rules = new Map();
    this.preferences = {
      cookieBlocking: true,
      youtubeEnhancement: true,
      youtubeAutoplay: false,
      youtubeDefaultQuality: '720p',
      youtubeBlockRelated: true
    };
    
    this.init();
  }
  
  async init() {
    console.log('Megisto Core: Initializing...');
    
    // Load blocking rules
    await this.loadRules();
    
    // Set up preferences
    await this.loadPreferences();
    
    // Set up message listeners
    this.setupMessageHandlers();
    
    // Set up web request blocking
    this.setupWebRequestBlocking();
    
    console.log('Megisto Core: Initialized successfully');
  }
  
  async loadRules() {
    try {
      const response = await fetch(browser.runtime.getURL('rules/default-rules.json'));
      const rulesData = await response.json();
      
      rulesData.rules.forEach(rule => {
        this.rules.set(rule.domain, rule);
      });
      
      console.log(`Megisto Core: Loaded ${this.rules.size} blocking rules`);
    } catch (error) {
      console.error('Megisto Core: Failed to load rules:', error);
    }
  }
  
  async loadPreferences() {
    try {
      const stored = await browser.storage.local.get('megistoPreferences');
      if (stored.megistoPreferences) {
        this.preferences = { ...this.preferences, ...stored.megistoPreferences };
      }
      console.log('Megisto Core: Preferences loaded:', this.preferences);
    } catch (error) {
      console.error('Megisto Core: Failed to load preferences:', error);
    }
  }
  
  async savePreferences() {
    try {
      await browser.storage.local.set({ megistoPreferences: this.preferences });
      console.log('Megisto Core: Preferences saved');
    } catch (error) {
      console.error('Megisto Core: Failed to save preferences:', error);
    }
  }
  
  setupMessageHandlers() {
    browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
      switch (message.type) {
        case 'GET_RULES':
          const domain = new URL(sender.tab.url).hostname;
          const rule = this.rules.get(domain) || this.rules.get('*');
          sendResponse({ rule });
          break;
          
        case 'GET_PREFERENCES':
          sendResponse({ preferences: this.preferences });
          break;
          
        case 'UPDATE_PREFERENCES':
          this.preferences = { ...this.preferences, ...message.preferences };
          this.savePreferences();
          sendResponse({ success: true });
          break;
          
        case 'BLOCK_POPUP':
          this.handlePopupBlock(sender.tab.id, message.url);
          break;
          
        default:
          console.warn('Megisto Core: Unknown message type:', message.type);
      }
    });
  }
  
  setupWebRequestBlocking() {
    // Block known tracking and popup scripts
    browser.webRequest.onBeforeRequest.addListener(
      (details) => {
        const url = details.url.toLowerCase();
        
        // Block common popup/overlay scripts
        if (url.includes('popup') || 
            url.includes('overlay') || 
            url.includes('modal') ||
            url.includes('consent-manager') ||
            url.includes('cookie-banner')) {
          console.log('Megisto Core: Blocked popup script:', details.url);
          return { cancel: true };
        }
        
        return {};
      },
      { urls: ["<all_urls>"] },
      ["blocking"]
    );
  }
  
  handlePopupBlock(tabId, url) {
    console.log(`Megisto Core: Blocking popup in tab ${tabId}:`, url);
    // Additional popup blocking logic can be added here
  }
}

// Initialize Megisto Core when the background script loads
const megistoCore = new MegistoCore();
