# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies
#
# If you believe there's an error in this file please file an
# issue against the rust-lang/cargo repository. If you're
# editing this file be aware that the upstream Cargo.toml
# will likely look very different (and much more reasonable)

[package]
edition = "2018"
name = "chunky-vec"
version = "0.1.0"
authors = ["<PERSON> Glastonbury <<EMAIL>>"]
description = "A pin safe, append only vector never moves the backing store for an element.\n"
keywords = ["data-structure", "vector", "pin"]
categories = ["data-structures"]
license = "Apache-2.0/MIT"
repository = "https://github.com/djg/chunky-vec"

[dependencies]
