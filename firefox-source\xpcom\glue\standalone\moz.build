# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

SOURCES += [
    "../FileUtils.cpp",
    "../MemUtils.cpp",
    "nsXPCOMGlue.cpp",
]

Library("xpcomglue")

FORCE_STATIC_LIB = True

if CONFIG["CC_TYPE"] == "clang-cl":
    DEFINES["_USE_ANSI_CPP"] = True
    # Don't include directives about which CRT to use
    CFLAGS += ["-Zl"]
    CXXFLAGS += ["-Zl"]

DEFINES["XPCOM_GLUE"] = True

LOCAL_INCLUDES += [
    "../../build",
    "../../threads",
]

# Don't use STL wrappers here (i.e. wrapped <new>); they require mozalloc
DisableStlWrapping()

DIST_INSTALL = True

if CONFIG["MOZ_WIDGET_TOOLKIT"] == "gtk":
    CXXFLAGS += CONFIG["GLIB_CFLAGS"]
