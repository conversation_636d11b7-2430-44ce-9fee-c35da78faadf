# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.61.0"
name = "chrono"
version = "0.4.40"
build = false
include = [
    "src/*",
    "tests/*.rs",
    "LICENSE.txt",
    "CITATION.cff",
]
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "Date and time library for Rust"
homepage = "https://github.com/chronotope/chrono"
documentation = "https://docs.rs/chrono/"
readme = "README.md"
keywords = [
    "date",
    "time",
    "calendar",
]
categories = ["date-and-time"]
license = "MIT OR Apache-2.0"
repository = "https://github.com/chronotope/chrono"

[package.metadata.docs.rs]
features = [
    "arbitrary",
    "rkyv",
    "serde",
    "unstable-locales",
]
rustdoc-args = [
    "--cfg",
    "docsrs",
]

[package.metadata.playground]
features = ["serde"]

[features]
__internal_bench = []
alloc = []
clock = [
    "winapi",
    "iana-time-zone",
    "android-tzdata",
    "now",
]
default = [
    "clock",
    "std",
    "oldtime",
    "wasmbind",
]
libc = []
now = ["std"]
oldtime = []
rkyv = [
    "dep:rkyv",
    "rkyv/size_32",
]
rkyv-16 = [
    "dep:rkyv",
    "rkyv?/size_16",
]
rkyv-32 = [
    "dep:rkyv",
    "rkyv?/size_32",
]
rkyv-64 = [
    "dep:rkyv",
    "rkyv?/size_64",
]
rkyv-validation = ["rkyv?/validation"]
std = ["alloc"]
unstable-locales = ["pure-rust-locales"]
wasmbind = [
    "wasm-bindgen",
    "js-sys",
]
winapi = ["windows-link"]

[lib]
name = "chrono"
path = "src/lib.rs"

[[test]]
name = "dateutils"
path = "tests/dateutils.rs"

[[test]]
name = "wasm"
path = "tests/wasm.rs"

[[test]]
name = "win_bindings"
path = "tests/win_bindings.rs"

[dependencies.arbitrary]
version = "1.0.0"
features = ["derive"]
optional = true

[dependencies.num-traits]
version = "0.2"
default-features = false

[dependencies.pure-rust-locales]
version = "0.8"
optional = true

[dependencies.rkyv]
version = "0.7.43"
optional = true
default-features = false

[dependencies.serde]
version = "1.0.99"
optional = true
default-features = false

[dev-dependencies.bincode]
version = "1.3.0"

[dev-dependencies.serde_derive]
version = "1"
default-features = false

[dev-dependencies.serde_json]
version = "1"

[dev-dependencies.similar-asserts]
version = "1.6.1"

[target.'cfg(all(target_arch = "wasm32", not(any(target_os = "emscripten", target_os = "wasi"))))'.dependencies.js-sys]
version = "0.3"
optional = true

[target.'cfg(all(target_arch = "wasm32", not(any(target_os = "emscripten", target_os = "wasi"))))'.dependencies.wasm-bindgen]
version = "0.2"
optional = true

[target.'cfg(all(target_arch = "wasm32", not(any(target_os = "emscripten", target_os = "wasi"))))'.dev-dependencies.wasm-bindgen-test]
version = "0.3"

[target.'cfg(target_os = "android")'.dependencies.android-tzdata]
version = "0.1.1"
optional = true

[target."cfg(unix)".dependencies.iana-time-zone]
version = "0.1.45"
features = ["fallback"]
optional = true

[target."cfg(windows)".dependencies.windows-link]
version = "0.1"
optional = true

[target."cfg(windows)".dev-dependencies.windows-bindgen]
version = "0.60"
