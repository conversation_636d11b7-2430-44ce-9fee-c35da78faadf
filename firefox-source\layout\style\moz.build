# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

with Files("**"):
    BUG_COMPONENT = ("Core", "CSS Parsing and Computation")

with Files("nsComputedDOMStyle.*"):
    BUG_COMPONENT = ("Core", "DOM: CSS Object Model")

with Files("nsROCSSPrimitiveValue.*"):
    BUG_COMPONENT = ("Core", "DOM: CSS Object Model")

with Files("CSSRuleList.*"):
    BUG_COMPONENT = ("Core", "DOM: CSS Object Model")

with Files("nsDOM*"):
    BUG_COMPONENT = ("Core", "DOM: CSS Object Model")

with Files("AnimationCollection.*"):
    BUG_COMPONENT = ("Core", "CSS Transitions and Animations")

with Files("AnimatedPropertyID*"):
    BUG_COMPONENT = ("Core", "CSS Transitions and Animations")

with Files("AnimationCommon.*"):
    BUG_COMPONENT = ("Core", "CSS Transitions and Animations")

with Files("nsAnimationManager.*"):
    BUG_COMPONENT = ("Core", "CSS Transitions and Animations")

with Files("nsTransitionManager.*"):
    BUG_COMPONENT = ("Core", "CSS Transitions and Animations")

with Files("StyleAnimationValue.*"):
    BUG_COMPONENT = ("Core", "CSS Transitions and Animations")

TEST_DIRS += ["test"]

EXPORTS += [
    "!nsCSSPropertyID.h",
    "AnimationCommon.h",
    "CounterStyleManager.h",
    "nsAnimationManager.h",
    "nsComputedDOMStyle.h",
    "nsCSSAnonBoxes.h",
    "nsCSSAnonBoxList.h",
    "nsCSSCounterDescList.h",
    "nsCSSFontDescList.h",
    "nsCSSPropertyIDSet.h",
    "nsCSSProps.h",
    "nsCSSPseudoElementList.h",
    "nsCSSPseudoElements.h",
    "nsCSSValue.h",
    "nsDOMCSSAttrDeclaration.h",
    "nsDOMCSSDeclaration.h",
    "nsFontFaceLoader.h",
    "nsICSSDeclaration.h",
    "nsICSSLoaderObserver.h",
    "nsStyleAutoArray.h",
    "nsStyleConsts.h",
    "nsStyleStruct.h",
    "nsStyleStructFwd.h",
    "nsStyleStructInlines.h",
    "nsStyleStructList.h",
    "nsStyleTransformMatrix.h",
    "nsStyleUtil.h",
]

EXPORTS.mozilla += [
    "!ServoCSSPropList.h",
    "AnimatedPropertyID.h",
    "AnimatedPropertyIDSet.h",
    "AnimationCollection.h",
    "AttributeStyles.h",
    "BindgenUniquePtr.h",
    "BuiltInStyleSheetList.h",
    "BuiltInStyleSheets.h",
    "CachedInheritingStyles.h",
    "ComputedStyle.h",
    "ComputedStyleInlines.h",
    "CSSEnabledState.h",
    "CSSPropFlags.h",
    "DeclarationBlock.h",
    "DocumentStyleRootIterator.h",
    "FontLoaderUtils.h",
    "FontPreloader.h",
    "GeckoBindings.h",
    "GlobalStyleSheetCache.h",
    "ImportScanner.h",
    "LayerAnimationInfo.h",
    "MappedDeclarationsBuilder.h",
    "MediaFeatureChange.h",
    "PostTraversalTask.h",
    "PreferenceSheet.h",
    "PreloadedStyleSheet.h",
    "PseudoStyleType.h",
    "RustCell.h",
    "ServoBindings.h",
    "ServoBindingTypes.h",
    "ServoBoxedTypeList.h",
    "ServoComputedData.h",
    "ServoCSSParser.h",
    "ServoCSSRuleList.h",
    "ServoElementSnapshot.h",
    "ServoElementSnapshotTable.h",
    "ServoLockedArcTypeList.h",
    "ServoStyleConstsForwards.h",
    "ServoStyleConstsInlines.h",
    "ServoStyleSet.h",
    "ServoStyleSetInlines.h",
    "ServoTraversalStatistics.h",
    "ServoTypes.h",
    "ServoUtils.h",
    "ShadowParts.h",
    "SharedStyleSheetCache.h",
    "SharedSubResourceCache.h",
    "StyleAnimationValue.h",
    "StyleColorInlines.h",
    "StyleSheet.h",
    "StyleSheetInfo.h",
    "StyleSheetInlines.h",
    "TimelineCollection.h",
    "TimelineManager.h",
    "URLExtraData.h",
]

EXPORTS.mozilla.dom += [
    "CSS.h",
    "CSSContainerRule.h",
    "CSSCounterStyleRule.h",
    "CSSFontFaceRule.h",
    "CSSFontFeatureValuesRule.h",
    "CSSFontPaletteValuesRule.h",
    "CSSImportRule.h",
    "CSSKeyframeRule.h",
    "CSSKeyframesRule.h",
    "CSSLayerBlockRule.h",
    "CSSLayerStatementRule.h",
    "CSSMarginRule.h",
    "CSSMediaRule.h",
    "CSSMozDocumentRule.h",
    "CSSNamespaceRule.h",
    "CSSNestedDeclarations.h",
    "CSSPageRule.h",
    "CSSPositionTryRule.h",
    "CSSPropertyRule.h",
    "CSSRuleList.h",
    "CSSScopeRule.h",
    "CSSStartingStyleRule.h",
    "CSSStyleRule.h",
    "CSSSupportsRule.h",
    "CSSValue.h",
    "FontFace.h",
    "FontFaceImpl.h",
    "FontFaceSet.h",
    "FontFaceSetDocumentImpl.h",
    "FontFaceSetImpl.h",
    "FontFaceSetIterator.h",
    "FontFaceSetWorkerImpl.h",
    "MediaList.h",
    "MediaQueryList.h",
    "PaintWorkletGlobalScope.h",
]

EXPORTS.mozilla.css += [
    "DocumentMatchingFunction.h",
    "ErrorReporter.h",
    "GroupRule.h",
    "ImageLoader.h",
    "Loader.h",
    "Rule.h",
    "SheetLoadData.h",
    "SheetParsingMode.h",
    "StreamLoader.h",
    "StylePreloadKind.h",
]

UNIFIED_SOURCES += [
    "AnimationCollection.cpp",
    "AttributeStyles.cpp",
    "CachedInheritingStyles.cpp",
    "ComputedStyle.cpp",
    "CounterStyleManager.cpp",
    "CSS.cpp",
    "CSSContainerRule.cpp",
    "CSSCounterStyleRule.cpp",
    "CSSFontFaceRule.cpp",
    "CSSFontFeatureValuesRule.cpp",
    "CSSFontPaletteValuesRule.cpp",
    "CSSImportRule.cpp",
    "CSSKeyframeRule.cpp",
    "CSSKeyframesRule.cpp",
    "CSSLayerBlockRule.cpp",
    "CSSLayerStatementRule.cpp",
    "CSSMarginRule.cpp",
    "CSSMediaRule.cpp",
    "CSSMozDocumentRule.cpp",
    "CSSNamespaceRule.cpp",
    "CSSNestedDeclarations.cpp",
    "CSSPageRule.cpp",
    "CSSPositionTryRule.cpp",
    "CSSPropertyRule.cpp",
    "CSSRuleList.cpp",
    "CSSScopeRule.cpp",
    "CSSStartingStyleRule.cpp",
    "CSSStyleRule.cpp",
    "CSSSupportsRule.cpp",
    "DeclarationBlock.cpp",
    "DocumentStyleRootIterator.cpp",
    "ErrorReporter.cpp",
    "FontFace.cpp",
    "FontFaceImpl.cpp",
    "FontFaceSet.cpp",
    "FontFaceSetDocumentImpl.cpp",
    "FontFaceSetImpl.cpp",
    "FontFaceSetIterator.cpp",
    "FontFaceSetWorkerImpl.cpp",
    "FontLoaderUtils.cpp",
    "FontPreloader.cpp",
    "GeckoBindings.cpp",
    "GlobalStyleSheetCache.cpp",
    "GroupRule.cpp",
    "ImageLoader.cpp",
    "ImportScanner.cpp",
    "LayerAnimationInfo.cpp",
    "Loader.cpp",
    "MappedDeclarationsBuilder.cpp",
    "MediaList.cpp",
    "MediaQueryList.cpp",
    "nsAnimationManager.cpp",
    "nsComputedDOMStyle.cpp",
    "nsCSSAnonBoxes.cpp",
    "nsCSSProps.cpp",
    "nsCSSPseudoElements.cpp",
    "nsCSSValue.cpp",
    "nsDOMCSSAttrDeclaration.cpp",
    "nsDOMCSSDeclaration.cpp",
    "nsDOMCSSValueList.cpp",
    "nsFontFaceLoader.cpp",
    "nsFontFaceUtils.cpp",
    "nsICSSDeclaration.cpp",
    "nsMediaFeatures.cpp",
    "nsROCSSPrimitiveValue.cpp",
    "nsStyleStruct.cpp",
    "nsStyleTransformMatrix.cpp",
    "nsStyleUtil.cpp",
    "nsTransitionManager.cpp",
    "PaintWorkletGlobalScope.cpp",
    "PaintWorkletImpl.cpp",
    "PostTraversalTask.cpp",
    "PreferenceSheet.cpp",
    "PreloadedStyleSheet.cpp",
    "PseudoStyleType.cpp",
    "Rule.cpp",
    "ServoCSSParser.cpp",
    "ServoCSSRuleList.cpp",
    "ServoElementSnapshot.cpp",
    "ServoStyleSet.cpp",
    "ShadowParts.cpp",
    "SharedStyleSheetCache.cpp",
    "SharedSubResourceCache.cpp",
    "StreamLoader.cpp",
    "StyleAnimationValue.cpp",
    "StyleColor.cpp",
    "StyleSheet.cpp",
    "TimelineCollection.cpp",
    "TimelineManager.cpp",
    "URLExtraData.cpp",
]

include("/ipc/chromium/chromium-config.mozbuild")

FINAL_LIBRARY = "xul"

LOCAL_INCLUDES += [
    "../base",
    "../generic",
    "../xul",
    "/dom/base",
    "/dom/html",
    "/dom/xul",
    "/image",
    # For nsHttpChannel.h
    "/netwerk/base",
    "/netwerk/protocol/http",
]

JAR_MANIFESTS += ["jar.mn"]

RESOURCE_FILES += [
    "contenteditable.css",
    "designmode.css",
]

CONTENT_ACCESSIBLE_FILES += [
    "ImageDocument.css",
    "res/close-12.svg",
    "res/plaintext.css",
    "res/viewsource.css",
    "TopLevelImageDocument.css",
    "TopLevelVideoDocument.css",
]


GeneratedFile(
    "nsCSSPropertyID.h",
    script="GenerateCSSPropertyID.py",
    entry_point="generate",
    inputs=["nsCSSPropertyID.h.in", "!ServoCSSPropList.py"],
)
GeneratedFile(
    "ServoCSSPropList.h",
    script="GenerateServoCSSPropList.py",
    entry_point="generate_header",
    inputs=["!ServoCSSPropList.py"],
)
GeneratedFile(
    "ServoCSSPropList.py",
    script="GenerateServoCSSPropList.py",
    entry_point="generate_data",
    inputs=["ServoCSSPropList.mako.py"],
)

if CONFIG["COMPILE_ENVIRONMENT"]:
    EXPORTS.mozilla += [
        "!CompositorAnimatableProperties.h",
        "!CountedUnknownProperties.h",
        "!ServoStyleConsts.h",
    ]

    GeneratedFile(
        "CompositorAnimatableProperties.h",
        script="GenerateCompositorAnimatableProperties.py",
        entry_point="generate",
        inputs=["!ServoCSSPropList.py"],
    )
    GeneratedFile(
        "CountedUnknownProperties.h",
        script="GenerateCountedUnknownProperties.py",
        entry_point="generate",
        inputs=[
            "/servo/components/style/properties/counted_unknown_properties.py",
        ],
    )
    GeneratedFile(
        "nsComputedDOMStyleGenerated.inc",
        script="GenerateComputedDOMStyleGenerated.py",
        entry_point="generate",
        inputs=["!ServoCSSPropList.py"],
    )
    GeneratedFile(
        "nsCSSPropsGenerated.inc",
        script="GenerateCSSPropsGenerated.py",
        entry_point="generate",
        inputs=["!ServoCSSPropList.py"],
    )
    CbindgenHeader(
        "ServoStyleConsts.h",
        inputs=["/servo/ports/geckolib", "/servo/components/style"],
    )
