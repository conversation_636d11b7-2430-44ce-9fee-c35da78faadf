/**
 * <PERSON><PERSON><PERSON> - <PERSON><PERSON> Tests
 * Playwright tests for the cookie banner blocking functionality
 */

const { test, expect } = require('@playwright/test');
const path = require('path');

// Test configuration
const MEGISTO_BROWSER_PATH = process.env.MEGISTO_BROWSER_PATH || 
  path.join(__dirname, '../../firefox-source/obj-megisto/dist/bin/megisto.exe');

const TEST_SITES = [
  {
    name: 'Generic Cookie Banner',
    url: 'data:text/html,<html><body><div id="cookie-banner" style="position:fixed;top:0;width:100%;background:red;height:100px;z-index:9999;"><PERSON><PERSON><button id="reject-button">Reject</button></div><h1>Test Page</h1></body></html>',
    bannerSelector: '#cookie-banner',
    shouldBeBlocked: true
  },
  {
    name: 'Consent Modal',
    url: 'data:text/html,<html><body><div class="consent-modal" style="position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:white;border:1px solid black;padding:20px;z-index:9999;">We use cookies<button class="reject-btn">Reject All</button></div><h1>Main Content</h1></body></html>',
    bannerSelector: '.consent-modal',
    shouldBeBlocked: true
  },
  {
    name: 'GDPR Banner',
    url: 'data:text/html,<html><body><div class="gdpr-banner" style="position:fixed;bottom:0;width:100%;background:blue;color:white;padding:10px;">GDPR Notice<button id="decline-cookies">Decline</button></div><h1>Website Content</h1></body></html>',
    bannerSelector: '.gdpr-banner',
    shouldBeBlocked: true
  }
];

test.describe('Megisto Cookie Blocker', () => {
  let browser;
  let context;

  test.beforeAll(async ({ playwright }) => {
    // Launch Megisto Browser
    browser = await playwright.firefox.launch({
      executablePath: MEGISTO_BROWSER_PATH,
      headless: false, // Set to true for CI
      args: [
        '--no-first-run',
        '--disable-default-browser-check',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding'
      ]
    });

    context = await browser.newContext({
      viewport: { width: 1280, height: 720 }
    });
  });

  test.afterAll(async () => {
    await context?.close();
    await browser?.close();
  });

  test.beforeEach(async () => {
    // Wait a bit for the extension to load
    await new Promise(resolve => setTimeout(resolve, 1000));
  });

  for (const site of TEST_SITES) {
    test(`should block cookie banner on ${site.name}`, async () => {
      const page = await context.newPage();
      
      try {
        // Navigate to test page
        await page.goto(site.url);
        
        // Wait for page to load
        await page.waitForLoadState('networkidle');
        
        // Wait a bit for the cookie blocker to process
        await page.waitForTimeout(2000);
        
        if (site.shouldBeBlocked) {
          // Check that the banner is not visible or has been removed
          const banner = page.locator(site.bannerSelector);
          
          // The banner should either not exist or not be visible
          const bannerCount = await banner.count();
          if (bannerCount > 0) {
            const isVisible = await banner.isVisible();
            expect(isVisible).toBeFalsy();
          }
          
          console.log(`✓ Cookie banner blocked on ${site.name}`);
        }
        
      } finally {
        await page.close();
      }
    });
  }

  test('should handle dynamic cookie banners', async () => {
    const page = await context.newPage();
    
    try {
      // Create a page that adds a cookie banner dynamically
      await page.goto('data:text/html,<html><body><h1>Test Page</h1><button id="add-banner">Add Cookie Banner</button></body></html>');
      
      // Add script to create dynamic banner
      await page.addScriptTag({
        content: `
          document.getElementById('add-banner').onclick = function() {
            const banner = document.createElement('div');
            banner.id = 'dynamic-cookie-banner';
            banner.style.cssText = 'position:fixed;top:0;width:100%;background:orange;height:80px;z-index:9999;';
            banner.innerHTML = 'Dynamic Cookie Banner <button id="accept-cookies">Accept</button>';
            document.body.appendChild(banner);
          };
        `
      });
      
      // Click button to add banner
      await page.click('#add-banner');
      
      // Wait for the cookie blocker to process the dynamic content
      await page.waitForTimeout(3000);
      
      // Check that the dynamic banner was blocked
      const dynamicBanner = page.locator('#dynamic-cookie-banner');
      const bannerCount = await dynamicBanner.count();
      
      if (bannerCount > 0) {
        const isVisible = await dynamicBanner.isVisible();
        expect(isVisible).toBeFalsy();
      }
      
      console.log('✓ Dynamic cookie banner blocked');
      
    } finally {
      await page.close();
    }
  });

  test('should block popups', async () => {
    const page = await context.newPage();
    
    try {
      // Monitor for popup attempts
      const popupPromise = page.waitForEvent('popup', { timeout: 5000 }).catch(() => null);
      
      // Navigate to page with popup
      await page.goto('data:text/html,<html><body><h1>Test Page</h1><button onclick="window.open(\'about:blank\', \'popup\')">Open Popup</button></body></html>');
      
      // Click popup button
      await page.click('button');
      
      // Wait to see if popup opens
      const popup = await popupPromise;
      
      // Popup should be blocked (null)
      expect(popup).toBeNull();
      
      console.log('✓ Popup blocked');
      
    } finally {
      await page.close();
    }
  });

  test('should preserve page functionality', async () => {
    const page = await context.newPage();
    
    try {
      // Test that normal page functionality still works
      await page.goto('data:text/html,<html><body><h1 id="title">Test Page</h1><button id="test-btn" onclick="document.getElementById(\'title\').textContent=\'Clicked\'">Click Me</button></body></html>');
      
      // Click button
      await page.click('#test-btn');
      
      // Check that the functionality worked
      const titleText = await page.textContent('#title');
      expect(titleText).toBe('Clicked');
      
      console.log('✓ Page functionality preserved');
      
    } finally {
      await page.close();
    }
  });

  test('should load extension properly', async () => {
    const page = await context.newPage();
    
    try {
      // Check that the Megisto extension is loaded
      await page.goto('about:debugging#/runtime/this-firefox');
      
      // Wait for page to load
      await page.waitForLoadState('networkidle');
      
      // Look for Megisto Core extension
      const extensionExists = await page.locator('text=Megisto Core').count() > 0;
      expect(extensionExists).toBeTruthy();
      
      console.log('✓ Megisto Core extension loaded');
      
    } finally {
      await page.close();
    }
  });
});

// Helper function to create test pages
function createTestPage(content) {
  return `data:text/html,<html><head><meta charset="utf-8"></head><body>${content}</body></html>`;
}

// Performance test
test.describe('Cookie Blocker Performance', () => {
  test('should not significantly impact page load time', async ({ browser }) => {
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
      const startTime = Date.now();
      
      await page.goto('data:text/html,<html><body><h1>Performance Test</h1></body></html>');
      await page.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      
      // Load time should be reasonable (less than 5 seconds for simple page)
      expect(loadTime).toBeLessThan(5000);
      
      console.log(`✓ Page load time: ${loadTime}ms`);
      
    } finally {
      await page.close();
      await context.close();
    }
  });
});
