# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "core-foundation"
version = "0.10.0"
authors = ["The Servo Project Developers"]
build = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "Bindings to Core Foundation for macOS"
homepage = "https://github.com/servo/core-foundation-rs"
readme = false
keywords = [
    "macos",
    "framework",
    "objc",
]
categories = ["os::macos-apis"]
license = "MIT OR Apache-2.0"
repository = "https://github.com/servo/core-foundation-rs"

[package.metadata.docs.rs]
all-features = true
default-target = "x86_64-apple-darwin"

[lib]
name = "core_foundation"
path = "src/lib.rs"

[[test]]
name = "use_macro_outside_crate"
path = "tests/use_macro_outside_crate.rs"

[dependencies.core-foundation-sys]
version = "0.8.7"
default-features = false

[dependencies.libc]
version = "0.2"

[dependencies.uuid]
version = "1"
optional = true

[features]
default = ["link"]
link = ["core-foundation-sys/link"]
mac_os_10_7_support = ["core-foundation-sys/mac_os_10_7_support"]
mac_os_10_8_features = ["core-foundation-sys/mac_os_10_8_features"]
with-uuid = ["uuid"]
