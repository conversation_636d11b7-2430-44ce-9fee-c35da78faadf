# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.74"
name = "clap_builder"
version = "4.5.15"
build = false
include = [
    "build.rs",
    "src/**/*",
    "Cargo.toml",
    "LICENSE*",
    "README.md",
    "benches/**/*",
    "examples/**/*",
]
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "A simple to use, efficient, and full-featured Command Line Argument Parser"
readme = "README.md"
keywords = [
    "argument",
    "cli",
    "arg",
    "parser",
    "parse",
]
categories = ["command-line-interface"]
license = "MIT OR Apache-2.0"
repository = "https://github.com/clap-rs/clap"

[package.metadata.docs.rs]
cargo-args = [
    "-Zunstable-options",
    "-Zrustdoc-scrape-examples",
]
features = ["unstable-doc"]
rustdoc-args = [
    "--cfg",
    "docsrs",
]

[package.metadata.playground]
features = ["unstable-doc"]

[package.metadata.release]
dependent-version = "upgrade"
shared-version = true
tag-name = "v{{version}}"

[lib]
name = "clap_builder"
path = "src/lib.rs"
bench = false

[dependencies.anstream]
version = "0.6.7"
optional = true

[dependencies.anstyle]
version = "1.0.8"

[dependencies.backtrace]
version = "0.3.73"
optional = true

[dependencies.clap_lex]
version = "0.7.0"

[dependencies.strsim]
version = "0.11.0"
optional = true

[dependencies.terminal_size]
version = "0.3.0"
optional = true

[dependencies.unicase]
version = "2.6.0"
optional = true

[dependencies.unicode-width]
version = "0.1.9"
optional = true

[dev-dependencies.color-print]
version = "0.3.6"

[dev-dependencies.static_assertions]
version = "1.1.0"

[dev-dependencies.unic-emoji-char]
version = "0.9.0"

[features]
cargo = []
color = ["dep:anstream"]
debug = ["dep:backtrace"]
default = [
    "std",
    "color",
    "help",
    "usage",
    "error-context",
    "suggestions",
]
deprecated = []
env = []
error-context = []
help = []
std = ["anstyle/std"]
string = []
suggestions = [
    "dep:strsim",
    "error-context",
]
unicode = [
    "dep:unicode-width",
    "dep:unicase",
]
unstable-doc = [
    "cargo",
    "wrap_help",
    "env",
    "unicode",
    "string",
    "unstable-ext",
]
unstable-ext = []
unstable-styles = ["color"]
unstable-v5 = ["deprecated"]
usage = []
wrap_help = [
    "help",
    "dep:terminal_size",
]

[lints.clippy]
assigning_clones = "allow"
blocks_in_conditions = "allow"
bool_assert_comparison = "allow"
branches_sharing_code = "allow"
checked_conversions = "warn"
collapsible_else_if = "allow"
create_dir = "warn"
dbg_macro = "warn"
debug_assert_with_mut_call = "warn"
doc_markdown = "warn"
empty_enum = "warn"
enum_glob_use = "warn"
expl_impl_clone_on_copy = "warn"
explicit_deref_methods = "warn"
explicit_into_iter_loop = "warn"
fallible_impl_from = "warn"
filter_map_next = "warn"
flat_map_option = "warn"
float_cmp_const = "warn"
fn_params_excessive_bools = "warn"
from_iter_instead_of_collect = "warn"
if_same_then_else = "allow"
implicit_clone = "warn"
imprecise_flops = "warn"
inconsistent_struct_constructor = "warn"
inefficient_to_string = "warn"
infinite_loop = "warn"
invalid_upcast_comparisons = "warn"
large_digit_groups = "warn"
large_stack_arrays = "warn"
large_types_passed_by_value = "warn"
let_and_return = "allow"
linkedlist = "warn"
lossy_float_literal = "warn"
macro_use_imports = "warn"
mem_forget = "warn"
multiple_bound_locations = "allow"
mutex_integer = "warn"
needless_continue = "warn"
needless_for_each = "warn"
negative_feature_names = "warn"
path_buf_push_overwrite = "warn"
ptr_as_ptr = "warn"
rc_mutex = "warn"
redundant_feature_names = "warn"
ref_option_ref = "warn"
rest_pat_in_fully_bound_structs = "warn"
same_functions_in_if_condition = "warn"
self_named_module_files = "warn"
semicolon_if_nothing_returned = "warn"
string_add_assign = "warn"
string_lit_as_bytes = "warn"
todo = "warn"
trait_duplication_in_bounds = "warn"
verbose_file_reads = "warn"
zero_sized_map_values = "warn"

[lints.rust]
unreachable_pub = "warn"
unsafe_op_in_unsafe_fn = "warn"
unused_lifetimes = "warn"
unused_macro_rules = "warn"
unused_qualifications = "warn"

[lints.rust.rust_2018_idioms]
level = "warn"
priority = -1
