# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.74"
name = "clap_derive"
version = "4.5.13"
build = false
include = [
    "build.rs",
    "src/**/*",
    "Cargo.toml",
    "LICENSE*",
    "README.md",
    "benches/**/*",
    "examples/**/*",
]
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "Parse command line argument by defining a struct, derive crate."
readme = "README.md"
keywords = [
    "clap",
    "cli",
    "parse",
    "derive",
    "proc_macro",
]
categories = [
    "command-line-interface",
    "development-tools::procedural-macro-helpers",
]
license = "MIT OR Apache-2.0"
repository = "https://github.com/clap-rs/clap"

[package.metadata.docs.rs]
targets = ["x86_64-unknown-linux-gnu"]

[package.metadata.release]
dependent-version = "upgrade"
shared-version = true
tag-name = "v{{version}}"

[lib]
name = "clap_derive"
path = "src/lib.rs"
bench = false
proc-macro = true

[dependencies.heck]
version = "0.5.0"

[dependencies.proc-macro2]
version = "1.0.69"

[dependencies.quote]
version = "1.0.9"

[dependencies.syn]
version = "2.0.8"
features = ["full"]

[features]
debug = []
default = []
deprecated = []
raw-deprecated = ["deprecated"]
unstable-v5 = ["deprecated"]

[lints.clippy]
assigning_clones = "allow"
blocks_in_conditions = "allow"
bool_assert_comparison = "allow"
branches_sharing_code = "allow"
checked_conversions = "warn"
collapsible_else_if = "allow"
create_dir = "warn"
dbg_macro = "warn"
debug_assert_with_mut_call = "warn"
doc_markdown = "warn"
empty_enum = "warn"
enum_glob_use = "warn"
expl_impl_clone_on_copy = "warn"
explicit_deref_methods = "warn"
explicit_into_iter_loop = "warn"
fallible_impl_from = "warn"
filter_map_next = "warn"
flat_map_option = "warn"
float_cmp_const = "warn"
fn_params_excessive_bools = "warn"
from_iter_instead_of_collect = "warn"
if_same_then_else = "allow"
implicit_clone = "warn"
imprecise_flops = "warn"
inconsistent_struct_constructor = "warn"
inefficient_to_string = "warn"
infinite_loop = "warn"
invalid_upcast_comparisons = "warn"
large_digit_groups = "warn"
large_stack_arrays = "warn"
large_types_passed_by_value = "warn"
let_and_return = "allow"
linkedlist = "warn"
lossy_float_literal = "warn"
macro_use_imports = "warn"
mem_forget = "warn"
multiple_bound_locations = "allow"
mutex_integer = "warn"
needless_continue = "warn"
needless_for_each = "warn"
negative_feature_names = "warn"
path_buf_push_overwrite = "warn"
ptr_as_ptr = "warn"
rc_mutex = "warn"
redundant_feature_names = "warn"
ref_option_ref = "warn"
rest_pat_in_fully_bound_structs = "warn"
same_functions_in_if_condition = "warn"
self_named_module_files = "warn"
semicolon_if_nothing_returned = "warn"
string_add_assign = "warn"
string_lit_as_bytes = "warn"
todo = "warn"
trait_duplication_in_bounds = "warn"
verbose_file_reads = "warn"
zero_sized_map_values = "warn"

[lints.rust]
unreachable_pub = "warn"
unsafe_op_in_unsafe_fn = "warn"
unused_lifetimes = "warn"
unused_macro_rules = "warn"
unused_qualifications = "warn"

[lints.rust.rust_2018_idioms]
level = "warn"
priority = -1
