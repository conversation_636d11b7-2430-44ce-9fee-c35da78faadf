# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

import re
import runpy
import string


def to_camel_case(ident):
    return re.sub(
        "(^|_|-)([a-z0-9])", lambda m: m.group(2).upper(), ident.strip("_").strip("-")
    )


def generate(output, prop_file):
    properties = runpy.run_path(prop_file)["COUNTED_UNKNOWN_PROPERTIES"]

    output.write("/* THIS IS AN AUTOGENERATED FILE.  DO NOT EDIT */\n\n")

    for prop in properties:
        output.write(
            "COUNTED_UNKNOWN_PROPERTY({}, {})\n".format(prop, to_camel_case(prop))
        )
