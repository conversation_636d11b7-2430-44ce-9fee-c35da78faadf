<!DOCTYPE html>
<!--
     Any copyright is dedicated to the Public Domain.
     http://creativecommons.org/publicdomain/zero/1.0/
-->
<html lang="en-US">
<head>
  <meta charset="UTF-8">
  <title>CSS Test: CSS display:contents</title>
  <link rel="author" title="<PERSON><PERSON>" href="https://bugzilla.mozilla.org/show_bug.cgi?id=907396">
  <link rel="help" href="http://dev.w3.org/csswg/css-display">
<style type="text/css">

        html,body {
            color:black; background-color:white; font-size:16px; padding:0; margin:0;
        }
	
.table   { display:table; border-collapse:collapse; border: blue solid 1pt;}
.itable  { display:inline-table; }
.caption { display:table-caption; }
.cell    { display:table-cell; border:inherit; }
.row     { display:table-row; border: green dashed 1pt; }
.rowg    { display:table-row-group; }
.head    { display:table-header-group; }
.foot    { display:table-footer-group; }
.col     { display:table-column; }
.colg    { display:table-column-group; }
.flex    { display:flex; }
.iflex   { display:inline-flex; }
.li      { display:list-item; }
.ib      { display:inline-block; }
.inline  { display:inline; }
.columns  { columns:2; height:4em; }

.contents { display:contents; align-items:inherit; justify-items:inherit; }

.c1 { color:lime; }
.c2 { background:blue; color:pink; }
.c3 { color:teal; }
.c4 { color:green; }
.c5 { color:silver; }
.c6 { color:cyan; }
.c7 { color:magenta; }
.c8 { color:yellow; }
.c9 { color:grey; }
.c10{ color:black; }

.b { background:inherit; }
    </style>
</head>
<body style="color:red">

<div class="contents c1"><div class="contents c2"><div class="caption">1<span class="b">1</span></div></div></div>
<div class="contents c2"><div class="row">2a<div class="cell">2<div class="contents c2">b<span class="b">b</span></div></div></div></div>
<div class="contents c3"><div class="cell">3</div></div>
<div class="contents c4"><div class="rowg">4</div></div>
<div class="contents c5"><div class="cell">5a</div></div>
<div class="cell c5">5b</div>
<div class="contents c6"><div class="head">6</div></div>
<div class="contents c7"><div class="cell"><div class="contents c2">7<span class="b">a</span></div></div></div>
<div class="contents c8"><div class="cell">7b</div></div>
<div class="contents c9"><div class="foot">8</div></div>
<div class="contents c9"><div class="foot">9<div class="contents c2">a<span class="b">b</span>c</div></div></div>
<div class="contents c10"><div class="cell">10</div></div>

<div class="table" style="float:right">
<div class="contents c1"><div class="contents c2"><div class="caption">1<span class="b">1</span></div></div></div>
<div class="contents c2"><div class="row">2a<div class="cell">2<div class="contents c2">b<span class="b">b</span></div></div></div></div>
<div class="contents c3"><div class="cell">3</div></div>
<div class="contents c4"><div class="rowg">4</div></div>
<div class="contents c5"><div class="cell">5a</div></div>
<div class="cell c5">5b</div>
<div class="contents c6"><div class="head">6</div></div>
<div class="contents c7"><div class="cell"><div class="contents c2">7<span class="b">a</span></div></div></div>
<div class="contents c8"><div class="cell">7b</div></div>
<div class="contents c9"><div class="foot">8</div></div>
<div class="contents c9"><div class="foot">9<div class="contents c2">a<span class="b">b</span>c</div></div></div>
<div class="contents c10"><div class="cell">10</div></div>
</div>

<div class="flex c1">
0
<div class="contents c1">x</div>
<div class="contents c1"><div class="contents c2">y</div></div>
<div class="contents c1"><div class="contents c2"><div class="">1<span class="b">1</span></div></div></div>
<div class="contents c2"><div class="inline">2a<div class="">2<div class="contents c2">b<span class="b">b</span></div></div></div></div>
<div class="contents c3"><div class="inline">3</div></div>
<div class="inline"><div class="contents c4">4</div></div>
<div class=""><div class="contents c5">5a</div></div>
<div class=" c5">5b</div>
<div class="contents c6"><div class="">6</div></div>
<div class="ib"><div class="contents c7"><div class="contents c2">7<span class="b">a</span></div></div></div>
<div class="contents c9"><div class="">8</div></div>
<div class="contents c9"><div class="contents">9<div class="contents c2">a<span class="b">b</span>c</div></div></div>
<div class="contents c10"><div class="">10</div></div>
</div>

<div class="flex"><div class="contents c2">
0
<div class="contents c1">x</div>
<div class="contents c1"><div class="contents c2">y</div></div>
<div class="contents c1"><div class="contents c2"><div class="">1<span class="b">1</span></div></div></div>
<div class="contents c2"><div class="inline">2a<div class="">2<div class="contents c2">b<span class="b">b</span></div></div></div></div>
<div class="contents c3"><div class="inline">3</div></div>
<div class=""><div class="contents c4">4</div></div>
<div class=""><div class="contents c5">5a</div></div>
<div class=" c5">5b</div>
<div class="contents c6"><div class="">6</div></div>
<div class="ib"><div class="contents c7"><div class="contents c2">7<span class="b">a</span></div></div></div>
<div class="contents c9"><div class="">8</div></div>
<div class="contents c9"><div class="contents">9<div class="contents c2">a<span class="b">b</span>c</div></div></div>
<div class="contents c10"><div class="">10</div></div>
</div></div>

<div class="iflex"><div class="contents c2">
0
</div></div>
<div class="iflex"><div class="contents c2">
0
<div class="contents c1">1</div>
2
</div></div>
<div class="iflex"><div class="contents c2">
0
<div class="c1">1</div>
2
</div></div>
<div class="iflex c3">
0
<div class="contents c2"><div class="c1">1</div></div>
2
</div>
<div class="iflex c3">
<div class="contents c2">0</div>
<div class="contents c2"><div class="c1">1</div></div>
<div class="contents c2">2</div>
</div>
<div class="iflex c3">
<div class="inline">0</div>
<div class="contents"><div class="inline c1">1</div></div>
<div class="inline">2</div>
</div>

<ul><li class="c1"><div class="contents c2">
0
<div class="contents c1">x</div>
<div class="contents c1"><div class="contents c2">y</div></div>
<div class="contents c1"><div class="contents c2"><div class="">1<span class="b">1</span></div></div></div>
<div class="contents c2"><div class="inline">2a<div class="">2<div class="contents c2">b<span class="b">b</span></div></div></div></div>
<div class="contents c3"><div class="inline">3</div></div>
<div class="inline"><div class="contents c4">4</div></div>
<div class=""><div class="contents c5">5a</div></div>
<div class=" c5">5b</div>
<div class="contents c6"><div class="">6</div></div>
<div class="ib"><div class="contents c7"><div class="contents c2">7<span class="b">a</span></div></div></div>
<div class="contents c9"><div class="">8</div></div>
<div class="contents c9"><div class="contents">9<div class="contents c2">a<span class="b">b</span>c</div></div></div>
<div class="contents c10"><div class="">10</div></div>
</div></li>
</ul>

<div class="columns">
<div class="contents c1"><div class="contents c2"><div>1<span class="b">1</span></div></div></div>
<div class="contents c2"><div>2</div></div>
<div class="contents c3"><div>3</div></div>
</div>

<div class="columns">
<div class="columns contents">
<div class="contents c1"><div class="contents c2"><div>1<span class="b">1</span></div></div></div>
<div class="contents c2"><div>2</div></div>
<div class="contents c3"><div>3</div></div>
</div>
</div>

<div class="contents c3"><!-- comment node --></div>
<div class="contents c3"><?PI ?></div>

<span class="c2"><legend class="contents c1">Legend</legend><legend class="contents c1">Legend</legend></span>
<br clear="all">
<span class="c3">x<div class="contents c1" style="float:left">float:left</div></span>
<span class="c3">y<div class="contents c1" style="position:absolute">position:absolute</div></span>

<fieldset class="contents c1"><legend class="contents">Legend</legend>fieldset</fieldset>
<button class="contents c1" style="font: inherit;">but<span>ton</span></button>

<!-- Stuff below should simply behave as having display: none -->

<select class="contents c1"><option>select</select>

</body>
</html>
