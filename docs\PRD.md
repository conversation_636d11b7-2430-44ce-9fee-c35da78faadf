# Megisto Browser – Product Requirements Document (PRD)

## 1. Vision
Megisto Browser is a **Firefox fork** with two main goals:
1. **Clean browsing** – automatically block cookie banners, popups, and intrusive overlays.  
2. **Enhanced media experience** – advanced native control of embedded YouTube videos.  

It is based on **Mozilla’s Gecko engine**, keeping full WebExtension compatibility, but with extra **system-level features** built in.  

---

## 2. Goals
- **Native anti-cookie and anti-popup system**: built-in, invisible to the user, always enabled.  
- **Advanced YouTube embed manager**: replace or extend embedded YouTube players with a wrapper (Video.js + YouTube plugin).  
- **Cross-platform build**: Windows primary development, later Linux/macOS.  

---

## 3. Target Audience
- Privacy-focused users who want browsing without consent banners.  
- Power users who want **enhanced control of embedded media**.  
- Developers/community looking for an open-source browser fork with opinionated defaults.  

---

## 4. Technical Requirements

### 4.1 Development Environment (Windows)
- **OS**: Windows 10/11 (64-bit).  
- **Tools**:
  - [Visual Studio Build Tools](https://visualstudio.microsoft.com/downloads/) (C++ build, MSVC toolchain).  
  - [MozillaBuild](https://wiki.mozilla.org/MozillaBuild) (bash-like shell + dependencies).  
  - [Rust (stable)](https://www.rust-lang.org/tools/install).  
  - Python 3.10+.  
  - Node.js + npm.  
  - Git (for repo management).  
  - Mercurial (`hg`) or GitHub Gecko mirror.  

### 4.2 Source Code
- Firefox/Gecko:  
  - [GitHub mirror](https://github.com/mozilla/gecko-dev)  
  - [Mozilla Mercurial](https://hg.mozilla.org/mozilla-unified)  

### 4.3 Dependencies & Frameworks
- **Anti-cookie / popup**:  
  - [Consent-O-Matic](https://github.com/cavi-au/Consent-O-Matic) (MIT – rule ideas).  
  - [uBlock Origin Popup Blocker wiki](https://github.com/gorhill/uBlock/wiki/Popup-Blocker) (GPL – reference only).  
- **Video embedding**:  
  - [Video.js](https://videojs.com/) (Apache-2.0).  
  - [videojs-youtube](https://github.com/videojs/videojs-youtube) (MIT).  
  - [YouTube IFrame API](https://developers.google.com/youtube/iframe_api_reference).  
- **Testing**:  
  - [Playwright](https://playwright.dev/) (cross-browser tests).  

---

## 5. Features

### 5.1 Anti-Cookie & Popup Manager
- Auto-dismiss consent banners (click “Reject All” / remove modal overlays).  
- Block aggressive popups (`window.open`, hijacked `target=_blank`).  
- Rule system (JSON updatable from remote endpoint).  
- Example rule format:
  ```json
  {
    "domain": "example.com",
    "selectors": ["#cookie-banner", ".cmp-modal"],
    "actions": ["click:#reject-button", "remove:.overlay"]
  }
5.2 YouTube Embed Manager

Detect <iframe src="youtube.com/embed/...">.

Replace with Video.js player + YouTube tech.

Options:

megisto.youtube.autoplay=false

megisto.youtube.defaultQuality=720p

megisto.youtube.blockRelated=true

5.3 System Add-on (Core Integration)

Invisible, non-removable add-on shipped with the browser.

Contains background script + content scripts.

Updatable independently of the browser binary.

Example manifest:

{
  "manifest_version": 2,
  "name": "Megisto Core",
  "version": "1.0",
  "applications": { "gecko": { "id": "megisto@browser" } },
  "background": { "scripts": ["background.js"] },
  "content_scripts": [
    {
      "matches": ["<all_urls>"],
      "js": ["content/cookieblocker.js", "content/videomanager.js"],
      "run_at": "document_start"
    }
  ]
}

6. Architecture
flowchart TD
    A[Gecko Engine] --> B[Megisto System Add-on]
    B --> C[Cookie/Popup Blocker]
    B --> D[YouTube Manager]
    C --> E[Rules JSON Service]
    D --> F[Video.js + YouTube API]

7. Roadmap

Phase 1 – Setup (2 weeks)

Fork Firefox, build on Windows.

Create skeleton system add-on.

Phase 2 – Cookie/Popup Manager (3–6 weeks)

Rule system.

Initial banner blocking.

Phase 3 – YouTube Manager (3–5 weeks)

Integrate Video.js + YouTube tech.

Implement user preferences.

Phase 4 – QA + Alpha Release (3 weeks)

Automated Playwright tests.

Prebuilt installer for Windows.

8. Licensing

Firefox (Gecko): MPL 2.0

Video.js: Apache-2.0

videojs-youtube: MIT

Consent-O-Matic: MIT

uBlock Origin: GPL (reference only, no code reuse).

9. References

Firefox Source Docs

System Add-ons

Video.js Docs

YouTube IFrame API

Repo Structure:

megisto-browser/
│
├─ browser/ # Firefox browser source (Gecko fork here)
│ └─ extensions/
│ └─ megisto-addon/ # System add-on folder
│ ├─ manifest.json
│ ├─ background.js
│ ├─ content/
│ │ ├─ cookieblocker.js
│ │ └─ videomanager.js
│ └─ rules/
│ └─ default-rules.json
│
├─ docs/
│ └─ PRD.md # This Product Requirements Document
│
├─ tools/
│ └─ build.ps1 # PowerShell build helper script (calls mach build)
│
└─ tests/
└─ playwright/ # Playwright tests
├─ test-cookieblocker.spec.js
└─ test-videomanager.spec.js