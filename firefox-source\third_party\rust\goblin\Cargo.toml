# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.63.0"
name = "goblin"
version = "0.9.2"
authors = [
    "m4b <<EMAIL>>",
    "seu <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
    "Lzu Tao <<EMAIL>>",
]
build = false
include = [
    "src",
    "CHANGELOG.md",
    "Cargo.toml",
    "LICENSE",
    "README.md",
]
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "An impish, cross-platform, ELF, Mach-o, and PE binary parsing and loading crate"
documentation = "https://docs.rs/goblin"
readme = "README.md"
keywords = [
    "binary",
    "elf",
    "mach",
    "pe",
    "archive",
]
categories = [
    "parsing",
    "development-tools::debugging",
]
license = "MIT"
repository = "https://github.com/m4b/goblin"

[lib]
name = "goblin"
path = "src/lib.rs"

[dependencies.log]
version = "0.4"
optional = true
default-features = false

[dependencies.plain]
version = "0.2.3"

[dependencies.scroll]
version = "0.12"
default-features = false

[dev-dependencies.stderrlog]
version = "0.5.4"

[features]
alloc = [
    "scroll/derive",
    "log",
]
archive = ["alloc"]
default = [
    "std",
    "elf32",
    "elf64",
    "mach32",
    "mach64",
    "pe32",
    "pe64",
    "te",
    "archive",
    "endian_fd",
]
elf32 = []
elf64 = []
endian_fd = ["alloc"]
mach32 = [
    "alloc",
    "endian_fd",
    "archive",
]
mach64 = [
    "alloc",
    "endian_fd",
    "archive",
]
pe32 = [
    "alloc",
    "endian_fd",
]
pe64 = [
    "alloc",
    "endian_fd",
]
std = [
    "alloc",
    "scroll/std",
]
te = [
    "alloc",
    "endian_fd",
]

[badges.travis-ci]
branch = "master"
repository = "m4b/goblin"
