# Megisto Browser - Development Setup Guide

This guide will help you set up the development environment for building Megisto Browser on Windows.

## Prerequisites

### System Requirements
- **Operating System**: Windows 10/11 (64-bit)
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 50GB free space minimum
- **Internet**: Stable connection for downloading dependencies

### Required Software
The setup script will install most of these automatically, but you can install them manually if needed:

1. **Visual Studio Build Tools** (C++ build tools, MSVC toolchain)
2. **MozillaBuild** (Mozilla's build environment)
3. **Rust** (stable channel)
4. **Python** 3.10+
5. **Node.js** + npm
6. **Git**
7. **Mercurial** (hg)

## Quick Start

### 1. Clone the Repository
```powershell
git clone <repository-url> megisto-browser
cd megisto-browser
```

### 2. Run Automated Setup
```powershell
# Run as Administrator
.\tools\setup-dev-env.ps1
```

This script will:
- Install required development tools
- Set up environment variables
- Create necessary directories
- Verify installations

### 3. Clone Firefox Source
```powershell
.\tools\clone-firefox.ps1
```

Options:
- `-Source github` (default) or `-Source mercurial`
- `-Branch central` (default) or other Firefox branches
- `-Shallow` for faster clone (shallow history)

### 4. Build Megisto Browser
```powershell
.\tools\build.ps1
```

Options:
- `-Clean` to remove previous build
- `-Package` to create distribution package
- `-Debug` for debug build
- `-Verbose` for detailed output

## Manual Setup (Alternative)

If you prefer to set up manually or the automated script fails:

### 1. Install Visual Studio Build Tools
Download and install from: https://visualstudio.microsoft.com/downloads/
- Select "C++ build tools" workload
- Include MSVC v143 compiler toolset
- Include Windows 10/11 SDK

### 2. Install MozillaBuild
Download from: https://ftp.mozilla.org/pub/mozilla/libraries/win32/MozillaBuildSetup-Latest.exe
- Install to default location: `C:\mozilla-build`

### 3. Install Other Tools
Using Chocolatey (recommended):
```powershell
# Install Chocolatey first
Set-ExecutionPolicy Bypass -Scope Process -Force
iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# Install tools
choco install git python nodejs rust hg -y
```

### 4. Set Environment Variables
```powershell
$env:MOZCONFIG = "$(Get-Location)\mozconfig"
$env:MOZ_OBJDIR = "$(Get-Location)\firefox-source\obj-megisto"
```

## Building Process

### 1. Configure Build
The `mozconfig` file contains build configuration. Key settings:
- Application: browser (Firefox)
- Branding: Megisto
- Optimizations: enabled for release builds
- Telemetry: disabled
- System add-on: included

### 2. Bootstrap (First Build Only)
```bash
# In MozillaBuild shell
cd firefox-source
./mach bootstrap --application-choice browser --no-interactive
```

### 3. Build
```bash
# In MozillaBuild shell
./mach build
```

### 4. Package (Optional)
```bash
# In MozillaBuild shell
./mach package
```

## Testing

### 1. Install Test Dependencies
```powershell
cd tests
npm install
npx playwright install firefox
```

### 2. Run Tests
```powershell
# Run all tests
.\tools\run-tests.ps1

# Run specific test suite
.\tools\run-tests.ps1 -TestType cookieblocker
.\tools\run-tests.ps1 -TestType videomanager

# Run in headed mode (visible browser)
.\tools\run-tests.ps1 -Headed

# Debug mode
.\tools\run-tests.ps1 -Debug
```

## Creating Installer

### 1. Install NSIS
```powershell
choco install nsis -y
```

### 2. Create Installer
```powershell
.\tools\create-installer.ps1 -Version "1.0.0"
```

Options:
- `-Version` specify version number
- `-Architecture x64` (default) or `x86`
- `-Sign` to sign the installer (requires certificate)

## Troubleshooting

### Common Issues

#### Build Fails with "MSVC not found"
- Ensure Visual Studio Build Tools are installed
- Check that MSVC v143 toolset is included
- Restart command prompt after installation

#### MozillaBuild Shell Issues
- Ensure MozillaBuild is installed to `C:\mozilla-build`
- Use the provided batch files to start the shell
- Check PATH environment variable

#### Out of Disk Space
- Firefox builds require significant space (20-30GB)
- Clean previous builds with `.\tools\build.ps1 -Clean`
- Use shallow clone: `.\tools\clone-firefox.ps1 -Shallow`

#### Rust Compilation Errors
- Update Rust: `rustup update`
- Clear Rust cache: `cargo clean`
- Reinstall Rust if necessary

#### Python Version Issues
- Ensure Python 3.10+ is installed
- Check `python --version`
- Update PATH if necessary

### Getting Help

1. Check the build logs in `logs/` directory
2. Review Firefox build documentation
3. Check Mozilla's build troubleshooting guides
4. File an issue with detailed error information

## Development Workflow

### 1. Making Changes
- Modify system add-on files in `browser/extensions/megisto-addon/`
- Update branding in `firefox-source/browser/branding/megisto/`
- Modify build configuration in `mozconfig`

### 2. Testing Changes
```powershell
# Rebuild
.\tools\build.ps1

# Test
.\tools\run-tests.ps1

# Manual testing
firefox-source\obj-megisto\dist\bin\megisto.exe
```

### 3. Packaging
```powershell
# Create package
.\tools\build.ps1 -Package

# Create installer
.\tools\create-installer.ps1
```

## Directory Structure

```
megisto-browser/
├── browser/extensions/megisto-addon/    # System add-on source
├── docs/                               # Documentation
├── firefox-source/                     # Firefox source code
├── tools/                             # Build and development scripts
├── tests/                             # Test suites
├── dist/                              # Distribution packages
├── logs/                              # Build logs
├── mozconfig                          # Build configuration
└── README.md                          # Project overview
```

## Next Steps

After successful setup:
1. Familiarize yourself with the codebase structure
2. Review the system add-on implementation
3. Run the test suite to ensure everything works
4. Start developing new features or fixing issues
5. Create pull requests for contributions
