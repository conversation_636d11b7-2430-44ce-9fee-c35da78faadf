/* -*- Mode: C++; tab-width: 2; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

/*
 * This CSS stylesheet defines the rules to be applied to any ImageDocuments,
 * including those in frames.
*/

body {
  /* To give the image access to our document's full viewport, we need to
     override the margin that the html.css UA stylesheet would otherwise apply
     to our body. */
  margin: 0;
}

@media not print {
  .fullZoomOut {
    cursor: zoom-out;
  }

  .fullZoomIn {
    cursor: zoom-in;
  }

  .shrinkToFit {
    cursor: zoom-in;
  }

  .overflowingVertical,
  .overflowingHorizontalOnly {
    cursor: zoom-out;
  }
}

.isInObjectOrEmbed {
  width: 100%;
  height: 100vh;
}

img {
  display: block;
}
