# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.63"
name = "socket2"
version = "0.5.7"
authors = [
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
]
include = [
    "Cargo.toml",
    "LICENSE-APACHE",
    "LICENSE-MIT",
    "README.md",
    "src/**/*.rs",
]
description = """
Utilities for handling networking sockets with a maximal amount of configuration
possible intended.
"""
homepage = "https://github.com/rust-lang/socket2"
documentation = "https://docs.rs/socket2"
readme = "README.md"
keywords = [
    "io",
    "socket",
    "network",
]
categories = [
    "api-bindings",
    "network-programming",
]
license = "MIT OR Apache-2.0"
repository = "https://github.com/rust-lang/socket2"

[package.metadata.docs.rs]
all-features = true
rustdoc-args = [
    "--cfg",
    "docsrs",
]
targets = [
    "aarch64-apple-ios",
    "aarch64-linux-android",
    "x86_64-apple-darwin",
    "x86_64-unknown-fuchsia",
    "x86_64-pc-windows-msvc",
    "x86_64-pc-solaris",
    "x86_64-unknown-freebsd",
    "x86_64-unknown-illumos",
    "x86_64-unknown-linux-gnu",
    "x86_64-unknown-linux-musl",
    "x86_64-unknown-netbsd",
    "x86_64-unknown-redox",
    "armv7-linux-androideabi",
    "i686-linux-android",
]

[package.metadata.playground]
features = ["all"]

[features]
all = []

[target."cfg(unix)".dependencies.libc]
version = "0.2.150"

[target."cfg(windows)".dependencies.windows-sys]
version = "0.52"
features = [
    "Win32_Foundation",
    "Win32_Networking_WinSock",
    "Win32_System_IO",
    "Win32_System_Threading",
    "Win32_System_WindowsProgramming",
]
