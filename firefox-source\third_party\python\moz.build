# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

# Default extra components to build config
with Files('**'):
    BUG_COMPONENT = ('Firefox Build System', 'General')

with Files('attrs/**'):
    BUG_COMPONENT = ('Firefox Build System', 'Task Configuration')

with Files('compare_locales/**'):
    BUG_COMPONENT = ('Localization Infrastructure and Tools', 'compare-locales')

with Files('fluent.migrate/**'):
    BUG_COMPONENT = ('Localization Infrastructure and Tools', 'Fluent Migration')

# Actually, https://github.com/projectfluent/python-fluent/issues
with Files('fluent.syntax/**'):
    BUG_COMPONENT = ('Localization Infrastructure and Tools', 'General')

with Files('jsmin/**'):
    BUG_COMPONENT = ('GeckoView', 'General')

with Files('mohawk/**'):
    BUG_COMPONENT = ('Taskcluster', 'Platform Libraries')

with Files('mozilla_version/**'):
    BUG_COMPONENT = ('Release Engineering', 'General')

with Files('pyasn1/**'):
    BUG_COMPONENT = ('Release Engineering', 'General')

with Files('pyasn1_modules/**'):
    BUG_COMPONENT = ('Core', 'Security: PSM')

with Files('pylru/**'):
    BUG_COMPONENT = ('mozilla.org', 'MozillaBuild')

with Files('pytest/**'):
    BUG_COMPONENT = ('Testing', 'General')

with Files('pyyaml/**'):
    BUG_COMPONENT = ('Taskcluster', 'General')

with Files('rsa/**'):
    BUG_COMPONENT = ('Core', 'Security: PSM')

with Files('slugid/**'):
    BUG_COMPONENT = ('Taskcluster', 'Platform Libraries')

with Files('taskcluster/**'):
    BUG_COMPONENT = ('Taskcluster', 'Platform Libraries')

with Files('voluptuous/**'):
    BUG_COMPONENT = ('Firefox Build System', 'Task Configuration')
