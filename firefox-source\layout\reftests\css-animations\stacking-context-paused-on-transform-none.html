<!DOCTYPE html>
<title>
Transform animation creates a stacking context even though it's paused on
a 'transform:none' keyframe
</title>
<style>
span {
  height: 100px;
  width: 100px;
  position: fixed;
  background: green;
  top: 50px;
}
@keyframes TransformNone {
  from, to { transform: none }
}
#test {
  width: 100px; height: 100px;
  background: blue;
  animation: TransformNone 100s paused;
}
</style>
<span></span>
<div id="test"></div>
