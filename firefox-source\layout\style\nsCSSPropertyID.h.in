/* -*- Mode: C++; tab-width: 8; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* vim: set ts=8 sts=2 et sw=2 tw=80: */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

/* enum types for CSS properties and their values */

#ifndef nsCSSPropertyID_h___
#define nsCSSPropertyID_h___

#include <nsHashKeys.h>

/*
   Declare the enum list using the magic of preprocessing
   enum values are "eCSSProperty_foo" (where foo is the property)

   To change the list of properties, see ServoCSSPropList.h

 */
enum nsCSSPropertyID : int32_t {
  eCSSProperty_UNKNOWN = -1,

$property_ids

  // Some of the values below could probably overlap with each other
  // if we had a need for them to do so.

  // Extra value to represent custom properties (--*).
  eCSSPropertyExtra_variable,
};

// MOZ_DBG support is defined in nsCSSProps.h since it depends on
// nsCSSProps::GetStringValue

const nsCSSPropertyID
  eCSSProperty_COUNT_no_shorthands = $longhand_count;
const nsCSSPropertyID
  eCSSProperty_COUNT = $shorthand_count;
const nsCSSPropertyID
  eCSSProperty_COUNT_with_aliases = eCSSPropertyExtra_variable;

namespace mozilla {

template<>
inline PLDHashNumber
Hash<nsCSSPropertyID>(const nsCSSPropertyID& aValue)
{
  return uint32_t(aValue);
}

} // namespace mozilla

// The "descriptors" that can appear in a @font-face rule.
// They have the syntax of properties but different value rules.
enum nsCSSFontDesc {
  eCSSFontDesc_UNKNOWN = -1,
#define CSS_FONT_DESC(name_, method_) eCSSFontDesc_##method_,
#include "nsCSSFontDescList.h"
#undef CSS_FONT_DESC
  eCSSFontDesc_COUNT
};

// The "descriptors" that can appear in a @counter-style rule.
// They have the syntax of properties but different value rules.
enum nsCSSCounterDesc {
  eCSSCounterDesc_UNKNOWN = -1,
#define CSS_COUNTER_DESC(name_, method_) eCSSCounterDesc_##method_,
#include "nsCSSCounterDescList.h"
#undef CSS_COUNTER_DESC
  eCSSCounterDesc_COUNT
};

namespace mozilla {

// FIXME: The underlying type of this enum should be uint8_t, but we can't do
// that because of https://bugs.llvm.org/show_bug.cgi?id=44228.
enum class CountedUnknownProperty : uint32_t {
#define COUNTED_UNKNOWN_PROPERTY(name_, method_) method_,
#include "mozilla/CountedUnknownProperties.h"
#undef COUNTED_UNKNOWN_PROPERTY
  Count,
};

} // namespace mozilla

#endif /* nsCSSPropertyID_h___ */
