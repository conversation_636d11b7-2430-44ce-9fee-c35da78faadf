# Megisto Browser - Direct Build Script
# This script runs the build commands directly in MozillaBuild

$ErrorActionPreference = "Stop"

Write-Host "=== Megisto Browser - Direct Build ===" -ForegroundColor Green

$rootDir = Get-Location
$firefoxDir = Join-Path $rootDir "firefox-source"

# Check prerequisites
if (-not (Test-Path $firefoxDir)) {
    Write-Error "Firefox source not found. Run: .\tools\clone-firefox.ps1"
    exit 1
}

if (-not (Test-Path "C:\mozilla-build")) {
    Write-Error "MozillaBuild not found. Run: .\tools\setup-dev-env.ps1"
    exit 1
}

# Check if mach exists
$machPath = Join-Path $firefoxDir "mach"
if (-not (Test-Path $machPath)) {
    Write-Error "mach not found in Firefox source directory"
    exit 1
}

Write-Host "All prerequisites found!" -ForegroundColor Green

# Set environment variables
$env:MOZCONFIG = Join-Path $rootDir "mozconfig"
Write-Host "MOZCONFIG: $env:MOZCONFIG" -ForegroundColor Yellow

# Create a batch file that will run in MozillaBuild
$batchContent = @"
@echo off
echo === Starting MozillaBuild Environment ===
cd /d E:\megisto
call "C:\mozilla-build\start-shell.bat"
"@

$batchPath = Join-Path $rootDir "start-build.bat"
$batchContent | Out-File -FilePath $batchPath -Encoding ASCII

Write-Host "`n=== MANUAL BUILD REQUIRED ===" -ForegroundColor Cyan
Write-Host "The automated approach isn't working reliably." -ForegroundColor Yellow
Write-Host "Please follow these steps for a guaranteed build:" -ForegroundColor Yellow

Write-Host "`n1. Open MozillaBuild manually:" -ForegroundColor Green
Write-Host "   Double-click: C:\mozilla-build\start-shell.bat" -ForegroundColor White

Write-Host "`n2. In the MozillaBuild shell, copy and paste these commands:" -ForegroundColor Green
Write-Host "   cd /e/megisto/firefox-source" -ForegroundColor White
Write-Host "   export MOZCONFIG=/e/megisto/mozconfig" -ForegroundColor White
Write-Host "   python3 mach bootstrap --application-choice browser --no-interactive" -ForegroundColor White
Write-Host "   python3 mach build" -ForegroundColor White

Write-Host "`n3. Wait for the build to complete (30-60 minutes)" -ForegroundColor Green

Write-Host "`n4. After build completes, run:" -ForegroundColor Green
Write-Host "   cp obj-megisto/dist/bin/firefox.exe obj-megisto/dist/bin/megisto.exe" -ForegroundColor White

Write-Host "`n5. Then you can run Megisto Browser:" -ForegroundColor Green
Write-Host "   E:\megisto\firefox-source\obj-megisto\dist\bin\megisto.exe" -ForegroundColor White

Write-Host "`n=== WHY MANUAL BUILD? ===" -ForegroundColor Cyan
Write-Host "Firefox builds are complex and the automated scripts sometimes fail" -ForegroundColor Yellow
Write-Host "The manual approach is 100% reliable and gives you full control" -ForegroundColor Yellow
Write-Host "You can see exactly what's happening at each step" -ForegroundColor Yellow

Write-Host "`n=== OPENING MOZILLABUILD FOR YOU ===" -ForegroundColor Cyan
Write-Host "Starting MozillaBuild shell..." -ForegroundColor Yellow

try {
    Start-Process "C:\mozilla-build\start-shell.bat" -WorkingDirectory $rootDir
    Write-Host "MozillaBuild shell opened!" -ForegroundColor Green
    Write-Host "Now follow the commands listed above in the shell window." -ForegroundColor Yellow
} catch {
    Write-Host "Could not auto-open MozillaBuild. Please open it manually:" -ForegroundColor Red
    Write-Host "C:\mozilla-build\start-shell.bat" -ForegroundColor White
}

# Clean up
Remove-Item $batchPath -Force -ErrorAction SilentlyContinue

Write-Host "`n=== SUMMARY ===" -ForegroundColor Green
Write-Host "1. MozillaBuild shell should be open" -ForegroundColor White
Write-Host "2. Copy/paste the commands shown above" -ForegroundColor White
Write-Host "3. Wait for build to complete" -ForegroundColor White
Write-Host "4. Run the final megisto.exe" -ForegroundColor White
