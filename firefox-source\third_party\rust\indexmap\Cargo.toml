# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.63"
name = "indexmap"
version = "2.8.0"
build = false
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "A hash table with consistent order and fast iteration."
documentation = "https://docs.rs/indexmap/"
readme = "README.md"
keywords = [
    "hashmap",
    "no_std",
]
categories = [
    "data-structures",
    "no-std",
]
license = "Apache-2.0 OR MIT"
repository = "https://github.com/indexmap-rs/indexmap"

[package.metadata.docs.rs]
features = [
    "arbitrary",
    "quickcheck",
    "serde",
    "borsh",
    "rayon",
]
rustdoc-args = [
    "--cfg",
    "docsrs",
]

[package.metadata.release]
allow-branch = ["main"]
sign-tag = true
tag-name = "{{version}}"

[features]
default = ["std"]
std = []
test_debug = []

[lib]
name = "indexmap"
path = "src/lib.rs"
bench = false

[[test]]
name = "equivalent_trait"
path = "tests/equivalent_trait.rs"

[[test]]
name = "macros_full_path"
path = "tests/macros_full_path.rs"

[[test]]
name = "quick"
path = "tests/quick.rs"

[[test]]
name = "tests"
path = "tests/tests.rs"

[[bench]]
name = "bench"
path = "benches/bench.rs"

[[bench]]
name = "faststring"
path = "benches/faststring.rs"

[dependencies.arbitrary]
version = "1.0"
optional = true
default-features = false

[dependencies.borsh]
version = "1.2"
optional = true
default-features = false

[dependencies.equivalent]
version = "1.0"
default-features = false

[dependencies.hashbrown]
version = "0.15.0"
default-features = false

[dependencies.quickcheck]
version = "1.0"
optional = true
default-features = false

[dependencies.rayon]
version = "1.9"
optional = true

[dependencies.serde]
version = "1.0"
optional = true
default-features = false

[dev-dependencies.fnv]
version = "1.0"

[dev-dependencies.itertools]
version = "0.14"

[dev-dependencies.lazy_static]
version = "1.3"

[dev-dependencies.quickcheck]
version = "1.0"
default-features = false

[dev-dependencies.rand]
version = "0.9"
features = ["small_rng"]

[dev-dependencies.serde_derive]
version = "1.0"

[lints.clippy]
style = "allow"

[profile.bench]
debug = 2
