<!DOCTYPE html>
<html>
<head>
<style>

@keyframes togreen {
    100% {
        color: green;
    }
}

.a:after {
    animation-name: togreen;
    animation-duration: 10s;
}

</style>
<script>

function boom()
{
  document.documentElement.setAttribute("class", "a");
  document.documentElement.offsetHeight;
  document.documentElement.appendChild(document.createElement("span"));
  document.documentElement.removeAttribute("class");
}

</script>
</head>
<body onload="boom();"></body>
</html>
