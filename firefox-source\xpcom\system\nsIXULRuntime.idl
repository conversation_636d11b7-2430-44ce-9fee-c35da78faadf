/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#include "nsISupports.idl"

%{C++

namespace mozilla {
// Simple C++ getter for nsIXULRuntime::browserTabsRemoteAutostart
// This getter is a temporary function that checks for special
// conditions in which e10s support is not great yet, and should
// therefore be disabled. Bug 1065561 tracks its removal.
bool BrowserTabsRemoteAutostart();
uint32_t GetMaxWebProcessCount();

// Returns the value of the fission.autostart pref. Since fission can be
// disabled on a per-window basis, this should only be used when you need the
// global value of the pref. For other use cases, you should use
// nsILoadContext::UseRemoteSubframes instead. This will also check for special
// conditions, like safe mode, which may require fission to be disabled, or
// environment variables MOZ_FORCE_ENABLE_FISSION and MOZ_FORCE_DISABLE_FISSION,
// used by mach run to enable/disable fission regardless of pref settings.
bool FissionAutostart();

// Returns whether or not we are currently enrolled in the fission experiment.
bool FissionExperimentEnrolled();

// Returns true if FissionAutostart() is true or
// fission.disableSessionHistoryInParent is false.
bool SessionHistoryInParent();

// Returns true if SessionHistoryInParent() is true and
// browser.sessionstore.disable_platform_collection is false.
bool SessionStorePlatformCollection();

// Returns true if SessionHistoryInParent() returns true and
// fission.bfcacheInParent is true.
bool BFCacheInParent();
}

%}

/**
 * Provides information about the XUL runtime.
 * @status UNSTABLE - This interface is not frozen and will probably change in
 *                    future releases. If you need this functionality to be
 *                    stable/frozen, please contact Benjamin Smedberg.
 */

[scriptable, uuid(03602fac-fa3f-4a50-9baa-b88456fb4a0f)]
interface nsIXULRuntime : nsISupports
{
  /**
   * Whether the application was launched in safe mode.
   */
  readonly attribute boolean inSafeMode;

  /**
   * The status of a given normandy experiment.
   */
  cenum ExperimentStatus : 8 {
    // The user is not actively enrolled in the experiment.
    eExperimentStatusUnenrolled = 0,
    // The user is enrolled in the control group, and should see the default
    // behavior.
    eExperimentStatusControl = 1,
    // The user is enrolled in the treatment group, and should see the
    // experimental behavior which is being tested.
    eExperimentStatusTreatment = 2,
    // The user was enrolled in the experiment, but became ineligible due to
    // manually modifying a relevant preference.
    eExperimentStatusDisqualified = 3,
    // The user was selected for the phased Fission rollout.
    eExperimentStatusRollout = 4,

    eExperimentStatusCount,
  };

  // If you update this enum, don't forget to raise the limit in
  // TelemetryEnvironmentTesting.sys.mjs and record the new value in
  // environment.rst
  cenum ContentWin32kLockdownState : 8 {
    LockdownEnabled = 1,  // no longer used
    MissingWebRender = 2,
    OperatingSystemNotSupported = 3,
    PrefNotSet = 4,  // no longer used
    MissingRemoteWebGL = 5,
    MissingNonNativeTheming = 6,
    DisabledByEnvVar = 7,  // - MOZ_ENABLE_WIN32K is set
    //DisabledBySafeMode = 8,
    DisabledByE10S = 9,      // - E10S is disabled for whatever reason
    DisabledByUserPref = 10,  // - The user manually set
                             // security.sandbox.content.win32k-disable to false
    EnabledByUserPref = 11,  // The user manually set
                             // security.sandbox.content.win32k-disable to true
    DisabledByControlGroup =
        12,  // The user is in the Control Group, so it is disabled
    EnabledByTreatmentGroup =
        13,  // The user is in the Treatment Group, so it is enabled
    DisabledByDefault = 14,  // The default value of the pref is false
    EnabledByDefault = 15,    // The default value of the pref is true
    DecodersArentRemote = 16,
    IncompatibleMitigationPolicy = 17, // Some incompatible Windows Exploit Mitigation policies are enabled
  };

  // This is the current value of the experiment for the session
  readonly attribute nsIXULRuntime_ExperimentStatus win32kExperimentStatus;
  // This will return what the browser thinks is the _current_ status of win32k lockdown
  // but this should only be used for testing
  readonly attribute nsIXULRuntime_ContentWin32kLockdownState win32kLiveStatusTestingOnly;
  // This is the current value of win32k lockdown for the session. It is set at startup,
  // and never changed.
  readonly attribute nsIXULRuntime_ContentWin32kLockdownState win32kSessionStatus;

  // NOTE: Please do not add new values to this enum without also updating the
  // mapping in aboutSupport.js
  cenum FissionDecisionStatus : 8 {
    eFissionStatusUnknown = 0,
    // Fission is disabled because the `MOZ_FORCE_DISABLE_E10S` environment
    // variable is set.
    eFissionDisabledByE10sEnv = 3,
    // Fission is enabled because the `MOZ_FORCE_ENABLE_FISSION` environment
    // variable is set.
    eFissionEnabledByEnv = 4,
    // Fission is disabled because the `MOZ_FORCE_DISABLE_FISSION` environment
    // variable is set.
    eFissionDisabledByEnv = 5,
    // Fission is enabled because the "fission.autostart" preference is true
    // by default.
    eFissionEnabledByDefault = 7,
    // Fission is disabled because the "fission.autostart" preference is false
    // by default.
    eFissionDisabledByDefault = 8,
    // Fission is enabled because the "fission.autostart" preference was
    // turned on by the user.
    eFissionEnabledByUserPref = 9,
    // Fission is enabled because the "fission.autostart" preference was
    // turned on by the user.
    eFissionDisabledByUserPref = 10,
    // Fission is disabled because e10s is disabled for some other reason.
    eFissionDisabledByE10sOther = 11,
  };

  /**
   * Whether Fission should be automatically enabled for new browser windows.
   * This may not match the value of the 'fission.autostart' pref.
   *
   * This value is guaranteed to remain constant for the length of a browser
   * session.
   */
  readonly attribute boolean fissionAutostart;

  /**
   * The deciding factor which caused Fission to be enabled or disabled in
   * this session. The string version is the same of the name of the constant,
   * without the leading `eFission`, and with an initial lower-case letter.
   */
  readonly attribute nsIXULRuntime_FissionDecisionStatus fissionDecisionStatus;
  readonly attribute ACString fissionDecisionStatusString;

  /**
   * Whether session history is stored in the parent process.
   */
  readonly attribute boolean sessionHistoryInParent;

  /**
   * Whether Gecko code drives session store collection data.
   */
  readonly attribute boolean sessionStorePlatformCollection;

  /**
   * Whether to write console errors to a log file. If a component
   * encounters startup errors that might prevent the app from showing
   * proper UI, it should set this flag to "true".
   */
  attribute boolean logConsoleErrors;

  /**
   * A string tag identifying the current operating system. This is taken
   * from the OS_TARGET configure variable. It will always be available.
   */
  readonly attribute AUTF8String OS;

  /**
   * A string tag identifying the binary ABI of the current processor and
   * compiler vtable. This is taken from the TARGET_XPCOM_ABI configure
   * variable. It may not be available on all platforms, especially
   * unusual processor or compiler combinations.
   *
   * The result takes the form <processor>-<compilerABI>, for example:
   *   x86-msvc
   *   ppc-gcc3
   *
   * This value should almost always be used in combination with "OS".
   *
   * @throw NS_ERROR_NOT_AVAILABLE if not available.
   */
  readonly attribute AUTF8String XPCOMABI;

  /**
   * A string tag identifying the target widget toolkit in use.
   * This is taken from the MOZ_WIDGET_TOOLKIT configure variable.
   */
  readonly attribute AUTF8String widgetToolkit;

  /**
   * The legal values of processType.
   */
  const unsigned long PROCESS_TYPE_DEFAULT = 0;
  const unsigned long PROCESS_TYPE_CONTENT = 2;
  const unsigned long PROCESS_TYPE_IPDLUNITTEST = 3;
  const unsigned long PROCESS_TYPE_GMPLUGIN = 4;
  const unsigned long PROCESS_TYPE_GPU = 5;
  const unsigned long PROCESS_TYPE_VR = 6;
  const unsigned long PROCESS_TYPE_RDD = 7;
  const unsigned long PROCESS_TYPE_SOCKET = 8;
  const unsigned long PROCESS_TYPE_FORKSERVER = 10;
  const unsigned long PROCESS_TYPE_UTILITY = 11;

  /**
   * The type of the caller's process.  Returns one of the values above.
   */
  readonly attribute unsigned long processType;

  /**
   * The system process ID of the caller's process.
   */
  readonly attribute unsigned long processID;

  /**
   * A globally unique and non-recycled ID of the caller's process.
   */
  readonly attribute uint64_t uniqueProcessID;

  /**
   * The type of remote content process we're running in.
   * null if we're in the parent/chrome process. This can contain
   * a URI if Fission is enabled, so don't use it for any kind of
   * telemetry.
   */
  readonly attribute AUTF8String remoteType;

  /**
   * If true, browser tabs may be opened by default in a different process
   * from the main browser UI.
   */
  readonly attribute boolean browserTabsRemoteAutostart;

  /**
   * Returns the number of content processes to use for normal web pages. If
   * this value is > 1, then e10s-multi should be considered to be "on".
   *
   * NB: If browserTabsRemoteAutostart is false, then this value has no
   * meaning and e10s should be considered to be "off"!
   */
  readonly attribute uint32_t maxWebProcessCount;

  /**
   * The current e10s-multi experiment number. Set dom.ipc.multiOptOut to (at
   * least) this to disable it until the next experiment.
   */
  const uint32_t E10S_MULTI_EXPERIMENT = 1;

  /**
   * If true, the accessibility service is running.
   */
  readonly attribute boolean accessibilityEnabled;

  /**
   * Executable of Windows service that activated accessibility.
   */
  readonly attribute AString accessibilityInstantiator;

  /**
   * Indicates whether the current Firefox build is 64-bit.
   */
  readonly attribute boolean is64Bit;

  /**
   * Indicates whether or not text recognition of images supported by the OS.
   */
  readonly attribute boolean isTextRecognitionSupported;

  /**
   * Signal the apprunner to invalidate caches on the next restart.
   * This will cause components to be autoregistered and all
   * fastload data to be re-created.
   */
  void invalidateCachesOnRestart();

  /**
   * Modification time of the profile lock before the profile was locked on
   * this startup. Used to know the last time the profile was used and not
   * closed cleanly. This is set to 0 if there was no existing profile lock.
   */
  readonly attribute PRTime replacedLockTime;

  /**
   * The default update channel (MOZ_UPDATE_CHANNEL).
   */
  readonly attribute AUTF8String defaultUpdateChannel;

  /**
   * The distribution ID for this build (MOZ_DISTRIBUTION_ID).
   */
  readonly attribute AUTF8String distributionID;

  /**
   * True if Windows DLL blocklist initialized correctly. This is
   * primarily for automated testing purposes.
   */
  readonly attribute boolean windowsDLLBlocklistStatus;

  /**
   * True if this application was started by the OS as part of an automatic
   * restart mechanism (such as RegisterApplicationRestart on Windows).
   */
  readonly attribute boolean restartedByOS;

  /** Whether the desktop environment uses a native menubar */
  readonly attribute boolean nativeMenubar;

  /** Whether the chrome color-scheme is dark */
  readonly attribute boolean chromeColorSchemeIsDark;

  /** Whether the content color-scheme derived from the app theme is dark */
  readonly attribute boolean contentThemeDerivedColorSchemeIsDark;

  /** Whether the user prefers reduced motion */
  readonly attribute boolean prefersReducedMotion;

  /** Whether we should draw over the titlebar */
  readonly attribute boolean drawInTitlebar;

  /** Number of times the caret blinks (-1 for "never stop blinking") */
  readonly attribute int32_t caretBlinkCount;

  /** Time for half the caret blink cycle, in ms. negative for "don't blink". */
  readonly attribute int32_t caretBlinkTime;

  /** Returns the desktop environment identifier. Only meaningful on GTK */
  readonly attribute ACString desktopEnvironment;

  /** Whether we use Wayland. Only meaningful on GTK */
  readonly attribute boolean isWayland;

  /**
   * The path of the shortcut used to start the current process, or "" if none.
   *
   * Windows Main process only, otherwise throws NS_ERROR_NOT_AVAILABLE
   *
   * May be mising in some cases where the user did launch from a shortcut:
   * - If the updater ran on startup
   * - If the AUMID was set before the shortcut could be saved
   *
   * @throw NS_ERROR_NOT_AVAILABLE if not available.
   */
  readonly attribute AString processStartupShortcut;

  /**
   * Returns a value corresponding to one of the
   * |mozilla::LauncherRegistryInfo::EnabledState| values.
   */
  readonly attribute uint32_t launcherProcessState;

  /**
   * Returns the last application version that used the current profile or null
   * if the last version could not be found (compatibility.ini was either
   * missing or invalid). Throws NS_ERROR_UNAVAILABLE if called from a content
   * process.
   */
  readonly attribute ACString lastAppVersion;

  /**
   * Returns the last application build ID that used the current profile or null
   * if the last build ID could not be found (compatibility.ini was either
   * missing or invalid). Throws NS_ERROR_UNAVAILABLE if called from a content
   * process.
   */
  readonly attribute ACString lastAppBuildID;
};


%{C++

namespace mozilla {

nsIXULRuntime::ContentWin32kLockdownState GetWin32kLockdownState();

}

%}
