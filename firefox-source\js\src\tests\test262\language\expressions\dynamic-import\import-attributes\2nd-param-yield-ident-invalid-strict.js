// |reftest| shell-option(--enable-import-attributes) skip-if(!xulRuntime.shell) error:SyntaxError -- requires shell-options
'use strict';
// Copyright (C) 2021 the V8 project authors. All rights reserved.
// This code is governed by the BSD license found in the LICENSE file.
/*---
description: >
  ImportCall parameter list forwards the Yield production parameter - invalid IdentifierReference
esid: sec-import-call-runtime-semantics-evaluation
info: |
  ImportCall[Yield, Await]:
    import ( AssignmentExpression[+In, ?Yield, ?Await] ,opt )
    import ( AssignmentExpression[+In, ?Yield, ?Await] , AssignmentExpression[+In, ?Yield, ?Await] ,opt )
features: [dynamic-import, import-attributes]
flags: [onlyStrict]
negative:
  phase: parse
  type: SyntaxError
---*/
$DONOTEVALUATE();

import('./empty_FIXTURE.js', yield);
