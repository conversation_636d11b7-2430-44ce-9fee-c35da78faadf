/* -*- Mode: C++; tab-width: 2; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#include "nsIInputStream.idl"

%{C++
#include "mozilla/MemoryReporting.h"

namespace mozilla {
class StreamBufferSource;
}  // namespace mozilla
%}

native MallocSizeOf(mozilla::MallocSizeOf);
[ptr] native StreamBufferSource(mozilla::StreamBufferSource);

/**
 * nsIStringInputStream
 *
 * Provides scriptable and specialized C++-only methods for initializing a
 * nsIInputStream implementation with a simple character array.
 */
[scriptable, builtinclass, uuid(450cd2d4-f0fd-424d-b365-b1251f80fd53)]
interface nsIStringInputStream : nsIInputStream
{
    /**
     * SetData - assign data to the input stream from a byte string.
     *
     * @param data    - stream data
     *
     * NOTE: For JS callers, no UTF-8 encoding will be performed.
     */
    void setByteStringData(in ACString data);

    /**
     * SetUTF8Data - encode input data to UTF-8 and assign it to the input
     * stream.
     *
     * @param data    - stream data
     *
     * NOTE: This method is meant to be used by JS callers,
     */
    void setUTF8Data(in AUTF8String data);

    /**
     * NOTE: the following methods are designed to give C++ code added control
     * over the ownership and lifetime of the stream data.  Use with care :-)
     */

    /**
     * SetData - assign data to the input stream (copied on assignment).
     *
     * @param data    - stream data
     * @param dataLen - stream data length
     *
     * NOTE: C++ code should consider using AdoptData or ShareData to avoid
     * making an extra copy of the stream data.
     */
    [noscript] void copyData(in string data, in size_t dataLen);

    /**
     * AdoptData - assign data to the input stream.  the input stream takes
     * ownership of the given data buffer and will free it when
     * the input stream is destroyed.
     *
     * @param data      - stream data
     * @param dataLen   - stream data length
     */
    [noscript] void adoptData(in charPtr data, in size_t dataLen);

    /**
     * ShareData - assign data to the input stream.  the input stream references
     * the given data buffer until the input stream is destroyed.  the given
     * data buffer must outlive the input stream.
     *
     * @param data      - stream data
     * @param dataLen   - stream data length
     */
    [noscript] void shareData(in string data, in size_t dataLen);

    /**
     * SetDataSource - assign data to the input stream.  the input stream holds
     * a strong reference to the given data buffer until it is destroyed.
     *
     * @param source    - stream data source
     */
    [noscript] void setDataSource(in StreamBufferSource source);

    [noscript, notxpcom]
    size_t SizeOfIncludingThisIfUnshared(in MallocSizeOf aMallocSizeOf);

    [noscript, notxpcom]
    size_t SizeOfIncludingThisEvenIfShared(in MallocSizeOf aMallocSizeOf);
};
