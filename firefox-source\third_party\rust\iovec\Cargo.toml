# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies
#
# If you believe there's an error in this file please file an
# issue against the rust-lang/cargo repository. If you're
# editing this file be aware that the upstream Cargo.toml
# will likely look very different (and much more reasonable)

[package]
name = "iovec"
version = "0.1.4"
authors = ["<PERSON> <<EMAIL>>"]
description = "Portable buffer type for scatter/gather I/O operations\n"
homepage = "https://github.com/carllerche/iovec"
documentation = "https://docs.rs/iovec"
readme = "README.md"
keywords = ["scatter", "gather", "vectored", "io", "networking"]
categories = ["network-programming", "api-bindings"]
license = "MIT/Apache-2.0"
repository = "https://github.com/carllerche/iovec"
[target."cfg(unix)".dependencies.libc]
version = "0.2"
