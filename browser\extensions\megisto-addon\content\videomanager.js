/**
 * <PERSON><PERSON><PERSON>rowser - YouTube Embed Manager
 * Content script that replaces YouTube embeds with enhanced Video.js players
 */

class YouTubeEmbedManager {
  constructor() {
    this.preferences = null;
    this.processedEmbeds = new Set();
    this.observer = null;
    
    this.init();
  }
  
  async init() {
    console.log('Megisto YouTube Manager: Initializing...');
    
    // Load preferences
    await this.loadPreferences();
    
    // Only proceed if YouTube enhancement is enabled
    if (!this.preferences.youtubeEnhancement) {
      console.log('Megisto YouTube Manager: YouTube enhancement disabled');
      return;
    }
    
    // Load Video.js if not already loaded
    await this.loadVideoJS();
    
    // Process existing YouTube embeds
    this.processExistingEmbeds();
    
    // Set up observer for dynamic embeds
    this.setupObserver();
    
    console.log('Megisto YouTube Manager: Initialized');
  }
  
  async loadPreferences() {
    try {
      const response = await browser.runtime.sendMessage({ type: 'GET_PREFERENCES' });
      this.preferences = response.preferences;
      console.log('Megisto YouTube Manager: Preferences loaded:', this.preferences);
    } catch (error) {
      console.error('Megisto YouTube Manager: Failed to load preferences:', error);
      // Use defaults
      this.preferences = {
        youtubeEnhancement: true,
        youtubeAutoplay: false,
        youtubeDefaultQuality: '720p',
        youtubeBlockRelated: true
      };
    }
  }
  
  async loadVideoJS() {
    // Check if Video.js is already loaded
    if (window.videojs) {
      console.log('Megisto YouTube Manager: Video.js already loaded');
      return;
    }
    
    try {
      // Load Video.js CSS
      const cssLink = document.createElement('link');
      cssLink.rel = 'stylesheet';
      cssLink.href = 'https://vjs.zencdn.net/8.6.1/video-js.css';
      document.head.appendChild(cssLink);
      
      // Load Video.js JavaScript
      const script = document.createElement('script');
      script.src = 'https://vjs.zencdn.net/8.6.1/video.min.js';
      script.onload = () => {
        console.log('Megisto YouTube Manager: Video.js loaded');
        this.loadYouTubePlugin();
      };
      document.head.appendChild(script);
      
    } catch (error) {
      console.error('Megisto YouTube Manager: Failed to load Video.js:', error);
    }
  }
  
  async loadYouTubePlugin() {
    try {
      // Load YouTube plugin for Video.js
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/videojs-youtube@3.0.1/dist/Youtube.min.js';
      script.onload = () => {
        console.log('Megisto YouTube Manager: YouTube plugin loaded');
      };
      document.head.appendChild(script);
    } catch (error) {
      console.error('Megisto YouTube Manager: Failed to load YouTube plugin:', error);
    }
  }
  
  processExistingEmbeds() {
    const youtubeIframes = document.querySelectorAll('iframe[src*="youtube.com/embed"], iframe[src*="youtu.be"]');
    youtubeIframes.forEach(iframe => this.replaceYouTubeEmbed(iframe));
  }
  
  replaceYouTubeEmbed(iframe) {
    if (this.processedEmbeds.has(iframe)) return;
    
    console.log('Megisto YouTube Manager: Processing YouTube embed:', iframe.src);
    
    // Extract video ID from URL
    const videoId = this.extractVideoId(iframe.src);
    if (!videoId) {
      console.warn('Megisto YouTube Manager: Could not extract video ID from:', iframe.src);
      return;
    }
    
    // Create Video.js player container
    const playerContainer = this.createPlayerContainer(iframe, videoId);
    
    // Replace the iframe with our player
    iframe.parentNode.replaceChild(playerContainer, iframe);
    
    // Initialize Video.js player
    this.initializePlayer(playerContainer, videoId);
    
    this.processedEmbeds.add(iframe);
  }
  
  extractVideoId(url) {
    const patterns = [
      /youtube\.com\/embed\/([^?&]+)/,
      /youtu\.be\/([^?&]+)/,
      /youtube\.com\/watch\?v=([^&]+)/
    ];
    
    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }
    
    return null;
  }
  
  createPlayerContainer(originalIframe, videoId) {
    const container = document.createElement('div');
    container.className = 'megisto-youtube-player';
    container.style.width = originalIframe.width || '560px';
    container.style.height = originalIframe.height || '315px';
    container.style.position = 'relative';
    
    const video = document.createElement('video-js');
    video.className = 'vjs-default-skin';
    video.setAttribute('controls', '');
    video.setAttribute('preload', 'auto');
    video.setAttribute('data-setup', '{}');
    video.style.width = '100%';
    video.style.height = '100%';
    
    // Set video source
    const source = document.createElement('source');
    source.src = `https://www.youtube.com/watch?v=${videoId}`;
    source.type = 'video/youtube';
    video.appendChild(source);
    
    container.appendChild(video);
    
    return container;
  }
  
  initializePlayer(container, videoId) {
    const video = container.querySelector('video-js');
    
    // Wait for Video.js to be available
    const initPlayer = () => {
      if (!window.videojs) {
        setTimeout(initPlayer, 100);
        return;
      }
      
      try {
        const player = window.videojs(video, {
          techOrder: ['youtube'],
          sources: [{
            type: 'video/youtube',
            src: `https://www.youtube.com/watch?v=${videoId}`
          }],
          youtube: {
            ytControls: 0,
            modestbranding: 1,
            rel: this.preferences.youtubeBlockRelated ? 0 : 1,
            showinfo: 0,
            autoplay: this.preferences.youtubeAutoplay ? 1 : 0,
            iv_load_policy: 3, // Hide annotations
            cc_load_policy: 0, // Hide captions by default
            playsinline: 1
          },
          fluid: true,
          responsive: true
        });
        
        // Set default quality if specified
        player.ready(() => {
          console.log('Megisto YouTube Manager: Player ready for video:', videoId);
          
          if (this.preferences.youtubeDefaultQuality) {
            player.on('loadedmetadata', () => {
              this.setVideoQuality(player, this.preferences.youtubeDefaultQuality);
            });
          }
          
          // Add custom controls
          this.addCustomControls(player);
        });
        
        // Handle errors
        player.on('error', (error) => {
          console.error('Megisto YouTube Manager: Player error:', error);
          this.fallbackToOriginalEmbed(container, videoId);
        });
        
      } catch (error) {
        console.error('Megisto YouTube Manager: Failed to initialize player:', error);
        this.fallbackToOriginalEmbed(container, videoId);
      }
    };
    
    initPlayer();
  }
  
  setVideoQuality(player, quality) {
    try {
      // This would need to be implemented based on the specific YouTube plugin capabilities
      console.log(`Megisto YouTube Manager: Setting quality to ${quality}`);
      // player.tech().setPlaybackQuality(quality);
    } catch (error) {
      console.warn('Megisto YouTube Manager: Could not set video quality:', error);
    }
  }
  
  addCustomControls(player) {
    // Add custom control bar components
    const controlBar = player.getChild('controlBar');
    
    // Add quality selector button
    const qualityButton = document.createElement('button');
    qualityButton.className = 'vjs-control vjs-button megisto-quality-button';
    qualityButton.innerHTML = '<span class="vjs-icon-chapters"></span>';
    qualityButton.title = 'Quality';
    qualityButton.onclick = () => this.showQualityMenu(player);
    
    controlBar.el().appendChild(qualityButton);
  }
  
  showQualityMenu(player) {
    // Implementation for quality selection menu
    console.log('Megisto YouTube Manager: Show quality menu');
    // This would show a dropdown with available quality options
  }
  
  fallbackToOriginalEmbed(container, videoId) {
    console.log('Megisto YouTube Manager: Falling back to original embed');
    
    const iframe = document.createElement('iframe');
    iframe.src = `https://www.youtube.com/embed/${videoId}?modestbranding=1&rel=0`;
    iframe.width = container.style.width;
    iframe.height = container.style.height;
    iframe.frameBorder = '0';
    iframe.allowFullscreen = true;
    
    container.parentNode.replaceChild(iframe, container);
  }
  
  setupObserver() {
    this.observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check for YouTube iframes in the added node
            const youtubeIframes = node.querySelectorAll ? 
              node.querySelectorAll('iframe[src*="youtube.com/embed"], iframe[src*="youtu.be"]') : [];
            
            youtubeIframes.forEach(iframe => this.replaceYouTubeEmbed(iframe));
            
            // Check if the node itself is a YouTube iframe
            if (node.tagName === 'IFRAME' && 
                (node.src.includes('youtube.com/embed') || node.src.includes('youtu.be'))) {
              this.replaceYouTubeEmbed(node);
            }
          }
        });
      });
    });
    
    this.observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new YouTubeEmbedManager();
  });
} else {
  new YouTubeEmbedManager();
}
