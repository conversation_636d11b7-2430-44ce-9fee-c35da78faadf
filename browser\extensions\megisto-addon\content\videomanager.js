/**
 * <PERSON><PERSON><PERSON>rowser - YouTube Embed Manager
 * Content script that replaces YouTube embeds with enhanced Video.js players
 */

class YouTubeEmbedManager {
  constructor() {
    this.preferences = null;
    this.processedEmbeds = new Set();
    this.observer = null;
    
    this.init();
  }
  
  async init() {
    console.log('Megisto YouTube Manager: Initializing...');
    
    // Load preferences
    await this.loadPreferences();
    
    // Only proceed if YouTube enhancement is enabled
    if (!this.preferences.youtubeEnhancement) {
      console.log('Megisto YouTube Manager: YouTube enhancement disabled');
      return;
    }
    
    // Load Video.js if not already loaded
    await this.loadVideoJS();
    
    // Process existing YouTube embeds
    this.processExistingEmbeds();
    
    // Set up observer for dynamic embeds
    this.setupObserver();
    
    console.log('Megisto YouTube Manager: Initialized');
  }
  
  async loadPreferences() {
    try {
      const response = await browser.runtime.sendMessage({ type: 'GET_PREFERENCES' });
      this.preferences = response.preferences;
      console.log('Megisto YouTube Manager: Preferences loaded:', this.preferences);
    } catch (error) {
      console.error('Megisto YouTube Manager: Failed to load preferences:', error);
      // Use defaults
      this.preferences = {
        youtubeEnhancement: true,
        youtubeAutoplay: false,
        youtubeDefaultQuality: '720p',
        youtubeBlockRelated: true
      };
    }
  }
  
  async loadVideoJS() {
    // Check if Video.js is already loaded
    if (window.videojs) {
      console.log('Megisto YouTube Manager: Video.js already loaded');
      return;
    }
    
    try {
      // Load Video.js CSS
      const cssLink = document.createElement('link');
      cssLink.rel = 'stylesheet';
      cssLink.href = 'https://vjs.zencdn.net/8.6.1/video-js.css';
      document.head.appendChild(cssLink);
      
      // Load Video.js JavaScript
      const script = document.createElement('script');
      script.src = 'https://vjs.zencdn.net/8.6.1/video.min.js';
      script.onload = () => {
        console.log('Megisto YouTube Manager: Video.js loaded');
        this.loadYouTubePlugin();
      };
      document.head.appendChild(script);
      
    } catch (error) {
      console.error('Megisto YouTube Manager: Failed to load Video.js:', error);
    }
  }
  
  async loadYouTubePlugin() {
    try {
      // Load YouTube plugin for Video.js
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/videojs-youtube@3.0.1/dist/Youtube.min.js';
      script.onload = () => {
        console.log('Megisto YouTube Manager: YouTube plugin loaded');
      };
      document.head.appendChild(script);
    } catch (error) {
      console.error('Megisto YouTube Manager: Failed to load YouTube plugin:', error);
    }
  }
  
  processExistingEmbeds() {
    const youtubeIframes = document.querySelectorAll('iframe[src*="youtube.com/embed"], iframe[src*="youtu.be"]');
    youtubeIframes.forEach(iframe => this.replaceYouTubeEmbed(iframe));
  }
  
  replaceYouTubeEmbed(iframe) {
    if (this.processedEmbeds.has(iframe)) return;
    
    console.log('Megisto YouTube Manager: Processing YouTube embed:', iframe.src);
    
    // Extract video ID from URL
    const videoId = this.extractVideoId(iframe.src);
    if (!videoId) {
      console.warn('Megisto YouTube Manager: Could not extract video ID from:', iframe.src);
      return;
    }
    
    // Create Video.js player container
    const playerContainer = this.createPlayerContainer(iframe, videoId);
    
    // Replace the iframe with our player
    iframe.parentNode.replaceChild(playerContainer, iframe);
    
    // Initialize Video.js player
    this.initializePlayer(playerContainer, videoId);
    
    this.processedEmbeds.add(iframe);
  }
  
  extractVideoId(url) {
    const patterns = [
      /youtube\.com\/embed\/([^?&]+)/,
      /youtu\.be\/([^?&]+)/,
      /youtube\.com\/watch\?v=([^&]+)/,
      /youtube\.com\/v\/([^?&]+)/,
      /youtube\.com\/watch\?.*v=([^&]+)/,
      /youtube-nocookie\.com\/embed\/([^?&]+)/
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }

    return null;
  }

  /**
   * Extract additional parameters from YouTube URL
   */
  extractVideoParams(url) {
    const params = {};
    const urlObj = new URL(url);

    // Common YouTube parameters
    const paramMap = {
      't': 'start',
      'start': 'start',
      'end': 'end',
      'autoplay': 'autoplay',
      'loop': 'loop',
      'mute': 'muted',
      'controls': 'controls',
      'rel': 'rel',
      'modestbranding': 'modestbranding',
      'showinfo': 'showinfo',
      'iv_load_policy': 'iv_load_policy',
      'cc_load_policy': 'cc_load_policy',
      'playsinline': 'playsinline'
    };

    for (const [ytParam, playerParam] of Object.entries(paramMap)) {
      const value = urlObj.searchParams.get(ytParam);
      if (value !== null) {
        params[playerParam] = value;
      }
    }

    return params;
  }
  
  createPlayerContainer(originalIframe, videoId) {
    const container = document.createElement('div');
    container.className = 'megisto-youtube-player';
    container.style.width = originalIframe.width || '560px';
    container.style.height = originalIframe.height || '315px';
    container.style.position = 'relative';
    
    const video = document.createElement('video-js');
    video.className = 'vjs-default-skin';
    video.setAttribute('controls', '');
    video.setAttribute('preload', 'auto');
    video.setAttribute('data-setup', '{}');
    video.style.width = '100%';
    video.style.height = '100%';
    
    // Set video source
    const source = document.createElement('source');
    source.src = `https://www.youtube.com/watch?v=${videoId}`;
    source.type = 'video/youtube';
    video.appendChild(source);
    
    container.appendChild(video);
    
    return container;
  }
  
  initializePlayer(container, videoId, originalUrl) {
    const video = container.querySelector('video-js');
    const videoParams = this.extractVideoParams(originalUrl);

    // Wait for Video.js to be available
    const initPlayer = () => {
      if (!window.videojs) {
        setTimeout(initPlayer, 100);
        return;
      }

      try {
        // Merge preferences with URL parameters
        const playerOptions = {
          techOrder: ['youtube'],
          sources: [{
            type: 'video/youtube',
            src: `https://www.youtube.com/watch?v=${videoId}`
          }],
          youtube: {
            ytControls: 0,
            modestbranding: 1,
            rel: this.preferences.youtubeBlockRelated ? 0 : 1,
            showinfo: 0,
            autoplay: videoParams.autoplay || (this.preferences.youtubeAutoplay ? 1 : 0),
            iv_load_policy: 3, // Hide annotations
            cc_load_policy: videoParams.cc_load_policy || 0,
            playsinline: videoParams.playsinline || 1,
            start: videoParams.start || 0,
            end: videoParams.end || 0,
            loop: videoParams.loop || 0,
            mute: videoParams.muted || 0
          },
          fluid: true,
          responsive: true,
          controls: true,
          preload: 'metadata',
          playbackRates: [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2],
          plugins: {
            hotkeys: {
              volumeStep: 0.1,
              seekStep: 5,
              enableModifiersForNumbers: false
            }
          }
        };

        const player = window.videojs(video, playerOptions);

        // Set up event handlers
        this.setupPlayerEvents(player, videoId, container);

        // Set default quality if specified
        player.ready(() => {
          console.log('Megisto YouTube Manager: Player ready for video:', videoId);

          if (this.preferences.youtubeDefaultQuality && this.preferences.youtubeDefaultQuality !== 'auto') {
            player.on('loadedmetadata', () => {
              this.setVideoQuality(player, this.preferences.youtubeDefaultQuality);
            });
          }

          // Add custom controls
          this.addCustomControls(player, videoId);

          // Apply custom styling
          this.applyCustomStyling(player, container);
        });

      } catch (error) {
        console.error('Megisto YouTube Manager: Failed to initialize player:', error);
        this.fallbackToOriginalEmbed(container, videoId);
      }
    };

    initPlayer();
  }
  
  setVideoQuality(player, quality) {
    try {
      // This would need to be implemented based on the specific YouTube plugin capabilities
      console.log(`Megisto YouTube Manager: Setting quality to ${quality}`);
      // player.tech().setPlaybackQuality(quality);
    } catch (error) {
      console.warn('Megisto YouTube Manager: Could not set video quality:', error);
    }
  }
  
  setupPlayerEvents(player, videoId, container) {
    // Error handling
    player.on('error', (error) => {
      console.error('Megisto YouTube Manager: Player error:', error);
      this.fallbackToOriginalEmbed(container, videoId);
    });

    // Track playback statistics
    player.on('play', () => {
      console.log('Megisto YouTube Manager: Video started:', videoId);
      this.updateStatistics('videoPlayed');
    });

    player.on('ended', () => {
      console.log('Megisto YouTube Manager: Video ended:', videoId);
      this.updateStatistics('videoCompleted');
    });

    // Handle fullscreen changes
    player.on('fullscreenchange', () => {
      if (player.isFullscreen()) {
        console.log('Megisto YouTube Manager: Entered fullscreen');
      } else {
        console.log('Megisto YouTube Manager: Exited fullscreen');
      }
    });

    // Handle quality changes
    player.on('qualitychange', (event) => {
      console.log('Megisto YouTube Manager: Quality changed to:', event.quality);
    });
  }

  addCustomControls(player, videoId) {
    try {
      // Add custom control bar components
      const controlBar = player.getChild('controlBar');

      if (!controlBar) {
        console.warn('Megisto YouTube Manager: Control bar not found');
        return;
      }

      // Add quality selector button
      this.addQualityButton(player, controlBar);

      // Add picture-in-picture button
      this.addPiPButton(player, controlBar);

      // Add download button (if enabled)
      if (this.preferences.enableDownload) {
        this.addDownloadButton(player, controlBar, videoId);
      }

      // Add theater mode button
      this.addTheaterModeButton(player, controlBar);

    } catch (error) {
      console.error('Megisto YouTube Manager: Failed to add custom controls:', error);
    }
  }

  addQualityButton(player, controlBar) {
    const qualityButton = document.createElement('button');
    qualityButton.className = 'vjs-control vjs-button megisto-quality-button';
    qualityButton.innerHTML = '<span class="vjs-icon-chapters" aria-hidden="true"></span><span class="vjs-control-text">Quality</span>';
    qualityButton.title = 'Video Quality';
    qualityButton.setAttribute('aria-label', 'Video Quality');
    qualityButton.onclick = () => this.showQualityMenu(player);

    // Insert before fullscreen button
    const fullscreenButton = controlBar.el().querySelector('.vjs-fullscreen-control');
    if (fullscreenButton) {
      controlBar.el().insertBefore(qualityButton, fullscreenButton);
    } else {
      controlBar.el().appendChild(qualityButton);
    }
  }

  addPiPButton(player, controlBar) {
    if (!document.pictureInPictureEnabled) {
      return; // PiP not supported
    }

    const pipButton = document.createElement('button');
    pipButton.className = 'vjs-control vjs-button megisto-pip-button';
    pipButton.innerHTML = '<span class="vjs-icon-picture-in-picture-enter" aria-hidden="true"></span><span class="vjs-control-text">Picture-in-Picture</span>';
    pipButton.title = 'Picture-in-Picture';
    pipButton.setAttribute('aria-label', 'Picture-in-Picture');
    pipButton.onclick = () => this.togglePictureInPicture(player);

    const fullscreenButton = controlBar.el().querySelector('.vjs-fullscreen-control');
    if (fullscreenButton) {
      controlBar.el().insertBefore(pipButton, fullscreenButton);
    } else {
      controlBar.el().appendChild(pipButton);
    }
  }

  addDownloadButton(player, controlBar, videoId) {
    const downloadButton = document.createElement('button');
    downloadButton.className = 'vjs-control vjs-button megisto-download-button';
    downloadButton.innerHTML = '<span class="vjs-icon-download" aria-hidden="true"></span><span class="vjs-control-text">Download</span>';
    downloadButton.title = 'Download Video';
    downloadButton.setAttribute('aria-label', 'Download Video');
    downloadButton.onclick = () => this.downloadVideo(videoId);

    const fullscreenButton = controlBar.el().querySelector('.vjs-fullscreen-control');
    if (fullscreenButton) {
      controlBar.el().insertBefore(downloadButton, fullscreenButton);
    } else {
      controlBar.el().appendChild(downloadButton);
    }
  }

  addTheaterModeButton(player, controlBar) {
    const theaterButton = document.createElement('button');
    theaterButton.className = 'vjs-control vjs-button megisto-theater-button';
    theaterButton.innerHTML = '<span class="vjs-icon-chapters" aria-hidden="true"></span><span class="vjs-control-text">Theater Mode</span>';
    theaterButton.title = 'Theater Mode';
    theaterButton.setAttribute('aria-label', 'Theater Mode');
    theaterButton.onclick = () => this.toggleTheaterMode(player);

    const fullscreenButton = controlBar.el().querySelector('.vjs-fullscreen-control');
    if (fullscreenButton) {
      controlBar.el().insertBefore(theaterButton, fullscreenButton);
    } else {
      controlBar.el().appendChild(theaterButton);
    }
  }
  
  showQualityMenu(player) {
    console.log('Megisto YouTube Manager: Show quality menu');

    // Create quality menu if it doesn't exist
    let qualityMenu = document.querySelector('.megisto-quality-menu');
    if (qualityMenu) {
      qualityMenu.style.display = qualityMenu.style.display === 'none' ? 'block' : 'none';
      return;
    }

    qualityMenu = document.createElement('div');
    qualityMenu.className = 'megisto-quality-menu';
    qualityMenu.style.cssText = `
      position: absolute;
      bottom: 50px;
      right: 10px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      border-radius: 4px;
      padding: 8px;
      z-index: 1000;
      min-width: 120px;
    `;

    const qualities = ['auto', '144p', '240p', '360p', '480p', '720p', '1080p', '1440p', '2160p'];

    qualities.forEach(quality => {
      const option = document.createElement('div');
      option.textContent = quality;
      option.style.cssText = `
        padding: 4px 8px;
        cursor: pointer;
        border-radius: 2px;
      `;
      option.onmouseover = () => option.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
      option.onmouseout = () => option.style.backgroundColor = 'transparent';
      option.onclick = () => {
        this.setVideoQuality(player, quality);
        qualityMenu.remove();
      };

      if (quality === this.preferences.youtubeDefaultQuality) {
        option.style.backgroundColor = 'rgba(255, 255, 255, 0.3)';
      }

      qualityMenu.appendChild(option);
    });

    player.el().appendChild(qualityMenu);

    // Close menu when clicking outside
    setTimeout(() => {
      document.addEventListener('click', function closeMenu(e) {
        if (!qualityMenu.contains(e.target)) {
          qualityMenu.remove();
          document.removeEventListener('click', closeMenu);
        }
      });
    }, 100);
  }

  togglePictureInPicture(player) {
    const video = player.el().querySelector('video');

    if (!video) {
      console.warn('Megisto YouTube Manager: Video element not found for PiP');
      return;
    }

    if (document.pictureInPictureElement) {
      document.exitPictureInPicture().catch(error => {
        console.error('Megisto YouTube Manager: Failed to exit PiP:', error);
      });
    } else {
      video.requestPictureInPicture().catch(error => {
        console.error('Megisto YouTube Manager: Failed to enter PiP:', error);
      });
    }
  }

  downloadVideo(videoId) {
    console.log('Megisto YouTube Manager: Download requested for:', videoId);

    // Open YouTube video in new tab for user to download using external tools
    const youtubeUrl = `https://www.youtube.com/watch?v=${videoId}`;
    window.open(youtubeUrl, '_blank');

    // Show informational message
    this.showNotification('Video opened in new tab. Use browser extensions or external tools to download.');
  }

  toggleTheaterMode(player) {
    const container = player.el().closest('.megisto-youtube-player');

    if (!container) {
      console.warn('Megisto YouTube Manager: Container not found for theater mode');
      return;
    }

    const isTheaterMode = container.classList.contains('megisto-theater-mode');

    if (isTheaterMode) {
      container.classList.remove('megisto-theater-mode');
      container.style.width = container.dataset.originalWidth || '560px';
      container.style.height = container.dataset.originalHeight || '315px';
    } else {
      container.dataset.originalWidth = container.style.width;
      container.dataset.originalHeight = container.style.height;
      container.classList.add('megisto-theater-mode');
      container.style.width = '100vw';
      container.style.height = '56.25vw'; // 16:9 aspect ratio
      container.style.maxHeight = '100vh';
    }
  }

  applyCustomStyling(player, container) {
    // Add custom CSS for enhanced player
    const style = document.createElement('style');
    style.textContent = `
      .megisto-youtube-player {
        position: relative;
        background: #000;
        border-radius: 8px;
        overflow: hidden;
      }

      .megisto-theater-mode {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        z-index: 9999 !important;
        border-radius: 0 !important;
      }

      .megisto-quality-button,
      .megisto-pip-button,
      .megisto-download-button,
      .megisto-theater-button {
        background: transparent;
        border: none;
        color: white;
        cursor: pointer;
        padding: 0.5em;
        margin: 0 0.2em;
      }

      .megisto-quality-button:hover,
      .megisto-pip-button:hover,
      .megisto-download-button:hover,
      .megisto-theater-button:hover {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 3px;
      }

      .vjs-control-text {
        position: absolute;
        left: -9999px;
      }
    `;

    document.head.appendChild(style);
  }

  showNotification(message) {
    const notification = document.createElement('div');
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 12px 16px;
      border-radius: 4px;
      z-index: 10000;
      max-width: 300px;
      font-size: 14px;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.remove();
    }, 5000);
  }

  updateStatistics(action) {
    try {
      browser.runtime.sendMessage({
        type: 'UPDATE_STATISTICS',
        action: action
      });
    } catch (error) {
      console.error('Megisto YouTube Manager: Failed to update statistics:', error);
    }
  }
  
  fallbackToOriginalEmbed(container, videoId) {
    console.log('Megisto YouTube Manager: Falling back to original embed');
    
    const iframe = document.createElement('iframe');
    iframe.src = `https://www.youtube.com/embed/${videoId}?modestbranding=1&rel=0`;
    iframe.width = container.style.width;
    iframe.height = container.style.height;
    iframe.frameBorder = '0';
    iframe.allowFullscreen = true;
    
    container.parentNode.replaceChild(iframe, container);
  }
  
  setupObserver() {
    this.observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check for YouTube iframes in the added node
            const youtubeIframes = node.querySelectorAll ? 
              node.querySelectorAll('iframe[src*="youtube.com/embed"], iframe[src*="youtu.be"]') : [];
            
            youtubeIframes.forEach(iframe => this.replaceYouTubeEmbed(iframe));
            
            // Check if the node itself is a YouTube iframe
            if (node.tagName === 'IFRAME' && 
                (node.src.includes('youtube.com/embed') || node.src.includes('youtu.be'))) {
              this.replaceYouTubeEmbed(node);
            }
          }
        });
      });
    });
    
    this.observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new YouTubeEmbedManager();
  });
} else {
  new YouTubeEmbedManager();
}
