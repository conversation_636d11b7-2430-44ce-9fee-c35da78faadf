# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies
#
# If you believe there's an error in this file please file an
# issue against the rust-lang/cargo repository. If you're
# editing this file be aware that the upstream Cargo.toml
# will likely look very different (and much more reasonable)

[package]
name = "stable_deref_trait"
version = "1.2.0"
authors = ["<PERSON> <<EMAIL>>"]
description = "An unsafe marker trait for types like Box and Rc that dereference to a stable address even when moved, and hence can be used with libraries such as owning_ref and rental.\n"
documentation = "https://docs.rs/stable_deref_trait/1.2.0/stable_deref_trait"
readme = "README.md"
categories = ["memory-management", "no-std"]
license = "MIT/Apache-2.0"
repository = "https://github.com/storyyeller/stable_deref_trait"

[features]
alloc = []
default = ["std"]
std = ["alloc"]
