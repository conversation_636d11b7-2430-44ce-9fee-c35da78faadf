<!DOCTYPE html>
<title>
Opacity animation creates a stacking context even if the opacity property
is overridden by an !important rule
</title>
<style>
span {
  height: 100px;
  width: 100px;
  position: fixed;
  background: green;
  top: 50px;
}
@keyframes Opaque {
  from, to { opacity: 1 }
}
#test {
  width: 100px; height: 100px;
  background: blue;
  animation: Opaque 100s;
  opacity: 1 !important;
}
</style>
<span></span>
<div id="test"></div>
