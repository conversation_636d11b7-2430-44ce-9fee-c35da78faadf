// Copyright (c) 2012 Ecma International.  All rights reserved.
// This code is governed by the BSD license found in the LICENSE file.

/*---
es5id: ********-6-a-232
description: >
    Object.defineProperties - 'O' is an Array, 'P' is an array index
    property,  'P' is accessor property and 'desc' is data descriptor,
    and the [[Configurable]] attribute value of 'P' is true, test 'P'
    is converted from accessor property to data property  (********
    step 4.c)
includes: [propertyHelper.js]
---*/

var arr = [];

Object.defineProperty(arr, "1", {
  get: function() {
    return 3;
  },
  configurable: true

});

Object.defineProperties(arr, {
  "1": {
    value: 12
  }
});

verifyProperty(arr, "1", {
  value: 12,
  writable: false,
  enumerable: false,
  configurable: true,
});

reportCompare(0, 0);
