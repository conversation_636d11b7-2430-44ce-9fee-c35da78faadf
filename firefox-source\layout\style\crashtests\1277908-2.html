<script>
function start() {
  o28=document.createElement('a');
  o28.href='javascript:x()';
  o115=document.createElement('tr');
  o116=document.createElement('th');
  o116.innerHTML="<style>{}\n*{ display: table;> </style><style>@keyframes key8 { from{ left; background-position-x: 128vw}to{}label}\n*{ animation-name: key8; animation-duration: 0.001s";
  document.documentElement.appendChild(o28);
  document.documentElement.appendChild(o115);
  document.documentElement.appendChild(o116);
  o213=document.createElement('input');
  o115.appendChild(o213);
  o216=document.createElement('style');
  o217=document.createTextNode("*{ text-shadow: 196608rem -3px");
  o216.appendChild(o217);
  o213.appendChild(o216);
}
</script>
<body onload="start()"></body>
