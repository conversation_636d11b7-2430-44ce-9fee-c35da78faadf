Metadata-Version: 2.1
Name: mohawk
Version: 1.1.0
Summary: Library for Hawk HTTP authorization
Home-page: https://github.com/kumar303/mohawk
Author: <PERSON>, <PERSON>
Author-email: kumar.m<PERSON><PERSON><PERSON>@gmail.com
License: MPL 2.0 (Mozilla Public License)
Platform: UNKNOWN
Classifier: Intended Audience :: Developers
Classifier: Natural Language :: English
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 2.6
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Topic :: Internet :: WWW/HTTP
Requires-Dist: six


<PERSON> lets two parties securely communicate with each other using
messages signed by a shared key.
It is based on HTTP MAC access authentication (which
was based on parts of OAuth 1.0).

The Mohawk API is a little different from that of the Node library
(i.e. https://github.com/hueniverse/hawk).
It was redesigned to be more intuitive to developers, less prone to security problems, and more Pythonic.

Read more: https://github.com/kumar303/mohawk/


