# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

toolkit.jar:
   res/ua.css                                (res/ua.css)
   res/html.css                              (res/html.css)
   res/quirk.css                             (res/quirk.css)
   res/counterstyles.css                     (res/counterstyles.css)
   res/noframes.css                          (res/noframes.css)
   res/scrollbars.css                        (res/scrollbars.css)
   res/forms.css                             (res/forms.css)
   res/accessiblecaret.css                   (res/accessiblecaret.css)
   res/details.css                           (res/details.css)
 #ifdef ANDROID
   res/accessiblecaret-normal.svg            (res/accessiblecaret-normal.svg)
   res/accessiblecaret-tilt-left.svg         (res/accessiblecaret-tilt-left.svg)
   res/accessiblecaret-tilt-right.svg        (res/accessiblecaret-tilt-right.svg)
 #else
   res/<EMAIL>         (res/<EMAIL>)
   res/<EMAIL>       (res/<EMAIL>)
   res/<EMAIL>         (res/<EMAIL>)
   res/<EMAIL>      (res/<EMAIL>)
   res/<EMAIL>      (res/<EMAIL>)
   res/<EMAIL>    (res/<EMAIL>)
   res/<EMAIL>      (res/<EMAIL>)
   res/<EMAIL>   (res/<EMAIL>)
   res/<EMAIL>     (res/<EMAIL>)
   res/<EMAIL>   (res/<EMAIL>)
   res/<EMAIL>     (res/<EMAIL>)
   res/<EMAIL>  (res/<EMAIL>)
 #endif

% resource gre-resources %res/
% resource content-accessible resource://gre/contentaccessible/ contentaccessible=yes
