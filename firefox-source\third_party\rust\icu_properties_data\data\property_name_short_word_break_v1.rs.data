// @generated
/// Implement `DataProvider<PropertyNameShortWordBreakV1>` on the given struct using the data
/// hardcoded in this file. This allows the struct to be used with
/// `icu`'s `_unstable` constructors.
///
/// Using this implementation will embed the following data in the binary's data segment:
/// * 130B[^1] for the singleton data struct
///
/// [^1]: these numbers can be smaller in practice due to linker deduplication
///
/// This macro requires the following crates:
/// * `icu`
/// * `icu_provider`
/// * `zerovec`
#[doc(hidden)]
#[macro_export]
macro_rules! __impl_property_name_short_word_break_v1 {
    ($ provider : ty) => {
        #[clippy::msrv = "1.82"]
        const _: () = <$provider>::MUST_USE_MAKE_PROVIDER_MACRO;
        #[clippy::msrv = "1.82"]
        impl $provider {
            #[doc(hidden)]
            pub const SINGLETON_PROPERTY_NAME_SHORT_WORD_BREAK_V1: &'static <icu::properties::provider::PropertyNameShortWordBreakV1 as icu_provider::DynamicDataMarker>::DataStruct = &icu::properties::provider::names::PropertyEnumToValueNameLinearMap { map: unsafe { zerovec::vecs::VarZeroVec16::from_bytes_unchecked(b"\x17\0\x02\0\x04\0\x06\0\x08\0\n\0\x0C\0\x0E\0\x10\0\x12\0\x18\0\x1A\0\x1C\0\x1E\0 \0\"\0$\0&\0(\0+\0-\x000\x003\0XXLEFOKAMLMNNUEXCRExtendLFMBNLRIHLSQDQEBEBGEMGAZZWJWSegSpace") } };
        }
        #[clippy::msrv = "1.82"]
        impl icu_provider::DataProvider<icu::properties::provider::PropertyNameShortWordBreakV1> for $provider {
            fn load(&self, req: icu_provider::DataRequest) -> Result<icu_provider::DataResponse<icu::properties::provider::PropertyNameShortWordBreakV1>, icu_provider::DataError> {
                if req.id.locale.is_unknown() {
                    Ok(icu_provider::DataResponse { payload: icu_provider::DataPayload::from_static_ref(Self::SINGLETON_PROPERTY_NAME_SHORT_WORD_BREAK_V1), metadata: icu_provider::DataResponseMetadata::default() })
                } else {
                    Err(icu_provider::DataErrorKind::InvalidRequest.with_req(<icu::properties::provider::PropertyNameShortWordBreakV1 as icu_provider::DataMarker>::INFO, req))
                }
            }
        }
    };
    ($ provider : ty , ITER) => {
        __impl_property_name_short_word_break_v1!($provider);
        #[clippy::msrv = "1.82"]
        impl icu_provider::IterableDataProvider<icu::properties::provider::PropertyNameShortWordBreakV1> for $provider {
            fn iter_ids(&self) -> Result<std::collections::BtreeSet<icu_provider::DataIdentifierCow<'static>>, icu_provider::DataError> {
                Ok([Default::default()].into_iter().collect())
            }
        }
    };
    ($ provider : ty , DRY) => {
        __impl_property_name_short_word_break_v1!($provider);
        #[clippy::msrv = "1.82"]
        impl icu_provider::DryDataProvider<icu::properties::provider::PropertyNameShortWordBreakV1> for $provider {
            fn dry_load(&self, req: icu_provider::DataRequest) -> Result<icu_provider::DataResponseMetadata, icu_provider::DataError> {
                if req.id.locale.is_unknown() {
                    Ok(icu_provider::DataResponseMetadata::default())
                } else {
                    Err(icu_provider::DataErrorKind::InvalidRequest.with_req(<icu::properties::provider::PropertyNameShortWordBreakV1 as icu_provider::DataMarker>::INFO, req))
                }
            }
        }
    };
    ($ provider : ty , DRY , ITER) => {
        __impl_property_name_short_word_break_v1!($provider);
        #[clippy::msrv = "1.82"]
        impl icu_provider::DryDataProvider<icu::properties::provider::PropertyNameShortWordBreakV1> for $provider {
            fn dry_load(&self, req: icu_provider::DataRequest) -> Result<icu_provider::DataResponseMetadata, icu_provider::DataError> {
                if req.id.locale.is_unknown() {
                    Ok(icu_provider::DataResponseMetadata::default())
                } else {
                    Err(icu_provider::DataErrorKind::InvalidRequest.with_req(<icu::properties::provider::PropertyNameShortWordBreakV1 as icu_provider::DataMarker>::INFO, req))
                }
            }
        }
        #[clippy::msrv = "1.82"]
        impl icu_provider::IterableDataProvider<icu::properties::provider::PropertyNameShortWordBreakV1> for $provider {
            fn iter_ids(&self) -> Result<std::collections::BtreeSet<icu_provider::DataIdentifierCow<'static>>, icu_provider::DataError> {
                Ok([Default::default()].into_iter().collect())
            }
        }
    };
}
#[doc(inline)]
pub use __impl_property_name_short_word_break_v1 as impl_property_name_short_word_break_v1;
