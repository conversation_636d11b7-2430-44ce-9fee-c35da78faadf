INFO: Adding configure options from E:\megisto\mozconfig
INFO:   --enable-application=browser
INFO:   --enable-optimize
INFO:   --disable-debug
INFO:   --disable-debug-symbols
INFO:   --with-app-name=megisto
INFO:   --with-app-basename=Megisto
INFO:   --with-branding=browser/branding/megisto
INFO:   --enable-extensions
INFO:   --disable-telemetry
INFO:   --disable-crashreporter
INFO:   --disable-updater
INFO:   --enable-release
INFO:   --enable-strip
INFO:   --enable-install-strip
INFO:   --target=x86_64-pc-mingw32
INFO:   --host=x86_64-pc-mingw32
INFO: checking for vcs source checkout... 
INFO: git
INFO: checking for a shell... 
INFO: C:/mozilla-build/msys2/usr/bin/sh.exe
INFO: checking for host system type... 
DEBUG: Executing: `C:/mozilla-build/msys2/usr/bin/sh.exe E:/megisto/firefox-source/build/moz.configure/../autoconf/config.sub x86_64-pc-mingw32`
INFO: x86_64-pc-mingw32
INFO: checking for target system type... 
DEBUG: Executing: `C:/mozilla-build/msys2/usr/bin/sh.exe E:/megisto/firefox-source/build/moz.configure/../autoconf/config.sub x86_64-pc-mingw32`
INFO: x86_64-pc-mingw32
INFO: checking whether cross compiling... 
INFO: no
INFO: checking if configuration file confvars.sh exists... 
INFO: E:/megisto/firefox-source/browser/confvars.sh
INFO: checking if configuration file configure.sh exists... 
ERROR: Expecting key=value format (E:/megisto/firefox-source/browser/branding/megisto/configure.sh, line 1)
