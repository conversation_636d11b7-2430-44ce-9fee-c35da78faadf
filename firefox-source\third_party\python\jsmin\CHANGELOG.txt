Changelog
=========

v3.0.0 (2021-09-08) <PERSON> Bradshaw
--------------------------------

- Breaking Change: Removed support for Python 2

- Removed usage of use_2to3 in setup.py

v2.2.2 (2017-05-01) Tikitu de Jager
-----------------------------------

- Add license headers to code files (fixes i#17)

- Remove mercurial files (fixes #20)

v2.2.1 (2016-03-06) Tikitu de Jager
-----------------------------------

- Fix #14: Infinite loop on `return x / 1;`

v2.2.0 (2015-12-19) Tikitu de Jager
-----------------------------------

- Merge #13: Preserve "loud comments" starting with `/*!`

  These are commonly used for copyright notices, and are preserved by various
  other minifiers (e.g. YUI Compressor).

v2.1.6 (2015-10-14) Tiki<PERSON> de Jager
-----------------------------------

- Fix #12: Newline following a regex literal should not be elided.

v2.1.5 (2015-10-11) Tikitu de Jager
-----------------------------------

- Fix #9: Premature end of statement caused by multi-line comment not
  adding newline.

- Fix #10: Removing multiline comment separating tokens must leave a space.

- Refactor comment handling for maintainability.

v2.1.4 (2015-08-23) Tikitu de Jager
-----------------------------------

- Fix #6: regex literal matching comment was not correctly matched.

- Refactor regex literal handling for robustness.

v2.1.3 (2015-08-09) Tikitu de Jager
-----------------------------------

- Reset issue numbering: issues live in github from now on.

- Fix #1: regex literal was not recognised when occurring directly after `{`.

v2.1.2 (2015-07-12) Tikitu de Jager
-----------------------------------

- Issue numbers here and below refer to the bitbucket repository.

- Fix #17: bug when JS starts with comment then literal regex.

v2.1.1 (2015-02-14) Tikitu de Jager
-----------------------------------

- Fix #16: bug returning a literal regex containing escaped forward-slashes.

v2.1.0 (2014-12-24) Tikitu de Jager
-----------------------------------

- First changelog entries; see README.rst for prior contributors.

- Expose quote_chars parameter to provide just enough unofficial Harmony
  support to be useful.

