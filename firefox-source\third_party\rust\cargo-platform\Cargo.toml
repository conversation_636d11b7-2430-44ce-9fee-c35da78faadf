# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "cargo-platform"
version = "0.1.2"
authors = ["The Cargo Project Developers"]
description = "Cargo's representation of a target platform."
homepage = "https://github.com/rust-lang/cargo"
documentation = "https://docs.rs/cargo-platform"
license = "MIT OR Apache-2.0"
repository = "https://github.com/rust-lang/cargo"
[dependencies.serde]
version = "1.0.82"
features = ["derive"]
