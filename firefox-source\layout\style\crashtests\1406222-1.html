<script>
window.addEventListener("load", function(event) {
  setTimeout(window.close, 1000);
});
</script>
<!-- a -->
<style id="s1">
:not(video) { position: fixed; }
a::first-line {}
*, .class3 { columns: 0px; }
</style>
<script>
function jsfuzzer() {
try { s1.appendChild(htmlvar00009); } catch(e) { }
try { htmlvar00001.scrollIntoView(true); } catch(e) { }
try { htmlvar00003.href = undefined; } catch(e) { }
try { document.createEvent("1"); } catch(e) { }
}
</script>
<body onload=jsfuzzer()>
<shadow id="htmlvar00001">
<a id="htmlvar00003">
</a>
<a id="htmlvar00009">
<!-- a -->
