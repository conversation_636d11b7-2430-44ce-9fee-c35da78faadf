<!DOCTYPE html>
<html>
	<head>
		<title>CSS Test: blending between an element and the child with opacity</title>
		<link rel="author" title="<PERSON><PERSON>" href="mailto:<EMAIL>">
		<link rel="help" href="http://dev.w3.org/fxtf/compositing-1/#mix-blend-mode">
		<meta name="flags" content="">
		<meta name="assert" content="Test checks that an element with mix-blend-mode and his child with opacity blends as a group(no blending between the element with mix-blend-mode and the child element)">
		<link rel="match" href="reference/mix-blend-mode-child-of-blended-has-opacity-ref.html">
		<style>
			.parent {
				opacity: 0.9;
				background: yellow;/*rgb(255,255,0);*/
				margin: 30px;
				width: 120px;
				height: 120px;
				display: inline-block;
			}
			.blended {
				width: 100px;
				height: 100px;
				background: fuchsia; /* rgb(255, 0, 255);*/
				display: inline-block;
				mix-blend-mode: difference;
				margin-top: 10px;
				margin-left: 10px;
			}
			.childBlended {
				background: red;/*rgb(255,0,0);*/
				width: 80px;
				height: 80px;
				margin-top: 10px;
				margin-left: 10px;
				opacity: 0.99;
			}
		</style>
	</head>
	<body>
 		<div class="parent">
			<div class="blended">
				<div class="childBlended"></div>
			</div>
		</div>
	</body>
</html>
