---
source: codespan-reporting/tests/term.rs
expression: TEST_DATA.emit_no_color(&config)
---
error[E0703]: invalid ABI: found `路濫狼á́́`
  ┌─ unicode.rs:1:8
  │
1 │ extern "路濫狼á́́" fn foo() {}
  │        ^^^^^^^^^ invalid ABI
  │
  = valid ABIs:
      - aapcs
      - amdgpu-kernel
      - C
      - cdecl
      - efiapi
      - fastcall
      - msp430-interrupt
      - platform-intrinsic
      - ptx-kernel
      - Rust
      - rust-call
      - rust-intrinsic
      - stdcall
      - system
      - sysv64
      - thiscall
      - unadjusted
      - vectorcall
      - win64
      - x86-interrupt

error: aborting due to previous error
 = For more information about this error, try `rustc --explain E0703`.


