<style>
input:invalid {}
</style>
<script>
function eventhandler1() {
try { htmlvar00005.select(); } catch(e) { }
try { htmlvar00009.appendChild(htmlvar00004); } catch(e) { }
try { htmlvar00013.innerText = ""; } catch(e) { }
}
function eventhandler2() {
try { htmlvar00011.addEventListener("DOMSubtreeModified", eventhandler1); } catch(e) { }
try { htmlvar00011.style.setProperty("text-transform", "full-width"); } catch(e) { }
}
</script>
<marquee id="htmlvar00004">
<input id="htmlvar00005" required="">
</marquee>
<form id="htmlvar00009">
<legend id="htmlvar00011">
<q id="htmlvar00013">
<image onload="eventhandler2()" src="data:;base64,R0lGODlhIAAAAAAAACH5BAAAAAAALAAAAAgACAAAAO">
