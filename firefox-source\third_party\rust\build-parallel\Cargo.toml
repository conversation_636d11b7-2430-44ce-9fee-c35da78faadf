# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies
#
# If you believe there's an error in this file please file an
# issue against the rust-lang/cargo repository. If you're
# editing this file be aware that the upstream Cargo.toml
# will likely look very different (and much more reasonable)

[package]
edition = "2018"
name = "build-parallel"
version = "0.1.2"
authors = ["<PERSON> <<EMAIL>>"]
description = "A helper library to let you parallelize work in build.rs\nusing the jobserver\n"
documentation = "https://doc.rs/build-parallel"
keywords = ["build-dependencies"]
categories = ["development-tools::build-utils"]
license = "MIT/Apache-2.0"
repository = "https://github.com/jrmuizel/build-parallel"
[dependencies.crossbeam-utils]
version = "0.8"

[dependencies.jobserver]
version = "0.1.19"

[dependencies.num_cpus]
version = "1.0"
