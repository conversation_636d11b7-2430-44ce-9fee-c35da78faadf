/* -*- Mode: C++; tab-width: 8; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* vim: set ts=8 sts=2 et sw=2 tw=80: */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

/* list of CSS pseudo-elements */

/*
 * This file contains the list of support CSS pseudo-elements and some flags.
 * It is designed to be used as inline input to nsCSSPseudoElements.cpp *only*
 * through the magic of C preprocessing.  All entries must be enclosed either
 * in the macro CSS_PSEUDO_ELEMENT; these macros will have cruel and unusual
 * things done to them.  The entries should be kept in some sort of logical
 * order.
 *
 * Code including this file MUST define CSS_PSEUDO_ELEMENT, which takes
 * three parameters:
 * name_  : The C++ identifier used for the atom (which will be a member
 *          of nsCSSPseudoElements)
 * value_ : The pseudo-element as a string, with single-colon syntax,
 *          used as the string value of the atom.
 * flags_ : A bitfield containing flags defined in nsCSSPseudoElements.h
 *
 * A corresponding atom must also be defined in StaticAtoms.py with a name of
 * "PseudoElement_<name_>" and whose value matches the definition in this file.
 */

// OUTPUT_CLASS=nsCSSPseudoElements
// MACRO_NAME=CSS_PSEUDO_ELEMENT

CSS_PSEUDO_ELEMENT(after, ":after", CSS_PSEUDO_ELEMENT_IS_CSS2 |
                                    CSS_PSEUDO_ELEMENT_IS_FLEX_OR_GRID_ITEM)
CSS_PSEUDO_ELEMENT(before, ":before", CSS_PSEUDO_ELEMENT_IS_CSS2 |
                                      CSS_PSEUDO_ELEMENT_IS_FLEX_OR_GRID_ITEM)
CSS_PSEUDO_ELEMENT(marker, ":marker", 0)

CSS_PSEUDO_ELEMENT(backdrop, ":backdrop", 0)

CSS_PSEUDO_ELEMENT(cue, ":cue", CSS_PSEUDO_ELEMENT_IS_JS_CREATED_NAC)

CSS_PSEUDO_ELEMENT(firstLetter, ":first-letter", CSS_PSEUDO_ELEMENT_IS_CSS2)
CSS_PSEUDO_ELEMENT(firstLine, ":first-line", CSS_PSEUDO_ELEMENT_IS_CSS2)

CSS_PSEUDO_ELEMENT(highlight, ":highlight", 0)
CSS_PSEUDO_ELEMENT(selection, ":selection", 0)
CSS_PSEUDO_ELEMENT(targetText, ":target-text", 0)

CSS_PSEUDO_ELEMENT(viewTransition, ":view-transition",
                   CSS_PSEUDO_ELEMENT_ENABLED_IN_UA_SHEETS)
CSS_PSEUDO_ELEMENT(viewTransitionGroup, ":view-transition-group",
                   CSS_PSEUDO_ELEMENT_ENABLED_IN_UA_SHEETS)
CSS_PSEUDO_ELEMENT(viewTransitionImagePair, ":view-transition-image-pair",
                   CSS_PSEUDO_ELEMENT_ENABLED_IN_UA_SHEETS)
CSS_PSEUDO_ELEMENT(viewTransitionOld, ":view-transition-old",
                   CSS_PSEUDO_ELEMENT_ENABLED_IN_UA_SHEETS)
CSS_PSEUDO_ELEMENT(viewTransitionNew, ":view-transition-new",
                   CSS_PSEUDO_ELEMENT_ENABLED_IN_UA_SHEETS)
// The internal implementation usage for View transition to create the snapshot
// containing block concept.
CSS_PSEUDO_ELEMENT(mozSnapshotContainingBlock,
                   ":-moz-snapshot-containing-block",
                   CSS_PSEUDO_ELEMENT_ENABLED_IN_UA_SHEETS)

// HTML5 Forms pseudo elements
CSS_PSEUDO_ELEMENT(mozNumberSpinBox, ":-moz-number-spin-box",
                   CSS_PSEUDO_ELEMENT_SUPPORTS_USER_ACTION_STATE |
                   CSS_PSEUDO_ELEMENT_ENABLED_IN_UA_SHEETS_AND_CHROME)
CSS_PSEUDO_ELEMENT(mozNumberSpinUp, ":-moz-number-spin-up",
                   CSS_PSEUDO_ELEMENT_SUPPORTS_USER_ACTION_STATE |
                   CSS_PSEUDO_ELEMENT_ENABLED_IN_UA_SHEETS_AND_CHROME)
CSS_PSEUDO_ELEMENT(mozNumberSpinDown, ":-moz-number-spin-down",
                   CSS_PSEUDO_ELEMENT_SUPPORTS_USER_ACTION_STATE |
                   CSS_PSEUDO_ELEMENT_ENABLED_IN_UA_SHEETS_AND_CHROME)
CSS_PSEUDO_ELEMENT(mozSearchClearButton, ":-moz-search-clear-button",
                   CSS_PSEUDO_ELEMENT_SUPPORTS_USER_ACTION_STATE |
                   CSS_PSEUDO_ELEMENT_ENABLED_IN_UA_SHEETS_AND_CHROME)
CSS_PSEUDO_ELEMENT(mozProgressBar, ":-moz-progress-bar",
                   CSS_PSEUDO_ELEMENT_SUPPORTS_USER_ACTION_STATE)
CSS_PSEUDO_ELEMENT(mozRangeTrack, ":-moz-range-track",
                   CSS_PSEUDO_ELEMENT_SUPPORTS_USER_ACTION_STATE)
CSS_PSEUDO_ELEMENT(mozRangeProgress, ":-moz-range-progress",
                   CSS_PSEUDO_ELEMENT_SUPPORTS_USER_ACTION_STATE)
CSS_PSEUDO_ELEMENT(mozRangeThumb, ":-moz-range-thumb",
                   CSS_PSEUDO_ELEMENT_SUPPORTS_USER_ACTION_STATE)
CSS_PSEUDO_ELEMENT(mozMeterBar, ":-moz-meter-bar",
                   CSS_PSEUDO_ELEMENT_SUPPORTS_USER_ACTION_STATE)
CSS_PSEUDO_ELEMENT(placeholder, ":placeholder",
                   CSS_PSEUDO_ELEMENT_SUPPORTS_USER_ACTION_STATE)
CSS_PSEUDO_ELEMENT(mozColorSwatch, ":-moz-color-swatch",
                   CSS_PSEUDO_ELEMENT_SUPPORTS_USER_ACTION_STATE)
// The root of the text value anonymous content inside an <input> or <textarea>.
CSS_PSEUDO_ELEMENT(mozTextControlEditingRoot, ":-moz-text-control-editing-root",
                   CSS_PSEUDO_ELEMENT_ENABLED_IN_UA_SHEETS)
// The element that shows the autofill value.
CSS_PSEUDO_ELEMENT(mozTextControlPreview, ":-moz-text-control-preview",
                   CSS_PSEUDO_ELEMENT_ENABLED_IN_UA_SHEETS)
// The Reveal Password button for <input type=password>.
CSS_PSEUDO_ELEMENT(mozReveal, ":-moz-reveal",
                   CSS_PSEUDO_ELEMENT_ENABLED_IN_UA_SHEETS)

// The button in an <input type=file>
CSS_PSEUDO_ELEMENT(fileSelectorButton, ":file-selector-button",
                   CSS_PSEUDO_ELEMENT_SUPPORTS_USER_ACTION_STATE)

// Standard progress/meter/range pseudo-elements.
CSS_PSEUDO_ELEMENT(sliderTrack, ":slider-track",
                   CSS_PSEUDO_ELEMENT_SUPPORTS_USER_ACTION_STATE |
                   CSS_PSEUDO_ELEMENT_ENABLED_IN_UA_SHEETS_AND_CHROME)
CSS_PSEUDO_ELEMENT(sliderThumb, ":slider-thumb",
                   CSS_PSEUDO_ELEMENT_SUPPORTS_USER_ACTION_STATE |
                   CSS_PSEUDO_ELEMENT_ENABLED_IN_UA_SHEETS_AND_CHROME)
CSS_PSEUDO_ELEMENT(sliderFill, ":slider-fill",
                   CSS_PSEUDO_ELEMENT_SUPPORTS_USER_ACTION_STATE |
                   CSS_PSEUDO_ELEMENT_ENABLED_IN_UA_SHEETS_AND_CHROME)

// The content in a <details> element that is shown when the element is open.
CSS_PSEUDO_ELEMENT(detailsContent, ":details-content",
                   CSS_PSEUDO_ELEMENT_SUPPORTS_USER_ACTION_STATE |
                   CSS_PSEUDO_ELEMENT_ENABLED_IN_UA_SHEETS_AND_CHROME)
