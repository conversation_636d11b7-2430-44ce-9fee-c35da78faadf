/**
 * Megist<PERSON> Browser - Preferences JavaScript
 * Handles the preferences dialog functionality
 */

const { Services } = ChromeUtils.import("resource://gre/modules/Services.jsm");

var MegistoPreferences = {
  
  /**
   * Initialize preferences dialog
   */
  init: function() {
    console.log("Megisto Preferences: Initializing...");
    
    // Load current statistics
    this.loadStatistics();
    
    // Set up event listeners
    this.setupEventListeners();
    
    console.log("Megisto Preferences: Initialized");
  },
  
  /**
   * Handle dialog accept (OK button)
   */
  onAccept: function() {
    console.log("Megisto Preferences: Saving preferences...");
    
    try {
      // Save all preferences
      this.savePreferences();
      
      // Notify extension of preference changes
      this.notifyPreferenceChanges();
      
      return true;
    } catch (error) {
      console.error("Megisto Preferences: Failed to save preferences:", error);
      return false;
    }
  },
  
  /**
   * Handle dialog cancel
   */
  onCancel: function() {
    console.log("Megisto Preferences: Cancelled");
    return true;
  },
  
  /**
   * Save all preferences
   */
  savePreferences: function() {
    // Preferences are automatically saved by the prefwindow
    // This method can be used for additional validation or processing
    
    const cookieBlockingEnabled = document.getElementById("enableCookieBlocking").checked;
    const youtubeEnhancementEnabled = document.getElementById("enableYouTubeEnhancement").checked;
    
    console.log("Megisto Preferences: Cookie blocking enabled:", cookieBlockingEnabled);
    console.log("Megisto Preferences: YouTube enhancement enabled:", youtubeEnhancementEnabled);
  },
  
  /**
   * Notify the extension about preference changes
   */
  notifyPreferenceChanges: function() {
    try {
      // Send message to background script
      const message = {
        type: 'PREFERENCES_UPDATED',
        preferences: this.getCurrentPreferences()
      };
      
      // Use the WebExtension messaging API
      browser.runtime.sendMessage(message);
      
    } catch (error) {
      console.error("Megisto Preferences: Failed to notify extension:", error);
    }
  },
  
  /**
   * Get current preferences as object
   */
  getCurrentPreferences: function() {
    return {
      cookieBlocking: {
        enabled: document.getElementById("enableCookieBlocking").checked,
        aggressive: document.getElementById("aggressiveBlocking").checked,
        whitelist: document.getElementById("whitelistTextbox").value
      },
      youtube: {
        enhancementEnabled: document.getElementById("enableYouTubeEnhancement").checked,
        autoplay: document.getElementById("youtubeAutoplayCheckbox").checked,
        defaultQuality: document.getElementById("qualityMenulist").value,
        blockRelated: document.getElementById("blockRelatedVideos").checked
      }
    };
  },
  
  /**
   * Load and display statistics
   */
  loadStatistics: function() {
    try {
      // Load statistics from storage
      const blockedToday = Services.prefs.getIntPref("megisto.stats.blockedToday", 0);
      const blockedTotal = Services.prefs.getIntPref("megisto.stats.blockedTotal", 0);
      
      document.getElementById("blockedTodayCount").value = blockedToday;
      document.getElementById("blockedTotalCount").value = blockedTotal;
      
    } catch (error) {
      console.error("Megisto Preferences: Failed to load statistics:", error);
    }
  },
  
  /**
   * Set up event listeners
   */
  setupEventListeners: function() {
    // Enable/disable dependent controls
    const cookieBlockingCheckbox = document.getElementById("enableCookieBlocking");
    const youtubeEnhancementCheckbox = document.getElementById("enableYouTubeEnhancement");
    
    cookieBlockingCheckbox.addEventListener("command", () => {
      this.updateCookieBlockingControls();
    });
    
    youtubeEnhancementCheckbox.addEventListener("command", () => {
      this.updateYouTubeControls();
    });
    
    // Initial state
    this.updateCookieBlockingControls();
    this.updateYouTubeControls();
  },
  
  /**
   * Update cookie blocking control states
   */
  updateCookieBlockingControls: function() {
    const enabled = document.getElementById("enableCookieBlocking").checked;
    const controls = [
      "aggressiveBlocking",
      "whitelistTextbox"
    ];
    
    controls.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.disabled = !enabled;
      }
    });
  },
  
  /**
   * Update YouTube control states
   */
  updateYouTubeControls: function() {
    const enabled = document.getElementById("enableYouTubeEnhancement").checked;
    const controls = [
      "youtubeAutoplayCheckbox",
      "qualityMenulist",
      "blockRelatedVideos",
      "customControls"
    ];
    
    controls.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.disabled = !enabled;
      }
    });
  },
  
  /**
   * Update blocking rules
   */
  updateBlockingRules: function() {
    console.log("Megisto Preferences: Updating blocking rules...");
    
    try {
      // Send message to background script to update rules
      const message = {
        type: 'UPDATE_BLOCKING_RULES'
      };
      
      browser.runtime.sendMessage(message);
      
      // Show confirmation
      Services.prompt.alert(
        window,
        "Megisto Browser",
        "Blocking rules updated successfully."
      );
      
    } catch (error) {
      console.error("Megisto Preferences: Failed to update rules:", error);
      Services.prompt.alert(
        window,
        "Megisto Browser",
        "Failed to update blocking rules: " + error.message
      );
    }
  },
  
  /**
   * Reset blocking rules to defaults
   */
  resetBlockingRules: function() {
    const confirmed = Services.prompt.confirm(
      window,
      "Megisto Browser",
      "Are you sure you want to reset all blocking rules to defaults? This will remove any custom rules you have added."
    );
    
    if (confirmed) {
      try {
        // Send message to background script to reset rules
        const message = {
          type: 'RESET_BLOCKING_RULES'
        };
        
        browser.runtime.sendMessage(message);
        
        // Clear whitelist
        document.getElementById("whitelistTextbox").value = "";
        
        Services.prompt.alert(
          window,
          "Megisto Browser",
          "Blocking rules reset to defaults."
        );
        
      } catch (error) {
        console.error("Megisto Preferences: Failed to reset rules:", error);
      }
    }
  },
  
  /**
   * Clear statistics
   */
  clearStatistics: function() {
    const confirmed = Services.prompt.confirm(
      window,
      "Megisto Browser",
      "Are you sure you want to clear all blocking statistics?"
    );
    
    if (confirmed) {
      try {
        Services.prefs.setIntPref("megisto.stats.blockedToday", 0);
        Services.prefs.setIntPref("megisto.stats.blockedTotal", 0);
        
        this.loadStatistics();
        
      } catch (error) {
        console.error("Megisto Preferences: Failed to clear statistics:", error);
      }
    }
  },
  
  /**
   * Test video player
   */
  testVideoPlayer: function() {
    console.log("Megisto Preferences: Testing video player...");
    
    // Open a test page with YouTube embed
    const testUrl = "data:text/html,<html><body><h1>Video Player Test</h1><iframe width='560' height='315' src='https://www.youtube.com/embed/dQw4w9WgXcQ' frameborder='0' allowfullscreen></iframe></body></html>";
    
    Services.wm.getMostRecentWindow("navigator:browser").gBrowser.addTab(testUrl);
  },
  
  /**
   * Reset video player settings
   */
  resetVideoPlayer: function() {
    const confirmed = Services.prompt.confirm(
      window,
      "Megisto Browser",
      "Reset video player settings to defaults?"
    );
    
    if (confirmed) {
      document.getElementById("youtubeAutoplayCheckbox").checked = false;
      document.getElementById("qualityMenulist").value = "720p";
      document.getElementById("blockRelatedVideos").checked = true;
    }
  },
  
  /**
   * Check for updates
   */
  checkForUpdates: function() {
    console.log("Megisto Preferences: Checking for updates...");
    
    Services.prompt.alert(
      window,
      "Megisto Browser",
      "Update checking is not yet implemented. Please check the project repository for updates."
    );
  },
  
  /**
   * Show update history
   */
  showUpdateHistory: function() {
    console.log("Megisto Preferences: Showing update history...");
    
    Services.prompt.alert(
      window,
      "Megisto Browser",
      "Update history is not yet implemented."
    );
  },
  
  /**
   * Open homepage
   */
  openHomepage: function() {
    const url = "https://github.com/megisto/megisto-browser";
    Services.wm.getMostRecentWindow("navigator:browser").gBrowser.addTab(url);
  },
  
  /**
   * Open support page
   */
  openSupport: function() {
    const url = "https://github.com/megisto/megisto-browser/issues";
    Services.wm.getMostRecentWindow("navigator:browser").gBrowser.addTab(url);
  }
};

// Initialize when the dialog loads
window.addEventListener("load", () => {
  MegistoPreferences.init();
});
