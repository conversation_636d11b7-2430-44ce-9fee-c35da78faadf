# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this file,
# You can obtain one at http://mozilla.org/MPL/2.0/.

import runpy


def generate(output, dataFile):
    output.write(
        """/* THIS IS AN AUTOGENERATED FILE.  DO NOT EDIT */
#ifndef COMPOSITOR_ANIMATABLE_PROPERTY_LIST
#define COMPOSITOR_ANIMATABLE_PROPERTY_LIST { \\
"""
    )

    def can_animate_on_compositor(p):
        return "CanAnimateOnCompositor" in p.flags and p.type() != "alias"

    properties = runpy.run_path(dataFile)["data"]
    properties = filter(can_animate_on_compositor, properties.values())

    count = 0
    for p in properties:
        output.write("  eCSSProperty_{}, \\\n".format(p.id))
        count += 1

    output.write("}\n")
    output.write("#endif /* COMPOSITOR_ANIMATABLE_PROPERTY_LIST */\n")

    output.write("\n")
    output.write("#ifndef COMPOSITOR_ANIMATABLE_PROPERTY_LIST_LENGTH\n")
    output.write(
        "#define COMPOSITOR_ANIMATABLE_PROPERTY_LIST_LENGTH {}\n".format(count)
    )
    output.write("#endif /* COMPOSITOR_ANIMATABLE_PROPERTY_LIST_LENGTH */\n")
