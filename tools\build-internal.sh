﻿#!/bin/bash
set -e

echo "=== Megisto Browser Build ==="
echo "Starting build process..."

# Change to Firefox source directory
cd "E:/megisto/firefox-source"

# Set environment variables
export MOZCONFIG="E:/megisto/mozconfig"
export MOZ_OBJDIR="E:/megisto/firefox-source/obj-megisto"

echo "Working directory: \E:\megisto"
echo "MOZCONFIG: \"
echo "MOZ_OBJDIR: \"

echo "MOZCONFIG: $MOZCONFIG"
echo "MOZ_OBJDIR: $MOZ_OBJDIR"

# Check if we're in the right directory
if [ ! -f "mach" ]; then
    echo "Error: mach not found. Are we in the Firefox source directory?"
    echo "Current directory: \E:\megisto"
    exit 1
fi

# Bootstrap if needed (first time build)
if [ ! -f "$MOZ_OBJDIR/config.status" ]; then
    echo "Bootstrapping build environment..."
    ./mach bootstrap --application-choice browser --no-interactive
fi

# Configure build
echo "Configuring build..."
./mach configure

# Build Megisto Browser
echo "Building Megisto Browser..."
if [ "False" = "True" ]; then
    ./mach build -v
else
    ./mach build
fi

echo "Build completed successfully!"

# Package if requested
if [ "False" = "True" ]; then
    echo "Creating package..."
    ./mach package
    
    # Copy package to dist directory
    if [ -d "$MOZ_OBJDIR/dist" ]; then
        cp -r "$MOZ_OBJDIR/dist/"* "E:/megisto/dist/"
        echo "Package copied to dist directory."
    fi
fi

echo "=== Build Complete ==="
