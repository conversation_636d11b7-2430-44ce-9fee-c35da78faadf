# Megisto Browser - Build Script
# This script builds Megisto Browser using the Firefox source

param(
    [switch]$Clean,        # Clean build (remove obj directory)
    [switch]$Package,      # Create installer package
    [switch]$Debug,        # Build debug version
    [switch]$Verbose,      # Verbose output
    [string]$Target = "browser"  # Build target
)

$ErrorActionPreference = "Stop"

Write-Host "=== Megisto Browser Build Script ===" -ForegroundColor Green

$rootDir = Get-Location
$firefoxDir = Join-Path $rootDir "firefox-source"
$objDir = Join-Path $firefoxDir "obj-megisto"
$distDir = Join-Path $rootDir "dist"
$logDir = Join-Path $rootDir "logs"

# Ensure directories exist
@($distDir, $logDir) | ForEach-Object {
    if (-not (Test-Path $_)) {
        New-Item -ItemType Directory -Path $_ -Force | Out-Null
    }
}

# Check if Firefox source exists
if (-not (Test-Path $firefoxDir)) {
    Write-Error "Firefox source not found at $firefoxDir. Please run .\tools\clone-firefox.ps1 first."
    exit 1
}

# Check if MozillaBuild is available
$mozillaBuildPath = "C:\mozilla-build"
if (-not (Test-Path $mozillaBuildPath)) {
    Write-Error "MozillaBuild not found at $mozillaBuildPath. Please install MozillaBuild first."
    exit 1
}

# Find the correct bash executable
$bashPaths = @(
    "C:\mozilla-build\msys\bin\bash.exe",
    "C:\mozilla-build\msys2\usr\bin\bash.exe",
    "C:\mozilla-build\bin\bash.exe"
)

$bashExe = $null
foreach ($path in $bashPaths) {
    if (Test-Path $path) {
        $bashExe = $path
        break
    }
}

if (-not $bashExe) {
    Write-Error "Could not find bash.exe in MozillaBuild installation. Please check your MozillaBuild installation."
    exit 1
}

Write-Host "Using bash at: $bashExe" -ForegroundColor Green

# Clean build if requested
if ($Clean -and (Test-Path $objDir)) {
    Write-Host "Cleaning previous build..." -ForegroundColor Yellow
    Remove-Item $objDir -Recurse -Force
    Write-Host "Clean completed." -ForegroundColor Green
}

# Set up environment variables
$env:MOZCONFIG = Join-Path $rootDir "mozconfig"
$env:MOZ_OBJDIR = $objDir

Write-Host "Using mozconfig: $env:MOZCONFIG" -ForegroundColor Yellow
Write-Host "Build directory: $env:MOZ_OBJDIR" -ForegroundColor Yellow

# Update mozconfig for debug build if requested
if ($Debug) {
    Write-Host "Configuring for debug build..." -ForegroundColor Yellow
    
    $debugMozconfig = @"
# Megisto Browser Debug Build Configuration

# Build Firefox with Megisto branding
ac_add_options --enable-application=browser

# Debug settings
ac_add_options --enable-debug
ac_add_options --enable-debug-symbols
ac_add_options --disable-optimize

# Megisto-specific options
ac_add_options --with-app-name=megisto
ac_add_options --with-app-basename=Megisto
ac_add_options --with-branding=browser/branding/megisto

# Enable system add-on support
ac_add_options --enable-extensions

# Disable telemetry and data collection
ac_add_options --disable-telemetry
ac_add_options --disable-crashreporter
ac_add_options --disable-updater

# Windows-specific options
ac_add_options --target=x86_64-pc-mingw32
ac_add_options --host=x86_64-pc-mingw32

# Output directory
mk_add_options MOZ_OBJDIR=@TOPSRCDIR@/obj-megisto
"@
    
    $debugMozconfig | Out-File -FilePath $env:MOZCONFIG -Encoding UTF8
}

# Create build script for MozillaBuild environment
$buildScript = @"
#!/bin/bash
set -e

echo "=== Megisto Browser Build ==="
echo "Starting build process..."

# Change to Firefox source directory
cd "$($firefoxDir -replace '\\', '/')"

# Set environment variables
export MOZCONFIG="$($env:MOZCONFIG -replace '\\', '/')"
export MOZ_OBJDIR="$($objDir -replace '\\', '/')"

echo "Working directory: \$(pwd)"
echo "MOZCONFIG: \$MOZCONFIG"
echo "MOZ_OBJDIR: \$MOZ_OBJDIR"

echo "MOZCONFIG: `$MOZCONFIG"
echo "MOZ_OBJDIR: `$MOZ_OBJDIR"

# Check if we're in the right directory
if [ ! -f "mach" ]; then
    echo "Error: mach not found. Are we in the Firefox source directory?"
    echo "Current directory: \$(pwd)"
    exit 1
fi

# Bootstrap if needed (first time build)
if [ ! -f "`$MOZ_OBJDIR/config.status" ]; then
    echo "Bootstrapping build environment..."
    ./mach bootstrap --application-choice browser --no-interactive
fi

# Configure build
echo "Configuring build..."
./mach configure

# Build Megisto Browser
echo "Building Megisto Browser..."
if [ "$Verbose" = "True" ]; then
    ./mach build -v
else
    ./mach build
fi

echo "Build completed successfully!"

# Package if requested
if [ "$Package" = "True" ]; then
    echo "Creating package..."
    ./mach package
    
    # Copy package to dist directory
    if [ -d "`$MOZ_OBJDIR/dist" ]; then
        cp -r "`$MOZ_OBJDIR/dist/"* "$($distDir -replace '\\', '/')/"
        echo "Package copied to dist directory."
    fi
fi

echo "=== Build Complete ==="
"@

$buildScriptPath = Join-Path $rootDir "tools\build-internal.sh"
$buildScript | Out-File -FilePath $buildScriptPath -Encoding UTF8

# Create batch file to run the build in MozillaBuild
$batchScript = @"
@echo off
echo Starting Megisto Browser build...

set MOZCONFIG=$env:MOZCONFIG
set MOZ_OBJDIR=$env:MOZ_OBJDIR
set Verbose=$Verbose
set Package=$Package

REM Change to project directory
cd /d "$rootDir"

REM Use MozillaBuild environment
call "C:\mozilla-build\start-shell.bat" /c "cd '$($rootDir -replace '\\', '/')' && bash tools/build-internal.sh"

if %ERRORLEVEL% NEQ 0 (
    echo Build failed with error code %ERRORLEVEL%
    pause
    exit /b %ERRORLEVEL%
)

echo Build completed successfully!
"@

$batchScriptPath = Join-Path $rootDir "tools\build-runner.bat"
$batchScript | Out-File -FilePath $batchScriptPath -Encoding ASCII

# Run the build
Write-Host "Starting build process..." -ForegroundColor Yellow
Write-Host "This may take 30-60 minutes depending on your system..." -ForegroundColor Yellow

$logFile = Join-Path $logDir "build-$(Get-Date -Format 'yyyyMMdd-HHmmss').log"

try {
    # Use a simpler approach that doesn't redirect output
    Write-Host "Running build command: $batchScriptPath" -ForegroundColor Cyan
    Write-Host "Build output will be shown in real-time..." -ForegroundColor Yellow

    $process = Start-Process -FilePath $batchScriptPath -Wait -PassThru -NoNewWindow

    if ($process.ExitCode -eq 0) {
        Write-Host "Build completed successfully!" -ForegroundColor Green

        # Show build artifacts
        if (Test-Path $objDir) {
            $artifacts = Get-ChildItem -Path (Join-Path $objDir "dist") -ErrorAction SilentlyContinue
            if ($artifacts) {
                Write-Host "`nBuild artifacts:" -ForegroundColor Yellow
                $artifacts | ForEach-Object {
                    Write-Host "  $($_.Name)" -ForegroundColor White
                }
            }
        }

        if ($Package -and (Test-Path $distDir)) {
            Write-Host "`nPackage created in: $distDir" -ForegroundColor Green
        }

    } else {
        Write-Error "Build failed with exit code $($process.ExitCode). Check the output above for details."
    }
}
catch {
    Write-Error "Build process failed: $($_.Exception.Message)"
}

# Clean up temporary files
Remove-Item $buildScriptPath -Force -ErrorAction SilentlyContinue
Remove-Item $batchScriptPath -Force -ErrorAction SilentlyContinue

Write-Host "`nBuild process completed. Check output above for any issues." -ForegroundColor Yellow

if ($process.ExitCode -eq 0) {
    Write-Host "`n=== Next Steps ===" -ForegroundColor Green
    Write-Host "1. Test the browser: $objDir\dist\bin\megisto.exe" -ForegroundColor White
    Write-Host "2. Run tests: .\tools\run-tests.ps1" -ForegroundColor White
    Write-Host "3. Create installer: .\tools\create-installer.ps1" -ForegroundColor White
}
