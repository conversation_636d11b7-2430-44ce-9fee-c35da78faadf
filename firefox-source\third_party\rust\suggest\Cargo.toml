# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
name = "suggest"
version = "0.1.0"
build = false
exclude = [
    "/android",
    "/ios",
]
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "Manages sponsored and web suggestions for Firefox Suggest"
readme = "README.md"
license = "MPL-2.0"

[features]
benchmark_api = [
    "tempfile",
    "viaduct-reqwest",
]

[lib]
name = "suggest"
path = "src/lib.rs"
bench = false

[[bin]]
name = "debug_ingestion_sizes"
path = "src/bin/debug_ingestion_sizes.rs"
bench = false
required-features = ["benchmark_api"]

[[bench]]
name = "benchmark_all"
path = "benches/benchmark_all.rs"
harness = false
required-features = ["benchmark_api"]

[dependencies]
anyhow = "1.0"
chrono = "0.4"
extend = "1.1"
once_cell = "1.5"
parking_lot = ">=0.11,<=0.12"
rmp-serde = "1.3"
serde_json = "1"
thiserror = "1"
unicase = "2.6"
unicode-normalization = "0.1"

[dependencies.error-support]
path = "../support/error"

[dependencies.interrupt-support]
path = "../support/interrupt"

[dependencies.remote_settings]
path = "../remote_settings"

[dependencies.rusqlite]
version = "0.33.0"
features = [
    "functions",
    "bundled",
    "load_extension",
    "collation",
]

[dependencies.serde]
version = "1"
features = ["derive"]

[dependencies.sql-support]
path = "../support/sql"

[dependencies.tempfile]
version = "3.2.0"
optional = true

[dependencies.uniffi]
version = "0.29.0"

[dependencies.url]
version = "2.1"
features = ["serde"]

[dependencies.viaduct]
path = "../viaduct"

[dependencies.viaduct-reqwest]
path = "../support/viaduct-reqwest"
optional = true

[dev-dependencies]
criterion = "0.5"
expect-test = "1.4"
hex = "0.4"
itertools = "0.14"

[dev-dependencies.error-support]
path = "../support/error"
features = ["testing"]

[dev-dependencies.rc_crypto]
path = "../support/rc_crypto"

[build-dependencies.uniffi]
version = "0.29.0"
features = ["build"]
