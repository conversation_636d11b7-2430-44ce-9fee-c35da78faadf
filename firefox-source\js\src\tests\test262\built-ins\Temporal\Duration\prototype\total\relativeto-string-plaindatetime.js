// |reftest| shell-option(--enable-temporal) skip-if(!this.hasOwnProperty('Temporal')||!xulRuntime.shell) -- Temporal is not enabled unconditionally, requires shell-options
// Copyright (C) 2021 Igalia, S.L. All rights reserved.
// This code is governed by the BSD license found in the LICENSE file.

/*---
esid: sec-temporal.duration.prototype.total
description: The relativeTo option accepts a PlainDateTime-like ISO 8601 string
features: [Temporal]
---*/

['2000-01-01', '2000-01-01T00:00', '2000-01-01T00:00[u-ca=iso8601]'].forEach((relativeTo) => {
  const duration = new Temporal.Duration(0, 0, 0, 31);
  const result = duration.total({ unit: "months", relativeTo });
  assert.sameValue(result, 1);
});

reportCompare(0, 0);
