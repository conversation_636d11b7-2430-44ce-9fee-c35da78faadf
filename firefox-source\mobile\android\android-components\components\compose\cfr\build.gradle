/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

plugins {
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
}

apply plugin: 'com.android.library'

android {
    defaultConfig {
        minSdkVersion = config.minSdkVersion
        compileSdk = config.compileSdkVersion
        targetSdkVersion = config.targetSdkVersion
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled = false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    buildFeatures {
        compose = true
    }

    namespace = 'mozilla.components.compose.cfr'
}

dependencies {
    implementation platform(libs.androidx.compose.bom)
    implementation project(':components:support-ktx')
    implementation project(':components:ui-icons')

    implementation libs.androidx.compose.ui
    implementation libs.androidx.compose.ui.tooling.preview
    implementation libs.androidx.compose.foundation
    implementation libs.androidx.compose.material3
    implementation libs.androidx.core
    implementation libs.androidx.core.ktx
    implementation libs.androidx.lifecycle.runtime
    implementation libs.androidx.savedstate

    debugImplementation libs.androidx.compose.ui.tooling

    testImplementation project(':components:support-test')
    testImplementation libs.testing.coroutines
    testImplementation libs.androidx.test.core
    testImplementation libs.androidx.test.junit
    testImplementation platform(libs.junit.bom)
    testImplementation libs.junit4
    testRuntimeOnly libs.junit.vintage
    testRuntimeOnly libs.junit.platform.launcher
    testImplementation libs.testing.robolectric
}

apply from: '../../../android-lint.gradle'
apply from: '../../../publish.gradle'
ext.configurePublish(config.componentsGroupId, project.name, project.ext.description)
