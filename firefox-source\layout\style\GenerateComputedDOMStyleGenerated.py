# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this file,
# You can obtain one at http://mozilla.org/MPL/2.0/.

import runpy

FILE = """/* THIS IS AN AUTOGENERATED FILE.  DO NOT EDIT */

/* processed file that defines entries for nsComputedDOMStyle, designed
   to be #included in nsComputedDOMStyle.cpp */

static constexpr size_t kEntryIndices[eCSSProperty_COUNT] = {{
  {indices}
}};

{asserts}

static constexpr Entry kEntries[eCSSProperty_COUNT] = {{
  {entries}
}};
"""


def generate(output, dataFile):
    def order_key(p):
        # Put prefixed properties after normal properties.
        # The spec is unclear about this, and Blink doesn't have any sensible
        # order at all, so it probably doesn't matter a lot. But originally
        # Gecko put then later so we do so as well. See w3c/csswg-drafts#2827.
        order = p.name.startswith("-")
        return (order, p.name)

    def has_cpp_getter(p):
        if not "ExposedOnGetCS" in p.flags:
            return False
        if "SerializedByServo" in p.flags:
            return False
        if p.type() == "longhand" and "IsLogical" in p.flags:
            return False
        return True

    def getter_entry(p):
        if has_cpp_getter(p):
            return "DoGet" + p.method
        # Put a dummy getter here instead of nullptr because MSVC seems
        # to have bug which ruins the table when we put nullptr for
        # pointer-to-member-function. See bug 1471426.
        return "DummyGetter"

    properties = runpy.run_path(dataFile)["data"]

    entries = []
    indices = []
    asserts = []
    index_map = {}
    non_aliases = list(filter(lambda p: p.type() != "alias", properties.values()))
    for i, p in enumerate(sorted(non_aliases, key=order_key)):
        can_be_exposed = "true" if "ExposedOnGetCS" in p.flags else "false"
        entries.append(
            "{{ eCSSProperty_{}, {}, &nsComputedDOMStyle::{}}}".format(
                p.id, can_be_exposed, getter_entry(p)
            )
        )
        index_map[p.id] = i
        i += 1

    for i, p in enumerate(non_aliases):
        indices.append(str(index_map[p.id]))
        asserts.append(
            'static_assert(size_t(eCSSProperty_{}) == {}, "");'.format(p.id, i)
        )

    assert len(indices) == len(entries)

    output.write(
        FILE.format(
            indices=", ".join(indices),
            entries=",\n    ".join(entries),
            asserts="\n".join(asserts),
        )
    )
