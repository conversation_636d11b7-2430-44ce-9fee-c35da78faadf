/**
 * <PERSON><PERSON><PERSON>er - Advanced Rule Processor
 * Handles complex rule processing for cookie banner blocking
 */

class RuleProcessor {
  constructor() {
    this.rules = new Map();
    this.executionQueue = [];
    this.isProcessing = false;
  }
  
  /**
   * Load rules from the rules engine
   */
  async loadRules(rulesData) {
    if (!rulesData || !rulesData.rules) {
      console.warn('Rule Processor: No rules data provided');
      return;
    }
    
    this.rules.clear();
    
    rulesData.rules.forEach(rule => {
      // Support wildcard and regex patterns
      const domain = rule.domain;
      if (domain === '*') {
        this.rules.set('*', rule);
      } else if (domain.includes('*')) {
        // Convert wildcard to regex
        const regexPattern = domain.replace(/\*/g, '.*');
        this.rules.set(new RegExp(regexPattern), rule);
      } else {
        this.rules.set(domain, rule);
      }
    });
    
    console.log(`Rule Processor: Loaded ${this.rules.size} rules`);
  }
  
  /**
   * Get applicable rule for current domain
   */
  getRuleForDomain(hostname) {
    // Exact match first
    if (this.rules.has(hostname)) {
      return this.rules.get(hostname);
    }
    
    // Check parent domains
    const parts = hostname.split('.');
    for (let i = 1; i < parts.length; i++) {
      const parentDomain = parts.slice(i).join('.');
      if (this.rules.has(parentDomain)) {
        return this.rules.get(parentDomain);
      }
    }
    
    // Check regex patterns
    for (const [pattern, rule] of this.rules.entries()) {
      if (pattern instanceof RegExp && pattern.test(hostname)) {
        return rule;
      }
    }
    
    // Fallback to wildcard rule
    return this.rules.get('*') || null;
  }
  
  /**
   * Process rule actions for elements
   */
  async processRule(rule, elements) {
    if (!rule || !rule.actions || !elements.length) {
      return;
    }
    
    console.log('Rule Processor: Processing rule for', rule.domain, 'with', elements.length, 'elements');
    
    for (const element of elements) {
      await this.executeActions(rule.actions, element);
    }
  }
  
  /**
   * Execute rule actions on an element
   */
  async executeActions(actions, context) {
    for (const action of actions) {
      try {
        await this.executeAction(action, context);
      } catch (error) {
        console.error('Rule Processor: Action execution failed:', action, error);
      }
    }
  }
  
  /**
   * Execute a single action
   */
  async executeAction(action, context) {
    const [actionType, selector] = action.split(':', 2);
    
    switch (actionType.toLowerCase()) {
      case 'click':
        await this.clickAction(selector, context);
        break;
        
      case 'remove':
        await this.removeAction(selector, context);
        break;
        
      case 'hide':
        await this.hideAction(selector, context);
        break;
        
      case 'wait':
        await this.waitAction(parseInt(selector) || 1000);
        break;
        
      case 'script':
        await this.scriptAction(selector, context);
        break;
        
      case 'attribute':
        await this.attributeAction(selector, context);
        break;
        
      default:
        console.warn('Rule Processor: Unknown action type:', actionType);
    }
  }
  
  /**
   * Click action implementation
   */
  async clickAction(selector, context) {
    const elements = this.findElements(selector, context);
    
    for (const element of elements) {
      if (element.offsetParent !== null) { // Element is visible
        console.log('Rule Processor: Clicking element:', element);
        
        // Try different click methods
        try {
          element.click();
        } catch (error) {
          // Fallback to dispatching click event
          const clickEvent = new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window
          });
          element.dispatchEvent(clickEvent);
        }
        
        // Wait a bit after clicking
        await this.waitAction(500);
        break; // Only click the first visible element
      }
    }
  }
  
  /**
   * Remove action implementation
   */
  async removeAction(selector, context) {
    const elements = this.findElements(selector, context);
    
    elements.forEach(element => {
      console.log('Rule Processor: Removing element:', element);
      element.remove();
    });
  }
  
  /**
   * Hide action implementation
   */
  async hideAction(selector, context) {
    const elements = this.findElements(selector, context);
    
    elements.forEach(element => {
      console.log('Rule Processor: Hiding element:', element);
      element.style.display = 'none';
      element.style.visibility = 'hidden';
      element.style.opacity = '0';
    });
  }
  
  /**
   * Wait action implementation
   */
  async waitAction(milliseconds) {
    console.log('Rule Processor: Waiting', milliseconds, 'ms');
    return new Promise(resolve => setTimeout(resolve, milliseconds));
  }
  
  /**
   * Script action implementation
   */
  async scriptAction(scriptCode, context) {
    try {
      console.log('Rule Processor: Executing script:', scriptCode);
      
      // Create a safe execution context
      const func = new Function('element', 'document', 'window', scriptCode);
      func.call(null, context, document, window);
      
    } catch (error) {
      console.error('Rule Processor: Script execution failed:', error);
    }
  }
  
  /**
   * Attribute action implementation
   */
  async attributeAction(attributeSpec, context) {
    // Format: "selector|attribute=value"
    const [selector, attrSpec] = attributeSpec.split('|', 2);
    const [attribute, value] = attrSpec.split('=', 2);
    
    const elements = this.findElements(selector, context);
    
    elements.forEach(element => {
      console.log('Rule Processor: Setting attribute', attribute, '=', value, 'on', element);
      
      if (value === null || value === undefined) {
        element.removeAttribute(attribute);
      } else {
        element.setAttribute(attribute, value);
      }
    });
  }
  
  /**
   * Find elements using various selector types
   */
  findElements(selector, context = document) {
    const elements = [];
    
    try {
      // Handle special selectors
      if (selector.includes(':contains(')) {
        // Custom :contains() selector
        const match = selector.match(/^(.+):contains\(['"](.+)['"]\)$/);
        if (match) {
          const [, baseSelector, text] = match;
          const candidates = context.querySelectorAll(baseSelector);
          
          for (const candidate of candidates) {
            if (candidate.textContent.toLowerCase().includes(text.toLowerCase())) {
              elements.push(candidate);
            }
          }
        }
      } else {
        // Standard CSS selector
        const found = context.querySelectorAll(selector);
        elements.push(...found);
      }
    } catch (error) {
      console.warn('Rule Processor: Invalid selector:', selector, error);
    }
    
    return elements;
  }
  
  /**
   * Queue rule processing to avoid conflicts
   */
  queueRuleProcessing(rule, elements) {
    this.executionQueue.push({ rule, elements });
    
    if (!this.isProcessing) {
      this.processQueue();
    }
  }
  
  /**
   * Process the execution queue
   */
  async processQueue() {
    this.isProcessing = true;
    
    while (this.executionQueue.length > 0) {
      const { rule, elements } = this.executionQueue.shift();
      await this.processRule(rule, elements);
    }
    
    this.isProcessing = false;
  }
  
  /**
   * Test if an element matches rule criteria
   */
  matchesRule(element, rule) {
    if (!rule || !rule.selectors) {
      return false;
    }
    
    // Check if element matches any of the rule selectors
    for (const selector of rule.selectors) {
      try {
        if (element.matches(selector)) {
          return true;
        }
        
        // Check if element contains matching children
        if (element.querySelector(selector)) {
          return true;
        }
      } catch (error) {
        // Ignore invalid selectors
      }
    }
    
    return false;
  }
  
  /**
   * Get rule statistics
   */
  getStatistics() {
    return {
      rulesLoaded: this.rules.size,
      queueLength: this.executionQueue.length,
      isProcessing: this.isProcessing
    };
  }
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = RuleProcessor;
} else {
  window.RuleProcessor = RuleProcessor;
}
