# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
rust-version = "1.56"
name = "inherent"
version = "1.0.7"
authors = ["<PERSON> <<EMAIL>>"]
description = "Make trait methods callable without the trait in scope"
documentation = "https://docs.rs/inherent"
readme = "README.md"
categories = [
    "rust-patterns",
    "no-std",
]
license = "MIT OR Apache-2.0"
repository = "https://github.com/dtolnay/inherent"

[package.metadata.docs.rs]
targets = ["x86_64-unknown-linux-gnu"]

[lib]
proc-macro = true

[dependencies.proc-macro2]
version = "1.0"

[dependencies.quote]
version = "1.0"

[dependencies.syn]
version = "2.0.10"
features = ["full"]

[dev-dependencies.rustversion]
version = "1.0"

[dev-dependencies.trybuild]
version = "1.0.49"
features = ["diff"]
