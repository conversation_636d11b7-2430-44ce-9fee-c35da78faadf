[1755687886.9263585,"clobber",{"msg":"Clobber not needed."}]
[1755687886.9323578,"UNKNOWN",{}]
[1755687886.9323578,"UNKNOWN",{}]
[1755687886.9393559,"UNKNOWN",{}]
[1755687886.9393559,"UNKNOWN",{}]
[1755687887.6157734,"build_output",{"line":"Using Python 3.12.10 from C:\\Users\\<USER>\\.mozbuild\\srcdirs\\firefox-source-be5f59d04810\\_virtualenvs\\build\\Scripts\\python.exe"}]
[1755687887.617803,"build_output",{"line":"Adding configure options from E:\\megisto\\mozconfig"}]
[1755687888.7587621,"build_output",{"line":"  --enable-application=browser"}]
[1755687888.7607625,"build_output",{"line":"  --enable-optimize"}]
[1755687888.761762,"build_output",{"line":"  --disable-debug"}]
[1755687888.7667644,"build_output",{"line":"  --disable-debug-symbols"}]
[1755687888.7717614,"build_output",{"line":"  --with-app-name=megisto"}]
[1755687888.776763,"build_output",{"line":"  --with-app-basename=Megisto"}]
[1755687888.7917635,"build_output",{"line":"  --with-branding=browser/branding/megisto"}]
[1755687888.7987642,"build_output",{"line":"  --enable-extensions"}]
[1755687888.8037632,"build_output",{"line":"  --disable-telemetry"}]
[1755687888.8147635,"build_output",{"line":"  --disable-crashreporter"}]
[1755687888.8217633,"build_output",{"line":"  --disable-updater"}]
[1755687888.8227637,"build_output",{"line":"  --enable-release"}]
[1755687888.824762,"build_output",{"line":"  --enable-strip"}]
[1755687888.8257613,"build_output",{"line":"  --enable-install-strip"}]
[1755687888.8287637,"build_output",{"line":"  --target=x86_64-pc-mingw32"}]
[1755687888.832761,"build_output",{"line":"  --host=x86_64-pc-mingw32"}]
[1755687888.8337615,"build_output",{"line":"checking for vcs source checkout... git"}]
[1755687888.9727623,"build_output",{"line":"checking for a shell... C:/mozilla-build/msys2/usr/bin/sh.exe"}]
[1755687889.1869957,"build_output",{"line":"checking for host system type... x86_64-pc-mingw32"}]
[1755687889.31312,"build_output",{"line":"checking for target system type... x86_64-pc-mingw32"}]
[1755687889.8047774,"build_output",{"line":"checking whether cross compiling... no"}]
[1755687889.8777838,"build_output",{"line":"checking if configuration file confvars.sh exists... E:/megisto/firefox-source/browser/confvars.sh"}]
[1755687889.8788242,"build_output",{"line":"checking if configuration file configure.sh exists..."}]
[1755687889.8798547,"build_output",{"line":"ERROR: Expecting key=value format (E:/megisto/firefox-source/browser/branding/megisto/configure.sh, line 1)"}]
