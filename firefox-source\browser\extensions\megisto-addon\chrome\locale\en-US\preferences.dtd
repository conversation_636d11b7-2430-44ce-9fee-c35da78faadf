<!-- <PERSON><PERSON><PERSON>er - Preferences Localization (English) -->

<!-- Window title -->
<!ENTITY megisto.preferences.title "Megisto Browser Preferences">

<!-- Pane labels -->
<!ENTITY megisto.preferences.cookieBlocking.label "Cookie Blocking">
<!ENTITY megisto.preferences.youtube.label "YouTube Enhancement">
<!ENTITY megisto.preferences.general.label "General">

<!-- Cookie Blocking preferences -->
<!ENTITY megisto.preferences.cookieBlocking.settings "Cookie Banner Blocking Settings">
<!ENTITY megisto.preferences.cookieBlocking.enable "Enable automatic cookie banner blocking">
<!ENTITY megisto.preferences.cookieBlocking.aggressive "Use aggressive blocking (may break some sites)">
<!ENTITY megisto.preferences.cookieBlocking.whitelist.label "Whitelist (sites to exclude from blocking):">
<!ENTITY megisto.preferences.cookieBlocking.whitelist.placeholder "Enter domain names, one per line (e.g., example.com)">
<!ENTITY megisto.preferences.cookieBlocking.updateRules "Update Blocking Rules">
<!ENTITY megisto.preferences.cookieBlocking.resetRules "Reset to Defaults">
<!ENTITY megisto.preferences.cookieBlocking.statistics "Blocking Statistics">
<!ENTITY megisto.preferences.cookieBlocking.blocked.today "Blocked today:">
<!ENTITY megisto.preferences.cookieBlocking.blocked.total "Total blocked:">
<!ENTITY megisto.preferences.cookieBlocking.clearStats "Clear Statistics">

<!-- YouTube Enhancement preferences -->
<!ENTITY megisto.preferences.youtube.enhancement "YouTube Video Enhancement">
<!ENTITY megisto.preferences.youtube.enable "Enable YouTube embed enhancement">
<!ENTITY megisto.preferences.youtube.autoplay "Allow autoplay on YouTube videos">
<!ENTITY megisto.preferences.youtube.defaultQuality.label "Default video quality:">
<!ENTITY megisto.preferences.youtube.blockRelated "Block related videos and suggestions">
<!ENTITY megisto.preferences.youtube.advanced "Advanced Video Settings">
<!ENTITY megisto.preferences.youtube.replaceEmbeds "Replace YouTube embeds with enhanced player">
<!ENTITY megisto.preferences.youtube.customControls "Use custom video controls">
<!ENTITY megisto.preferences.youtube.testPlayer "Test Video Player">
<!ENTITY megisto.preferences.youtube.resetPlayer "Reset Player Settings">

<!-- General preferences -->
<!ENTITY megisto.preferences.general.privacy "Privacy Settings">
<!ENTITY megisto.preferences.general.trackingProtection "Enable tracking protection">
<!ENTITY megisto.preferences.general.doNotTrack "Send Do Not Track header">
<!ENTITY megisto.preferences.general.disableTelemetry "Disable telemetry and data collection">
<!ENTITY megisto.preferences.general.updates "Update Settings">
<!ENTITY megisto.preferences.general.updates.auto "Automatically install updates">
<!ENTITY megisto.preferences.general.updates.manual "Check for updates manually">
<!ENTITY megisto.preferences.general.updates.disabled "Disable update checking">
<!ENTITY megisto.preferences.general.updates.check "Check for Updates">
<!ENTITY megisto.preferences.general.updates.history "Update History">
<!ENTITY megisto.preferences.general.about "About Megisto Browser">
<!ENTITY megisto.preferences.general.description "Megisto Browser is a privacy-focused Firefox fork with built-in cookie blocking and YouTube enhancement features.">
<!ENTITY megisto.preferences.general.homepage "Visit Homepage">
<!ENTITY megisto.preferences.general.support "Get Support">
