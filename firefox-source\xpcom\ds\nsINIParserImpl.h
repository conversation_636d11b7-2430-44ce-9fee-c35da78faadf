/* -*- Mode: C++; tab-width: 8; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* vim: set ts=8 sts=2 et sw=2 tw=80: */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#ifndef nsINIParserImpl_h__
#define nsINIParserImpl_h__

#include "nsIINIParser.h"
#include "mozilla/Attributes.h"

#define NS_INIPARSERFACTORY_CONTRACTID "@mozilla.org/xpcom/ini-parser-factory;1"

class nsINIParserFactory final : public nsIINIParserFactory {
  ~nsINIParserFactory() = default;

 public:
  NS_DECL_ISUPPORTS
  NS_DECL_NSIINIPARSERFACTORY
};

#endif  // nsINIParserImpl_h__
