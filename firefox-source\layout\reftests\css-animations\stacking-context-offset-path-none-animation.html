<!DOCTYPE html>
<title>
offset-path animation creates a stacking context even though it has only
'offset-path:none' keyframes
</title>
<style>
span {
  height: 100px;
  width: 100px;
  position: fixed;
  background: green;
  top: 50px;
}
@keyframes OffsetPathNone {
  from, to { offset-path: none }
}
#test {
  width: 100px; height: 100px;
  background: blue;
  animation: OffsetPathNone 100s infinite;
}
</style>
<span></span>
<div id="test"></div>
