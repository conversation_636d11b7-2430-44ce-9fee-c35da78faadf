{"meta": {"processType": 0, "product": "mach", "stackwalk": 0, "version": 27, "preprocessedProfileVersion": 47, "symbolicationNotSupported": true, "interval": 100.0, "startTime": 1755687886455.6738, "profilingStartTime": 0, "logicalCPUs": 12, "physicalCPUs": 6, "mainMemory": 34257047552, "categories": [{"name": "Other", "color": "grey", "subcategories": ["Other"]}, {"name": "Phases", "color": "grey", "subcategories": ["Other"]}, {"name": "Tasks", "color": "grey", "subcategories": ["Other"]}], "markerSchema": [{"name": "CPU", "tooltipLabel": "{marker.name}", "display": [], "data": [{"key": "cpuPer<PERSON>", "label": "CPU Percent", "format": "string"}, {"key": "user_pct", "label": "User %", "format": "string"}, {"key": "system_pct", "label": "System %", "format": "string"}, {"key": "idle_pct", "label": "Idle %", "format": "string"}], "graphs": [{"key": "system", "color": "grey", "type": "bar"}, {"key": "user", "color": "yellow", "type": "bar"}]}, {"name": "Phase", "tooltipLabel": "{marker.data.phase}", "tableLabel": "{marker.name} — {marker.data.phase} — CPU time: {marker.data.cpuTime} ({marker.data.cpuPercent})", "chartLabel": "{marker.data.phase}", "display": ["marker-chart", "marker-table", "timeline-overview"], "data": [{"key": "cpuTime", "label": "CPU Time", "format": "duration"}, {"key": "cpuPer<PERSON>", "label": "CPU Percent", "format": "string"}]}, {"name": "Text", "tooltipLabel": "{marker.name}", "tableLabel": "{marker.name} — {marker.data.text}", "chartLabel": "{marker.data.text}", "display": ["marker-chart", "marker-table"], "data": [{"key": "text", "label": "Description", "format": "string", "searchable": true}]}, {"name": "<PERSON><PERSON>", "tooltipLabel": "{marker.name}", "display": [], "data": [{"key": "used", "label": "Memory Used", "format": "bytes"}, {"key": "cached", "label": "Memory cached", "format": "bytes"}, {"key": "buffers", "label": "Memory buffers", "format": "bytes"}], "graphs": [{"key": "used", "color": "orange", "type": "line-filled"}]}, {"name": "IO", "tooltipLabel": "{marker.name}", "display": [], "data": [{"key": "write_bytes", "label": "Written", "format": "bytes"}, {"key": "write_count", "label": "Write count", "format": "integer"}, {"key": "read_bytes", "label": "Read", "format": "bytes"}, {"key": "read_count", "label": "Read count", "format": "integer"}], "graphs": [{"key": "read_bytes", "color": "green", "type": "bar"}, {"key": "write_bytes", "color": "red", "type": "bar"}]}, {"name": "Process", "chartLabel": "{marker.data.cmd}", "tooltipLabel": "{marker.name}", "tableLabel": "{marker.data.cmd}", "display": ["marker-chart", "marker-table"], "data": [{"key": "cmd", "label": "Command line", "format": "string", "searchable": true}, {"key": "pid", "label": "Process ID", "format": "pid"}, {"key": "ppid", "label": "Parent process ID", "format": "pid"}]}], "usesOnlyOneStackType": true, "CPUName": "Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz", "profilingEndTime": 3047.0}, "libs": [], "threads": [{"processType": "default", "processName": "mach", "processStartupTime": 0, "processShutdownTime": null, "registerTime": 0, "unregisterTime": null, "pausedRanges": [], "showMarkersInTimeline": true, "name": "", "isMainThread": false, "pid": "0", "tid": 0, "samples": {"weightType": "samples", "weight": null, "stack": [], "time": [], "length": 0}, "stringArray": ["(root)", "CPU Use", "Memory", "IO", "Phase", "process", "configure", "resourcemonitor"], "markers": {"data": [{"type": "CPU", "cpuPercent": "11.5%", "user": 0.083, "system": 0.115, "user_pct": "8.3%", "system_pct": "3.1%", "idle_pct": "88.5%"}, {"type": "<PERSON><PERSON>", "used": 22742810624}, {"type": "IO", "read_count": 30, "read_bytes": 3095040, "write_count": 54, "write_bytes": 3276800}, {"type": "CPU", "cpuPercent": "8.0%", "user": 0.045, "system": 0.076, "user_pct": "4.2%", "system_pct": "2.8%", "idle_pct": "84.5%"}, {"type": "<PERSON><PERSON>", "used": 22744399872}, {"type": "IO", "read_count": 54, "read_bytes": 9255424, "write_count": 51, "write_bytes": 8486912}, {"type": "CPU", "cpuPercent": "8.9%", "user": 0.078, "system": 0.091, "user_pct": "8.4%", "system_pct": "1.4%", "idle_pct": "98.0%"}, {"type": "<PERSON><PERSON>", "used": 22760144896}, {"type": "IO", "read_count": 33, "read_bytes": 3884032, "write_count": 53, "write_bytes": 4759552}, {"type": "CPU", "cpuPercent": "9.1%", "user": 0.041, "system": 0.091, "user_pct": "4.1%", "system_pct": "5.0%", "idle_pct": "91.2%"}, {"type": "<PERSON><PERSON>", "used": 22784397312}, {"type": "IO", "read_count": 40, "read_bytes": 186880, "write_count": 93, "write_bytes": 720896}, {"type": "CPU", "cpuPercent": "4.2%", "user": 0.037, "system": 0.037, "user_pct": "3.3%", "system_pct": "0.0%", "idle_pct": "86.8%"}, {"type": "<PERSON><PERSON>", "used": 22807412736}, {"type": "IO", "read_count": 44, "read_bytes": 2045440, "write_count": 89, "write_bytes": 1765376}, {"type": "CPU", "cpuPercent": "7.2%", "user": 0.056, "system": 0.07, "user_pct": "5.5%", "system_pct": "1.4%", "idle_pct": "91.4%"}, {"type": "<PERSON><PERSON>", "used": 22778097664}, {"type": "IO", "read_count": 11, "read_bytes": 4205056, "write_count": 17, "write_bytes": 4026368}, {"type": "CPU", "cpuPercent": "12.4%", "user": 0.11, "system": 0.123, "user_pct": "11.2%", "system_pct": "1.4%", "idle_pct": "89.6%"}, {"type": "<PERSON><PERSON>", "used": 22801711104}, {"type": "IO", "read_count": 0, "read_bytes": 0, "write_count": 0, "write_bytes": 0}, {"type": "CPU", "cpuPercent": "24.0%", "user": 0.23, "system": 0.241, "user_pct": "23.7%", "system_pct": "1.2%", "idle_pct": "78.1%"}, {"type": "<PERSON><PERSON>", "used": 22823849984}, {"type": "IO", "read_count": 5, "read_bytes": 57344, "write_count": 48, "write_bytes": 1806336}, {"type": "CPU", "cpuPercent": "27.4%", "user": 0.233, "system": 0.247, "user_pct": "23.8%", "system_pct": "1.4%", "idle_pct": "77.0%"}, {"type": "<PERSON><PERSON>", "used": 22835621888}, {"type": "IO", "read_count": 0, "read_bytes": 0, "write_count": 0, "write_bytes": 0}, {"type": "CPU", "cpuPercent": "23.1%", "user": 0.228, "system": 0.241, "user_pct": "21.3%", "system_pct": "1.2%", "idle_pct": "71.0%"}, {"type": "<PERSON><PERSON>", "used": 22886621184}, {"type": "IO", "read_count": 0, "read_bytes": 0, "write_count": 0, "write_bytes": 0}, {"type": "CPU", "cpuPercent": "28.0%", "user": 0.253, "system": 0.278, "user_pct": "27.7%", "system_pct": "2.8%", "idle_pct": "79.0%"}, {"type": "<PERSON><PERSON>", "used": 23041990656}, {"type": "IO", "read_count": 42, "read_bytes": 172032, "write_count": 22, "write_bytes": 888832}, {"type": "CPU", "cpuPercent": "10.7%", "user": 0.06, "system": 0.107, "user_pct": "6.0%", "system_pct": "4.8%", "idle_pct": "89.6%"}, {"type": "<PERSON><PERSON>", "used": 22943055872}, {"type": "IO", "read_count": 4, "read_bytes": 16384, "write_count": 158, "write_bytes": 8232960}, {"type": "CPU", "cpuPercent": "8.3%", "user": 0.036, "system": 0.084, "user_pct": "3.6%", "system_pct": "4.8%", "idle_pct": "90.8%"}, {"type": "<PERSON><PERSON>", "used": 23132192768}, {"type": "IO", "read_count": 4, "read_bytes": 820224, "write_count": 0, "write_bytes": 0}, {"type": "CPU", "cpuPercent": "100.0%", "user": 0.53, "system": 1.0, "user_pct": "50.6%", "system_pct": "44.8%", "idle_pct": "0.0%"}, {"type": "<PERSON><PERSON>", "used": 23029907456}, {"type": "IO", "read_count": 0, "read_bytes": 0, "write_count": 0, "write_bytes": 0}, {"type": "CPU", "cpuPercent": "81.7%", "user": 0.493, "system": 0.8, "user_pct": "51.8%", "system_pct": "32.2%", "idle_pct": "21.0%"}, {"type": "<PERSON><PERSON>", "used": 22989082624}, {"type": "IO", "read_count": 10, "read_bytes": 229376, "write_count": 13, "write_bytes": 77824}, {"type": "CPU", "cpuPercent": "21.7%", "user": 0.136, "system": 0.22, "user_pct": "13.4%", "system_pct": "8.3%", "idle_pct": "76.8%"}, {"type": "<PERSON><PERSON>", "used": 23008821248}, {"type": "IO", "read_count": 11, "read_bytes": 225280, "write_count": 4, "write_bytes": 24576}, {"type": "CPU", "cpuPercent": "59.3%", "user": 0.384, "system": 0.607, "user_pct": "39.7%", "system_pct": "23.1%", "idle_pct": "40.6%"}, {"type": "<PERSON><PERSON>", "used": 23048724480}, {"type": "IO", "read_count": 2, "read_bytes": 65536, "write_count": 17, "write_bytes": 1077248}, {"type": "CPU", "cpuPercent": "23.5%", "user": 0.083, "system": 0.167, "user_pct": "8.3%", "system_pct": "8.3%", "idle_pct": "83.5%"}, {"type": "<PERSON><PERSON>", "used": 23079407616}, {"type": "IO", "read_count": 21, "read_bytes": 7569408, "write_count": 27, "write_bytes": 7233536}, {"type": "CPU", "cpuPercent": "71.3%", "user": 0.417, "system": 0.711, "user_pct": "39.4%", "system_pct": "27.8%", "idle_pct": "27.3%"}, {"type": "<PERSON><PERSON>", "used": 23085690880}, {"type": "IO", "read_count": 31, "read_bytes": 4588544, "write_count": 37, "write_bytes": 4804608}, {"type": "CPU", "cpuPercent": "30.8%", "user": 0.242, "system": 0.305, "user_pct": "27.2%", "system_pct": "7.1%", "idle_pct": "78.1%"}, {"type": "<PERSON><PERSON>", "used": 23122759680}, {"type": "IO", "read_count": 84, "read_bytes": 16978944, "write_count": 239, "write_bytes": 19664896}, {"type": "CPU", "cpuPercent": "3.3%", "user": 0.017, "system": 0.033, "user_pct": "1.7%", "system_pct": "1.7%", "idle_pct": "96.8%"}, {"type": "<PERSON><PERSON>", "used": 23098228736}, {"type": "IO", "read_count": 17, "read_bytes": 3202048, "write_count": 28, "write_bytes": 4018176}, {"type": "CPU", "cpuPercent": "15.0%", "user": 0.115, "system": 0.148, "user_pct": "9.7%", "system_pct": "2.8%", "idle_pct": "72.0%"}, {"type": "<PERSON><PERSON>", "used": 23187132416}, {"type": "IO", "read_count": 36, "read_bytes": 7195136, "write_count": 47, "write_bytes": 6897664}, {"type": "CPU", "cpuPercent": "7.0%", "user": 0.042, "system": 0.069, "user_pct": "5.0%", "system_pct": "3.3%", "idle_pct": "111.8%"}, {"type": "<PERSON><PERSON>", "used": 23189680128}, {"type": "IO", "read_count": 34, "read_bytes": 3014144, "write_count": 65, "write_bytes": 4284416}, {"type": "CPU", "cpuPercent": "2.1%", "user": 0.01, "system": 0.021, "user_pct": "1.0%", "system_pct": "1.0%", "idle_pct": "97.9%"}, {"type": "<PERSON><PERSON>", "used": 23208124416}, {"type": "IO", "read_count": 34, "read_bytes": 292352, "write_count": 57, "write_bytes": 258048}, {"type": "CPU", "cpuPercent": "6.9%", "user": 0.056, "system": 0.07, "user_pct": "5.5%", "system_pct": "1.4%", "idle_pct": "91.4%"}, {"type": "<PERSON><PERSON>", "used": 23219351552}, {"type": "IO", "read_count": 31, "read_bytes": 246272, "write_count": 47, "write_bytes": 208896}, {"type": "CPU", "cpuPercent": "0.0%", "user": 0.0, "system": 0.0, "user_pct": "0.0%", "system_pct": "0.0%", "idle_pct": "102.2%"}, {"type": "<PERSON><PERSON>", "used": 23185137664}, {"type": "IO", "read_count": 37, "read_bytes": 5800448, "write_count": 61, "write_bytes": 5160960}, {"type": "CPU", "cpuPercent": "6.2%", "user": 0.036, "system": 0.06, "user_pct": "3.6%", "system_pct": "2.4%", "idle_pct": "92.3%"}, {"type": "<PERSON><PERSON>", "used": 23197450240}, {"type": "IO", "read_count": 46, "read_bytes": 9840128, "write_count": 72, "write_bytes": 10182656}, {"type": "Phase", "phase": "configure", "cpuPercent": "22.7%", "cpuTime": 35734.37500001455}, {"type": "Text", "text": "activate virtualenv"}, {"type": "Phase", "phase": "teardown"}, {"type": "Text", "text": "stop"}, {"type": "Text", "text": "as_profile"}], "name": [1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 4, 6, 4, 7, 7], "startTime": [63, 63, 63, 188, 188, 188, 282, 282, 282, 375, 375, 375, 532, 532, 532, 610, 610, 610, 704, 704, 704, 797, 797, 797, 907, 907, 907, 1000, 1000, 1000, 1110, 1110, 1110, 1204, 1204, 1204, 1313, 1313, 1313, 1422, 1422, 1422, 1579, 1579, 1579, 1672, 1672, 1672, 1750, 1750, 1750, 1891, 1891, 1891, 1969, 1969, 1969, 2250, 2250, 2250, 2360, 2360, 2360, 2438, 2438, 2438, 2532, 2532, 2532, 2610, 2610, 2610, 2735, 2735, 2735, 2829, 2829, 2829, 2922, 2922, 2922, 16.0, 688.0, 3032.0, 3032.0, 3047.0], "endTime": [188, 188, 188, 282, 282, 282, 375, 375, 375, 532, 532, 532, 610, 610, 610, 704, 704, 704, 797, 797, 797, 907, 907, 907, 1000, 1000, 1000, 1110, 1110, 1110, 1204, 1204, 1204, 1313, 1313, 1313, 1422, 1422, 1422, 1579, 1579, 1579, 1672, 1672, 1672, 1750, 1750, 1750, 1891, 1891, 1891, 1969, 1969, 1969, 2250, 2250, 2250, 2360, 2360, 2360, 2438, 2438, 2438, 2532, 2532, 2532, 2610, 2610, 2610, 2735, 2735, 2735, 2829, 2829, 2829, 2922, 2922, 2922, 3032, 3032, 3032, 3032.0, 688.0, 3047.0, 3047.0, 3047.0], "phase": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "category": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2, 1, 2, 2], "length": 86}, "stackTable": {"frame": [0], "prefix": [null], "category": [0], "subcategory": [0], "length": 1}, "frameTable": {"address": [-1], "inlineDepth": [0], "category": [null], "subcategory": [0], "func": [0], "nativeSymbol": [null], "innerWindowID": [0], "implementation": [null], "line": [null], "column": [null], "length": 1}, "funcTable": {"isJS": [false], "relevantForJS": [false], "name": [0], "resource": [-1], "fileName": [null], "lineNumber": [null], "columnNumber": [null], "length": 1}, "resourceTable": {"lib": [], "name": [], "host": [], "type": [], "length": 0}, "nativeSymbols": {"libIndex": [], "address": [], "name": [], "functionSize": [], "length": 0}}], "counters": []}