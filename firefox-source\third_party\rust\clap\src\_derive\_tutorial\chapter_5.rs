//! ## Next Steps
//!
//! - [Cookbook][crate::_cookbook] for application-focused examples
//! - Explore more features in the [Derive reference][super]
//!   - See also [`Command`], [`Arg`], [`ArgGroup`], and [`PossibleValue`] builder functions which
//!     can be used as attributes
//!
//! For support, see [Discussions](https://github.com/clap-rs/clap/discussions)

#![allow(unused_imports)]
use crate::builder::*;

pub use super::chapter_4 as previous;
pub use crate::_derive::_tutorial as table_of_contents;
