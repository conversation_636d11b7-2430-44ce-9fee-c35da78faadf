# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.67.0"
name = "intl-memoizer"
version = "0.5.3"
authors = [
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>",
    "<PERSON><PERSON> <<EMAIL>>",
    "Sta<PERSON> <<EMAIL>>",
]
build = false
include = [
    "src/**/*",
    "benches/*.rs",
    "Cargo.toml",
    "README.md",
    "LICENSE-APACHE",
    "LICENSE-MIT",
]
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = """
A memoizer specifically tailored for storing lazy-initialized intl formatters for Project Fluent,
a localization system designed to unleash the entire expressive power of natural language translations.
"""
homepage = "https://www.projectfluent.org"
readme = "README.md"
keywords = [
    "localization",
    "l10n",
    "i18n",
    "intl",
    "internationalization",
]
categories = [
    "localization",
    "internationalization",
]
license = "Apache-2.0 OR MIT"
repository = "https://github.com/projectfluent/fluent-rs"

[lib]
name = "intl_memoizer"
path = "src/lib.rs"

[dependencies.type-map]
version = "0.5"

[dependencies.unic-langid]
version = "0.9"

[dev-dependencies.fluent-langneg]
version = "0.13"

[dev-dependencies.intl_pluralrules]
version = "7.0"
