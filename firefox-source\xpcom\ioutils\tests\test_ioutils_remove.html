<!-- Any copyright is dedicated to the Public Domain.
- http://creativecommons.org/publicdomain/zero/1.0/ -->
<!DOCTYPE HTML>
<html>

<head>
  <meta charset="utf-8">
  <title>Test the IOUtils file I/O API</title>
  <script src="chrome://mochikit/content/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" href="chrome://mochikit/content/tests/SimpleTest/test.css" />
  <script src="file_ioutils_test_fixtures.js"></script>
  <script>
    "use strict";

    const { Assert } = ChromeUtils.importESModule(
      "resource://testing-common/Assert.sys.mjs"
    );

    add_task(async function test_create_and_remove_file() {
      info("Test creating and removing a single file");
      const tmpFileName = PathUtils.join(PathUtils.tempDir, "test_ioutils_create_and_remove.tmp");
      await IOUtils.write(tmpFileName, new Uint8Array(0));
      ok(await fileExists(tmpFileName), `Expected file ${tmpFileName} to exist`);

      await IOUtils.remove(tmpFileName);
      ok(!await fileExists(tmpFileName), "IOUtils::remove can remove files");

      info("Test creating and removing an empty directory");
      const tempDirName = PathUtils.join(PathUtils.tempDir, "test_ioutils_create_and_remove.tmp.d");
      await IOUtils.makeDirectory(tempDirName);
      ok(await dirExists(tempDirName), `Expected directory ${tempDirName} to exist`);

      await IOUtils.remove(tempDirName);
      ok(!await dirExists(tempDirName), "IOUtils::remove can remove empty directories");
    });

    add_task(async function test_remove_non_existing() {
      const tmpFileName = PathUtils.join(PathUtils.tempDir, "test_ioutil_remove_non_existing.tmp");
      ok(!await fileExists(tmpFileName), `Expected file ${tmpFileName} not to exist`);

      await IOUtils.remove(tmpFileName, { ignoreAbsent: true });
      ok(!await fileExists(tmpFileName), "IOUtils::remove can ignore missing files without error");

      await Assert.rejects(
        IOUtils.remove(tmpFileName, { ignoreAbsent: false }),
        /NotFoundError: Could not remove `.*': file does not exist/,
        "IOUtils::remove can throw an error when target file is missing"
      );
      ok(!await fileExists(tmpFileName), `Expected file ${tmpFileName} not to exist`);
    });

    add_task(async function test_remove_recursive() {
      const tmpParentDir = PathUtils.join(PathUtils.tempDir, "test_ioutils_remove.tmp.d");
      const tmpChildDir = PathUtils.join(tmpParentDir, "child.tmp.d");
      const tmpTopLevelFileName = PathUtils.join(tmpParentDir, "top.tmp");
      const tmpNestedFileName = PathUtils.join(tmpChildDir, "nested.tmp");
      await createDir(tmpChildDir);
      await createFile(tmpTopLevelFileName, "");
      await createFile(tmpNestedFileName, "");

      ok(
        await fileExists(tmpTopLevelFileName),
        `Expected file ${tmpTopLevelFileName} to exist`
      );
      ok(
        await fileExists(tmpNestedFileName),
        `Expected file  ${tmpNestedFileName} to exist`
      );

      await Assert.rejects(
        IOUtils.remove(tmpParentDir, { recursive: false }),
        /OperationError: Could not remove `.*': the directory is not empty/,
        "IOUtils::remove fails if non-recursively removing directory with contents"
      );

      await IOUtils.remove(tmpParentDir, { recursive: true });
      ok(
        !await dirExists(tmpParentDir),
        "IOUtils::remove can recursively remove a directory"
      );
    });

    if (Services.appinfo.OS === "WINNT") {
      add_task(async function test_remove_retry_readonly() {

        const tmpDir = PathUtils.join(PathUtils.tempDir, "test_ioutils_remove_retry_readonly.tmp.d");
        const path = PathUtils.join(tmpDir, "file.txt");

        await createDir(tmpDir);
        await createFile(path, "");

        await IOUtils.setWindowsAttributes(path, { readOnly: true });

        await Assert.rejects(
          IOUtils.remove(path),
          /NotAllowedError/,
          "Cannot remove a readonly file by default"
        );

        Assert.ok(await fileExists(path), "File should still exist");

        await IOUtils.remove(path, { retryReadonly: true });

        Assert.ok(!await fileExists(path), "File should not exist");

        await IOUtils.remove(tmpDir);
      });
    }
  </script>
</head>

<body>
  <p id="display"></p>
  <div id="content" style="display: none"></div>
  <pre id="test"></pre>
</body>

</html>
