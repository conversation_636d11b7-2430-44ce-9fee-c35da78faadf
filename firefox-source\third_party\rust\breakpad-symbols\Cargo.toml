# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "breakpad-symbols"
version = "0.24.0"
authors = ["<PERSON> <<EMAIL>>"]
build = false
exclude = ["testdata/*"]
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "A library for working with Google Breakpad's text-format symbol files."
homepage = "https://github.com/rust-minidump/rust-minidump"
readme = "README.md"
license = "MIT"
repository = "https://github.com/rust-minidump/rust-minidump"
resolver = "2"

[lib]
name = "breakpad_symbols"
path = "src/lib.rs"

[dependencies.async-trait]
version = "0.1.52"

[dependencies.cab]
version = "0.6.0"
optional = true

[dependencies.cachemap2]
version = "0.3.0"

[dependencies.circular]
version = "0.3.0"

[dependencies.debugid]
version = "0.8.0"

[dependencies.futures-util]
version = "0.3"

[dependencies.minidump-common]
version = "0.24.0"

[dependencies.nom]
version = "7"

[dependencies.range-map]
version = "0.2"

[dependencies.reqwest]
version = "0.12"
features = [
    "gzip",
    "rustls-tls",
]
optional = true
default-features = false

[dependencies.tempfile]
version = "3.3.0"
optional = true

[dependencies.thiserror]
version = "1.0.37"

[dependencies.tracing]
version = "0.1.34"
features = ["log"]

[dev-dependencies.tempfile]
version = "3.3.0"

[dev-dependencies.tokio]
version = "1.12.0"
features = ["full"]

[features]
fuzz = []
http = [
    "reqwest",
    "tempfile",
]
mozilla_cab_symbols = [
    "http",
    "cab",
]

[badges.travis-ci]
repository = "rust-minidump/rust-minidump"
