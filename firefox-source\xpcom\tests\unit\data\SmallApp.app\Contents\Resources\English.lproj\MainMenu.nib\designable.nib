<?xml version="1.0" encoding="UTF-8"?>
<archive type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="7.02">
	<data>
		<int key="IBDocument.SystemTarget">0</int>
		<string key="IBDocument.SystemVersion">9E17</string>
		<string key="IBDocument.InterfaceBuilderVersion">644</string>
		<string key="IBDocument.AppKitVersion">949.33</string>
		<string key="IBDocument.HIToolboxVersion">352.00</string>
		<object class="NSMutableArray" key="IBDocument.EditedObjectIDs">
			<bool key="EncodedWithXMLCoder">YES</bool>
			<integer value="29"/>
		</object>
		<object class="NSArray" key="IBDocument.PluginDependencies">
			<bool key="EncodedWithXMLCoder">YES</bool>
			<string>com.apple.InterfaceBuilderKit</string>
			<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
		</object>
		<object class="NSMutableArray" key="IBDocument.RootObjects" id="1048">
			<bool key="EncodedWithXMLCoder">YES</bool>
			<object class="NSCustomObject" id="1021">
				<string key="NSClassName">NSApplication</string>
			</object>
			<object class="NSCustomObject" id="1014">
				<string key="NSClassName">FirstResponder</string>
			</object>
			<object class="NSCustomObject" id="1050">
				<string key="NSClassName">NSApplication</string>
			</object>
			<object class="NSMenu" id="649796088">
				<string key="NSTitle">AMainMenu</string>
				<object class="NSMutableArray" key="NSMenuItems">
					<bool key="EncodedWithXMLCoder">YES</bool>
					<object class="NSMenuItem" id="694149608">
						<reference key="NSMenu" ref="649796088"/>
						<string key="NSTitle">NewApplication</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<object class="NSCustomResource" key="NSOnImage" id="35465992">
							<string key="NSClassName">NSImage</string>
							<string key="NSResourceName">NSMenuCheckmark</string>
						</object>
						<object class="NSCustomResource" key="NSMixedImage" id="591987212">
							<string key="NSClassName">NSImage</string>
							<string key="NSResourceName">NSMenuMixedState</string>
						</object>
						<string key="NSAction">submenuAction:</string>
						<object class="NSMenu" key="NSSubmenu" id="110575045">
							<string key="NSTitle">NewApplication</string>
							<object class="NSMutableArray" key="NSMenuItems">
								<bool key="EncodedWithXMLCoder">YES</bool>
								<object class="NSMenuItem" id="632727374">
									<reference key="NSMenu" ref="110575045"/>
									<string key="NSTitle">Quit NewApplication</string>
									<string key="NSKeyEquiv">q</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="35465992"/>
									<reference key="NSMixedImage" ref="591987212"/>
								</object>
							</object>
							<string key="NSName">_NSAppleMenu</string>
						</object>
					</object>
					<object class="NSMenuItem" id="379814623">
						<reference key="NSMenu" ref="649796088"/>
						<string key="NSTitle">File</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<reference key="NSOnImage" ref="35465992"/>
						<reference key="NSMixedImage" ref="591987212"/>
					</object>
					<object class="NSMenuItem" id="952259628">
						<reference key="NSMenu" ref="649796088"/>
						<string key="NSTitle">Edit</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<reference key="NSOnImage" ref="35465992"/>
						<reference key="NSMixedImage" ref="591987212"/>
					</object>
					<object class="NSMenuItem" id="626404410">
						<reference key="NSMenu" ref="649796088"/>
						<string key="NSTitle">Format</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<reference key="NSOnImage" ref="35465992"/>
						<reference key="NSMixedImage" ref="591987212"/>
					</object>
					<object class="NSMenuItem" id="586577488">
						<reference key="NSMenu" ref="649796088"/>
						<string key="NSTitle">View</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<reference key="NSOnImage" ref="35465992"/>
						<reference key="NSMixedImage" ref="591987212"/>
					</object>
					<object class="NSMenuItem" id="713487014">
						<reference key="NSMenu" ref="649796088"/>
						<string key="NSTitle">Window</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<reference key="NSOnImage" ref="35465992"/>
						<reference key="NSMixedImage" ref="591987212"/>
					</object>
					<object class="NSMenuItem" id="391199113">
						<reference key="NSMenu" ref="649796088"/>
						<string key="NSTitle">Help</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<reference key="NSOnImage" ref="35465992"/>
						<reference key="NSMixedImage" ref="591987212"/>
					</object>
				</object>
				<string key="NSName">_NSMainMenu</string>
			</object>
		</object>
		<object class="IBObjectContainer" key="IBDocument.Objects">
			<object class="NSMutableArray" key="connectionRecords">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">terminate:</string>
						<reference key="source" ref="1014"/>
						<reference key="destination" ref="632727374"/>
					</object>
					<int key="connectionID">369</int>
				</object>
			</object>
			<object class="IBMutableOrderedSet" key="objectRecords">
				<object class="NSArray" key="orderedObjects">
					<bool key="EncodedWithXMLCoder">YES</bool>
					<object class="IBObjectRecord">
						<int key="objectID">0</int>
						<object class="NSArray" key="object" id="1049">
							<bool key="EncodedWithXMLCoder">YES</bool>
						</object>
						<reference key="children" ref="1048"/>
						<nil key="parent"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-2</int>
						<reference key="object" ref="1021"/>
						<reference key="parent" ref="1049"/>
						<string type="base64-UTF8" key="objectName">RmlsZSdzIE93bmVyA</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-1</int>
						<reference key="object" ref="1014"/>
						<reference key="parent" ref="1049"/>
						<string key="objectName">First Responder</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-3</int>
						<reference key="object" ref="1050"/>
						<reference key="parent" ref="1049"/>
						<string key="objectName">Application</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">29</int>
						<reference key="object" ref="649796088"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="713487014"/>
							<reference ref="694149608"/>
							<reference ref="391199113"/>
							<reference ref="952259628"/>
							<reference ref="379814623"/>
							<reference ref="586577488"/>
							<reference ref="626404410"/>
						</object>
						<reference key="parent" ref="1049"/>
						<string key="objectName">MainMenu</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">19</int>
						<reference key="object" ref="713487014"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
						</object>
						<reference key="parent" ref="649796088"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">56</int>
						<reference key="object" ref="694149608"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="110575045"/>
						</object>
						<reference key="parent" ref="649796088"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">103</int>
						<reference key="object" ref="391199113"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
						</object>
						<reference key="parent" ref="649796088"/>
						<string key="objectName">1</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">217</int>
						<reference key="object" ref="952259628"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
						</object>
						<reference key="parent" ref="649796088"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">83</int>
						<reference key="object" ref="379814623"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
						</object>
						<reference key="parent" ref="649796088"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">57</int>
						<reference key="object" ref="110575045"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="632727374"/>
						</object>
						<reference key="parent" ref="694149608"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">136</int>
						<reference key="object" ref="632727374"/>
						<reference key="parent" ref="110575045"/>
						<string key="objectName">1111</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">295</int>
						<reference key="object" ref="586577488"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
						</object>
						<reference key="parent" ref="649796088"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">299</int>
						<reference key="object" ref="626404410"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
						</object>
						<reference key="parent" ref="649796088"/>
					</object>
				</object>
			</object>
			<object class="NSMutableDictionary" key="flattenedProperties">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<object class="NSMutableArray" key="dict.sortedKeys">
					<bool key="EncodedWithXMLCoder">YES</bool>
					<string>-1.IBPluginDependency</string>
					<string>-2.IBPluginDependency</string>
					<string>-3.IBPluginDependency</string>
					<string>103.IBPluginDependency</string>
					<string>103.ImportedFromIB2</string>
					<string>136.IBPluginDependency</string>
					<string>136.ImportedFromIB2</string>
					<string>19.IBPluginDependency</string>
					<string>19.ImportedFromIB2</string>
					<string>217.IBPluginDependency</string>
					<string>217.ImportedFromIB2</string>
					<string>29.IBEditorWindowLastContentRect</string>
					<string>29.IBPluginDependency</string>
					<string>29.ImportedFromIB2</string>
					<string>29.WindowOrigin</string>
					<string>29.editorWindowContentRectSynchronizationRect</string>
					<string>295.IBPluginDependency</string>
					<string>299.IBPluginDependency</string>
					<string>56.IBPluginDependency</string>
					<string>56.ImportedFromIB2</string>
					<string>57.IBEditorWindowLastContentRect</string>
					<string>57.IBPluginDependency</string>
					<string>57.ImportedFromIB2</string>
					<string>57.editorWindowContentRectSynchronizationRect</string>
					<string>83.IBPluginDependency</string>
					<string>83.ImportedFromIB2</string>
				</object>
				<object class="NSMutableArray" key="dict.values">
					<bool key="EncodedWithXMLCoder">YES</bool>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilderKit</string>
					<string>com.apple.InterfaceBuilderKit</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<integer value="1" id="9"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<reference ref="9"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<reference ref="9"/>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<reference ref="9"/>
					<string>{{0, 975}, {478, 20}}</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<reference ref="9"/>
					<string>{74, 862}</string>
					<string>{{6, 978}, {478, 20}}</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<reference ref="9"/>
					<string>{{12, 952}, {218, 23}}</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<reference ref="9"/>
					<string>{{23, 794}, {245, 183}}</string>
					<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
					<reference ref="9"/>
				</object>
			</object>
			<object class="NSMutableDictionary" key="unlocalizedProperties">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<object class="NSArray" key="dict.sortedKeys">
					<bool key="EncodedWithXMLCoder">YES</bool>
				</object>
				<object class="NSMutableArray" key="dict.values">
					<bool key="EncodedWithXMLCoder">YES</bool>
				</object>
			</object>
			<nil key="activeLocalization"/>
			<object class="NSMutableDictionary" key="localizations">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<object class="NSArray" key="dict.sortedKeys">
					<bool key="EncodedWithXMLCoder">YES</bool>
				</object>
				<object class="NSMutableArray" key="dict.values">
					<bool key="EncodedWithXMLCoder">YES</bool>
				</object>
			</object>
			<nil key="sourceID"/>
			<int key="maxID">374</int>
		</object>
		<object class="IBClassDescriber" key="IBDocument.Classes"/>
		<int key="IBDocument.localizationMode">0</int>
		<string key="IBDocument.LastKnownRelativeProjectPath">../SmallApp.xcodeproj</string>
		<int key="IBDocument.defaultPropertyAccessControl">3</int>
	</data>
</archive>
