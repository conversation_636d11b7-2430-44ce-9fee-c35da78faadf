# Megisto Browser - Firefox Source Cloning Script
# This script clones the Firefox source code and sets up the Megisto fork

param(
    [string]$Source = "github",  # "github" or "mercurial"
    [string]$Branch = "central", # Firefox branch to clone
    [switch]$Shallow,            # Perform shallow clone
    [switch]$Verbose
)

$ErrorActionPreference = "Stop"

Write-Host "=== Megisto Browser - Firefox Source Setup ===" -ForegroundColor Green

# Check if git/hg is available
if ($Source -eq "github" -and -not (Get-Command "git" -ErrorAction SilentlyContinue)) {
    Write-Error "Git is not installed or not in PATH. Please install Git first."
    exit 1
}

if ($Source -eq "mercurial" -and -not (Get-Command "hg" -ErrorAction SilentlyContinue)) {
    Write-Error "Mercurial is not installed or not in PATH. Please install Mercurial first."
    exit 1
}

$rootDir = Get-Location
$firefoxDir = Join-Path $rootDir "firefox-source"

# Remove existing Firefox source if it exists
if (Test-Path $firefoxDir) {
    Write-Host "Removing existing Firefox source directory..." -ForegroundColor Yellow
    Remove-Item $firefoxDir -Recurse -Force
}

# Clone Firefox source
Write-Host "Cloning Firefox source code..." -ForegroundColor Yellow

if ($Source -eq "github") {
    $repoUrl = "https://github.com/mozilla/gecko-dev.git"
    
    if ($Shallow) {
        Write-Host "Performing shallow clone from GitHub..." -ForegroundColor Yellow
        git clone --depth 1 --branch $Branch $repoUrl $firefoxDir
    } else {
        Write-Host "Performing full clone from GitHub..." -ForegroundColor Yellow
        git clone --branch $Branch $repoUrl $firefoxDir
    }
} else {
    $repoUrl = "https://hg.mozilla.org/mozilla-unified"
    
    Write-Host "Cloning from Mozilla Mercurial repository..." -ForegroundColor Yellow
    hg clone $repoUrl $firefoxDir
    
    if ($Branch -ne "central") {
        Set-Location $firefoxDir
        hg update $Branch
        Set-Location $rootDir
    }
}

if (-not (Test-Path $firefoxDir)) {
    Write-Error "Failed to clone Firefox source code."
    exit 1
}

Write-Host "Firefox source cloned successfully." -ForegroundColor Green

# Create mozconfig file
Write-Host "Creating mozconfig file..." -ForegroundColor Yellow

$mozconfigContent = @"
# Megisto Browser Build Configuration

# Build Firefox with Megisto branding
ac_add_options --enable-application=browser

# Optimization settings
ac_add_options --enable-optimize
ac_add_options --disable-debug
ac_add_options --disable-debug-symbols

# Megisto-specific options
ac_add_options --with-app-name=megisto
ac_add_options --with-app-basename=Megisto
ac_add_options --with-branding=browser/branding/megisto

# Enable system add-on support
ac_add_options --enable-extensions

# Disable telemetry and data collection
ac_add_options --disable-telemetry
ac_add_options --disable-crashreporter
ac_add_options --disable-updater

# Performance optimizations
ac_add_options --enable-release
ac_add_options --enable-strip
ac_add_options --enable-install-strip

# Windows-specific options
ac_add_options --target=x86_64-pc-mingw32
ac_add_options --host=x86_64-pc-mingw32

# Output directory
mk_add_options MOZ_OBJDIR=@TOPSRCDIR@/obj-megisto
"@

$mozconfigPath = Join-Path $rootDir "mozconfig"
$mozconfigContent | Out-File -FilePath $mozconfigPath -Encoding UTF8

Write-Host "mozconfig created at: $mozconfigPath" -ForegroundColor Green

# Create Megisto branding directory
Write-Host "Setting up Megisto branding..." -ForegroundColor Yellow

$brandingDir = Join-Path $firefoxDir "browser\branding\megisto"
New-Item -ItemType Directory -Path $brandingDir -Force | Out-Null

# Create branding files
$brandingConfig = @"
# Megisto Browser Branding Configuration

MOZ_APP_VENDOR=Megisto
MOZ_APP_NAME=megisto
MOZ_APP_DISPLAYNAME=Megisto Browser
MOZ_APP_VERSION=1.0.0
MOZ_APP_VERSION_DISPLAY=1.0.0

MOZ_BRANDING_DIRECTORY=browser/branding/megisto
MOZ_OFFICIAL_BRANDING_DIRECTORY=browser/branding/megisto

# Update settings
MOZ_UPDATER=0
MOZ_UPDATE_CHANNEL=release

# Crash reporting
MOZ_CRASHREPORTER=0

# Telemetry
MOZ_TELEMETRY_REPORTING=0
MOZ_DATA_REPORTING=0
MOZ_SERVICES_HEALTHREPORT=0
MOZ_NORMANDY=0
"@

$brandingConfigPath = Join-Path $brandingDir "configure.sh"
$brandingConfig | Out-File -FilePath $brandingConfigPath -Encoding UTF8

# Create locales file
$localesContent = @"
en-US
"@

$localesPath = Join-Path $brandingDir "locales"
$localesContent | Out-File -FilePath $localesPath -Encoding UTF8

Write-Host "Megisto branding created." -ForegroundColor Green

# Copy system add-on to Firefox source
Write-Host "Integrating Megisto system add-on..." -ForegroundColor Yellow

$sourceAddonDir = Join-Path $rootDir "browser\extensions\megisto-addon"
$targetAddonDir = Join-Path $firefoxDir "browser\extensions\megisto-addon"

if (Test-Path $sourceAddonDir) {
    Copy-Item $sourceAddonDir $targetAddonDir -Recurse -Force
    Write-Host "System add-on integrated into Firefox source." -ForegroundColor Green
} else {
    Write-Warning "System add-on not found at $sourceAddonDir"
}

# Create build preparation script
Write-Host "Creating build preparation script..." -ForegroundColor Yellow

$prepBuildScript = @"
#!/bin/bash
# Megisto Browser - Build Preparation Script

echo "Preparing Megisto Browser build..."

# Set up environment
export MOZCONFIG="$rootDir/mozconfig"
export MOZ_OBJDIR="$firefoxDir/obj-megisto"

# Bootstrap build environment
cd "$firefoxDir"
./mach bootstrap --application-choice browser --no-interactive

echo "Build preparation complete."
echo "Run './mach build' to build Megisto Browser."
"@

$prepBuildPath = Join-Path $rootDir "tools\prep-build.sh"
$prepBuildScript | Out-File -FilePath $prepBuildPath -Encoding UTF8

Write-Host "Build preparation script created at: $prepBuildPath" -ForegroundColor Green

# Create Windows batch file for MozillaBuild
$batchScript = @"
@echo off
echo Starting MozillaBuild environment for Megisto Browser...
cd /d "%~dp0\.."
set MOZCONFIG=%CD%\mozconfig
C:\mozilla-build\start-shell.bat
"@

$batchPath = Join-Path $rootDir "tools\start-mozillabuild.bat"
$batchScript | Out-File -FilePath $batchPath -Encoding ASCII

Write-Host "MozillaBuild starter script created at: $batchPath" -ForegroundColor Green

Write-Host "`n=== Firefox Source Setup Complete ===" -ForegroundColor Green
Write-Host "Firefox source cloned and configured successfully!" -ForegroundColor Green
Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Run .\tools\start-mozillabuild.bat to start MozillaBuild environment" -ForegroundColor White
Write-Host "2. In MozillaBuild shell, run: ./mach bootstrap --application-choice browser" -ForegroundColor White
Write-Host "3. Then run: ./mach build" -ForegroundColor White
Write-Host "`nAlternatively, run .\tools\build.ps1 for automated building" -ForegroundColor White
