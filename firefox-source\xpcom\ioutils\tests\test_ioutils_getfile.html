<!-- Any copyright is dedicated to the Public Domain.
- http://creativecommons.org/publicdomain/zero/1.0/ -->
<!DOCTYPE HTML>
<html>

<head>
  <meta charset="utf-8">
  <title>Test the IOUtils file I/O API</title>
  <script src="chrome://mochikit/content/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" href="chrome://mochikit/content/tests/SimpleTest/test.css" />
  <script>
    "use strict";

    const TEST_PATH = PathUtils.join(PathUtils.tempDir, "test-ioutils-getfile");

    add_task(async function test_getFile() {
      const expectedPath = PathUtils.join(TEST_PATH, "foo", "bar", "baz", "get-file.txt");
      const parentPath = PathUtils.parent(expectedPath);

      ok(!(await IOUtils.exists(parentPath)), "Parent directory should not exist");

      const file = await IOUtils.getFile(TEST_PATH, "foo", "bar", "baz", "get-file.txt");
      const path = file.path;

      is(path, expectedPath, "Should have the correct path");
      ok(await IOUtils.exists(parentPath), "Parent directory should be created");
      ok(!(await IOUtils.exists(path)), "File should not be created");

      await IOUtils.remove(TEST_PATH, { recursive: true });
    });

    add_task(async function test_getFile_exists() {
      const expectedPath = PathUtils.join(TEST_PATH, "foo", "bar", "baz", "get-file-exists.txt");

      await IOUtils.makeDirectory(PathUtils.parent(expectedPath));
      await IOUtils.writeUTF8(expectedPath, "hello world");

      const file = await IOUtils.getFile(TEST_PATH, "foo", "bar", "baz", "get-file-exists.txt");
      is(file.path, expectedPath, "Should have the correct path");
      is(await IOUtils.readUTF8(file.path), "hello world", "Contents should be unchanged");

      await IOUtils.remove(TEST_PATH, { recursive: true });
    });

    add_task(async function test_getDirectory() {
      const expectedPath = PathUtils.join(TEST_PATH, "qux", "quux", "corge");

      ok(!(await IOUtils.exists(PathUtils.parent(expectedPath))), "Parent directory should not exist");

      const file = await IOUtils.getDirectory(TEST_PATH, "qux", "quux", "corge");

      is(file.path, expectedPath, "Should have the correct path");
      ok(await IOUtils.exists(expectedPath), "Directory should be created");

      const info = await IOUtils.stat(expectedPath);
      is(info.type, "directory", "Should create a directory");

      await IOUtils.remove(TEST_PATH, { recursive: true });
    });

    add_task(async function test_getDirectory_exists() {
      const expectedPath = PathUtils.join(TEST_PATH, "qux", "quux", "corge");

      await IOUtils.makeDirectory(expectedPath);

      const file = await IOUtils.getDirectory(TEST_PATH, "qux", "quux", "corge");
      is(file.path, expectedPath, "Should have the correct path");
      ok(await IOUtils.exists(expectedPath), "Directory should still exist");

      const info = await IOUtils.stat(expectedPath);
      is(info.type, "directory", "Should still be a directory");

      await IOUtils.remove(TEST_PATH, { recursive: true });
    });
  </script>
</head>

<body>
  <p id="display"></p>
  <div id="content" style="display: none"></div>
  <pre id="test"></pre>
</body>

</html>
