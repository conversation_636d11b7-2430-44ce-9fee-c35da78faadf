/**
 * Megisto Browser - Global Test Setup
 * Runs before all tests to prepare the environment
 */

const fs = require('fs');
const path = require('path');

async function globalSetup(config) {
  console.log('🚀 Setting up Megisto Browser tests...');
  
  // Check if Megisto Browser executable exists
  const megistoBrowserPath = process.env.MEGISTO_BROWSER_PATH || 
    path.join(__dirname, '../firefox-source/obj-megisto/dist/bin/megisto.exe');
  
  if (!fs.existsSync(megistoBrowserPath)) {
    console.warn(`⚠️  Megisto Browser not found at: ${megistoBrowserPath}`);
    console.warn('   Please build Megisto Browser first using: .\\tools\\build.ps1');
    console.warn('   Or set MEGISTO_BROWSER_PATH environment variable');
  } else {
    console.log(`✅ Megisto Browser found at: ${megistoBrowserPath}`);
  }
  
  // Create test results directory
  const testResultsDir = path.join(__dirname, 'test-results');
  if (!fs.existsSync(testResultsDir)) {
    fs.mkdirSync(testResultsDir, { recursive: true });
    console.log('📁 Created test results directory');
  }
  
  // Create artifacts directory
  const artifactsDir = path.join(testResultsDir, 'artifacts');
  if (!fs.existsSync(artifactsDir)) {
    fs.mkdirSync(artifactsDir, { recursive: true });
    console.log('📁 Created test artifacts directory');
  }
  
  // Log test configuration
  console.log('🔧 Test Configuration:');
  console.log(`   - Workers: ${config.workers || 'default'}`);
  console.log(`   - Retries: ${config.retries}`);
  console.log(`   - Timeout: ${config.timeout}ms`);
  console.log(`   - Headless: ${process.env.CI ? 'true' : 'false'}`);
  
  console.log('✅ Global setup completed');
}

module.exports = globalSetup;
