# Megisto Browser - Development Environment Setup Script
# This script sets up the Windows development environment for building Megisto Browser

param(
    [switch]$SkipDownloads,
    [switch]$Verbose
)

$ErrorActionPreference = "Stop"

Write-Host "=== Megisto Browser Development Environment Setup ===" -ForegroundColor Green

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "This script must be run as Administrator. Please restart PowerShell as Administrator and try again."
    exit 1
}

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Function to download and install a package
function Install-Package($name, $url, $installer, $checkCommand) {
    Write-Host "Checking $name..." -ForegroundColor Yellow
    
    if (Test-Command $checkCommand) {
        Write-Host "$name is already installed." -ForegroundColor Green
        return
    }
    
    if ($SkipDownloads) {
        Write-Warning "$name is not installed but -SkipDownloads was specified."
        return
    }
    
    Write-Host "Installing $name..." -ForegroundColor Yellow
    
    $tempFile = Join-Path $env:TEMP $installer
    
    try {
        Invoke-WebRequest -Uri $url -OutFile $tempFile -UseBasicParsing
        Start-Process -FilePath $tempFile -Wait
        Remove-Item $tempFile -Force
        Write-Host "$name installed successfully." -ForegroundColor Green
    }
    catch {
        Write-Error "Failed to install $name`: $($_.Exception.Message)"
    }
}

# Check Windows version
$osVersion = [System.Environment]::OSVersion.Version
if ($osVersion.Major -lt 10) {
    Write-Error "Windows 10 or later is required. Current version: $($osVersion.ToString())"
    exit 1
}

Write-Host "Windows version check passed: $($osVersion.ToString())" -ForegroundColor Green

# Install Chocolatey if not present
if (-not (Test-Command "choco")) {
    Write-Host "Installing Chocolatey..." -ForegroundColor Yellow
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    refreshenv
}

# Install required tools using Chocolatey
$tools = @(
    @{ Name = "Git"; Package = "git"; Command = "git" },
    @{ Name = "Python 3.10+"; Package = "python"; Command = "python" },
    @{ Name = "Node.js"; Package = "nodejs"; Command = "node" },
    @{ Name = "Rust"; Package = "rust"; Command = "rustc" },
    @{ Name = "Mercurial"; Package = "hg"; Command = "hg" }
)

foreach ($tool in $tools) {
    Write-Host "Checking $($tool.Name)..." -ForegroundColor Yellow
    
    if (-not (Test-Command $tool.Command)) {
        Write-Host "Installing $($tool.Name)..." -ForegroundColor Yellow
        choco install $tool.Package -y
        refreshenv
    } else {
        Write-Host "$($tool.Name) is already installed." -ForegroundColor Green
    }
}

# Install Visual Studio Build Tools
Write-Host "Checking Visual Studio Build Tools..." -ForegroundColor Yellow
$vsWhere = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere.exe"
if (Test-Path $vsWhere) {
    $vsInstallations = & $vsWhere -products * -requires Microsoft.VisualStudio.Component.VC.Tools.x86.x64 -format json | ConvertFrom-Json
    if ($vsInstallations.Count -gt 0) {
        Write-Host "Visual Studio Build Tools found." -ForegroundColor Green
    } else {
        Write-Warning "Visual Studio Build Tools not found. Please install Visual Studio Build Tools with C++ workload."
    }
} else {
    Write-Warning "Visual Studio Build Tools not found. Please install Visual Studio Build Tools with C++ workload."
}

# Install MozillaBuild
Write-Host "Checking MozillaBuild..." -ForegroundColor Yellow
$mozillaBuildPath = "C:\mozilla-build"
if (Test-Path $mozillaBuildPath) {
    Write-Host "MozillaBuild found at $mozillaBuildPath" -ForegroundColor Green
} else {
    if (-not $SkipDownloads) {
        Write-Host "Installing MozillaBuild..." -ForegroundColor Yellow
        $mozillaBuildUrl = "https://ftp.mozilla.org/pub/mozilla/libraries/win32/MozillaBuildSetup-Latest.exe"
        $tempFile = Join-Path $env:TEMP "MozillaBuildSetup.exe"
        
        try {
            Invoke-WebRequest -Uri $mozillaBuildUrl -OutFile $tempFile -UseBasicParsing
            Start-Process -FilePath $tempFile -Wait
            Remove-Item $tempFile -Force
            Write-Host "MozillaBuild installed successfully." -ForegroundColor Green
        }
        catch {
            Write-Error "Failed to install MozillaBuild: $($_.Exception.Message)"
        }
    } else {
        Write-Warning "MozillaBuild not found but -SkipDownloads was specified."
    }
}

# Verify installations
Write-Host "`n=== Verification ===" -ForegroundColor Green

$verifications = @(
    @{ Name = "Git"; Command = "git --version" },
    @{ Name = "Python"; Command = "python --version" },
    @{ Name = "Node.js"; Command = "node --version" },
    @{ Name = "npm"; Command = "npm --version" },
    @{ Name = "Rust"; Command = "rustc --version" },
    @{ Name = "Cargo"; Command = "cargo --version" },
    @{ Name = "Mercurial"; Command = "hg --version" }
)

foreach ($verification in $verifications) {
    try {
        $output = Invoke-Expression $verification.Command 2>$null
        Write-Host "$($verification.Name): $output" -ForegroundColor Green
    }
    catch {
        Write-Warning "$($verification.Name): Not found or not working"
    }
}

# Create development directories
Write-Host "`n=== Creating Development Directories ===" -ForegroundColor Green
$devDirs = @("build", "dist", "logs")
foreach ($dir in $devDirs) {
    $fullPath = Join-Path (Get-Location) $dir
    if (-not (Test-Path $fullPath)) {
        New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
        Write-Host "Created directory: $dir" -ForegroundColor Green
    }
}

# Set up environment variables
Write-Host "`n=== Setting Environment Variables ===" -ForegroundColor Green
$envVars = @{
    "MEGISTO_ROOT" = Get-Location
    "MOZCONFIG" = Join-Path (Get-Location) "mozconfig"
}

foreach ($envVar in $envVars.GetEnumerator()) {
    [Environment]::SetEnvironmentVariable($envVar.Key, $envVar.Value, "User")
    Write-Host "Set $($envVar.Key) = $($envVar.Value)" -ForegroundColor Green
}

Write-Host "`n=== Setup Complete ===" -ForegroundColor Green
Write-Host "Development environment setup completed successfully!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Run .\tools\clone-firefox.ps1 to clone Firefox source" -ForegroundColor White
Write-Host "2. Run .\tools\build.ps1 to build Megisto Browser" -ForegroundColor White
Write-Host "3. Restart your terminal to ensure all environment variables are loaded" -ForegroundColor White
