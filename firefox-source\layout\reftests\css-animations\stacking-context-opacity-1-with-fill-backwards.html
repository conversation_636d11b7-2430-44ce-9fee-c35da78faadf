<!DOCTYPE html>
<title>
Opacity animation does not destroy stacking context when the animation
has finished but has fill:backwards
</title>
<style>
span {
  height: 100px;
  width: 100px;
  position: fixed;
  background: green;
  top: 50px;
}
@keyframes Opaque {
  from, to { opacity: 1 }
}
#test {
  width: 100px; height: 100px;
  background: blue;
  animation: Opaque 100s 100s backwards;
}
</style>
<span></span>
<div id="test"></div>
