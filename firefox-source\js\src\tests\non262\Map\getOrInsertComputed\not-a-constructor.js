// |reftest| shell-option(--enable-upsert) skip-if(!Map.prototype.getOrInsertComputed)
// Copyright (C) 2020 <PERSON>. All rights reserved.
// Copyright (C) 2024 <PERSON>, <PERSON>. All rights reserved.
// This code is governed by the BSD license found in the LICENSE file.

/*---
esid: proposal-upsert
description: >
  Map.prototype.getOrInsertComputed does not implement [[Construct]], is not new-able
info: |
  ECMAScript Function Objects

  Built-in function objects that are not identified as constructors do not
  implement the [[Construct]] internal method unless otherwise specified in
  the description of a particular function.

  sec-evaluatenew

  ...
  7. If IsConstructor(constructor) is false, throw a TypeError exception.
  ...
includes: [isConstructor.js]
features: [Reflect.construct, Map, arrow-function]
---*/

assertEq(isConstructor(Map.prototype.getOrInsertComputed), false, 'isConstructor(Map.prototype.getOrInsertComputed) must return false');

assertThrowsInstanceOf(() => {
  let m = new Map(); new m.getOrInsertComputed();
}, TypeError);

reportCompare(0, 0);
