[package]
name = "xpcom"
version = "0.1.0"
authors = ["<PERSON><PERSON> <<EMAIL>>"]
edition = "2018"
license = "MPL-2.0"

[dependencies]
cstr = "0.2"
libc = "0.2"
nsstring = { path = "../nsstring" }
nserror = { path = "../nserror" }
threadbound = "0.1"
xpcom_macros = { path = "xpcom_macros" }
thin-vec = { version = "0.2.1", features = ["gecko-ffi"] }
mozbuild = "0.1"

[features]
thread_sanitizer = []
gecko_refcount_logging = []
