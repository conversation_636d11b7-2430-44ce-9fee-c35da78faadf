# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

TEST_DIRS += [
    "gtest",
]

if CONFIG["OS_ARCH"] == "WINNT":
    TEST_DIRS += ["windows"]

if CONFIG["OS_TARGET"] == "Linux":
    CppUnitTests(
        [
            "TestMemoryPressureWatcherLinux",
        ]
    )

EXPORTS.testing += [
    "TestHarness.h",
]

test_progs = [
    "TestArguments",
    "TestBlockingProcess",
    "TestPRIntN",
    "TestQuickReturn",
    "TestUnicodeArguments",
]
SimplePrograms(test_progs)

USE_LIBS += ["mozglue"]

if CONFIG["MOZ_ASAN"] or CONFIG["MOZ_UBSAN"] or CONFIG["MOZ_TSAN"]:
    USE_LIBS += ["sanitizer-options"]

XPCSHELL_TESTS_MANIFESTS += ["unit/xpcshell.toml"]

if CONFIG["COMPILE_ENVIRONMENT"]:
    TEST_HARNESS_FILES.xpcshell.xpcom.tests.unit += [
        "!%s%s" % (f, CONFIG["BIN_SUFFIX"]) for f in test_progs
    ]

XPIDL_MODULE = "xpcomtest"
XPIDL_SOURCES += [
    "NotXPCOMTest.idl",
]

LOCAL_INCLUDES += [
    "../base",
    "../ds",
]

CRASHTEST_MANIFESTS += ["crashtests/crashtests.list"]
