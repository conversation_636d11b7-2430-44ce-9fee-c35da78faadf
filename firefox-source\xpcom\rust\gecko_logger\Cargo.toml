[package]
name = "gecko_logger"
version = "0.1.0"
authors = ["<EMAIL>"]
edition = "2018"
license = "MPL-2.0"

[dependencies]
lazy_static = "1"
log = {version = "0.4", features = ["release_max_level_info"]}
env_logger = {version = "0.10", default-features = false, features = ["color"]} # disable `regex` to reduce code size
app_services_logger = { path = "../../../services/common/app_services_logger" }
