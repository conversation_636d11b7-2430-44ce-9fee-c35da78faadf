/* -*- Mode: IDL; tab-width: 2; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */


#include "nsISupports.idl"

[scriptable, uuid(a6dcc76e-9f62-4cc1-a470-b483a1a6f096)]
interface nsIBlocklistService : nsISupports
{
  // Indicates that the item does not appear in the blocklist.
  const unsigned long STATE_NOT_BLOCKED = 0;
  // Indicates that the item is in the blocklist but the problem is not severe
  // enough to warant forcibly blocking.
  const unsigned long STATE_SOFTBLOCKED = 1;
  // Indicates that the item should be blocked and never used.
  const unsigned long STATE_BLOCKED     = 2;

  // Unused; Please increment if we add more blocklist states.
  const unsigned long STATE_MAX = 3;

  readonly attribute boolean isLoaded;
};
