# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

# flake8: noqa

import sys

from Atom import (
    Atom,
    InheritingAnonBox<PERSON>tom,
    NonInheritingAnonBox<PERSON>tom,
    PseudoElement<PERSON>tom,
)
from HTMLAtoms import HTML_PARSER_ATOMS

# Static atom definitions, used to generate nsGkAtomList.h.
#
# Each atom is defined by a call to <PERSON><PERSON>, Pseudo<PERSON>lement<PERSON>tom,
# NonInheritingAnonBoxAtom or InheritingAnonBox<PERSON>tom.
#
# The first argument is the atom's identifier.
# The second argument is the atom's string value.
#
# Please keep the Atom() definitions on one line as this is parsed by the
#   htmlparser: parser/html/java/htmlparser
# Please keep "START ATOMS" and "END ATOMS" comments as the parser uses them.
#
# It is not possible to conditionally define static atoms with #ifdef etc.
# fmt: off
STATIC_ATOMS = [
    # START ATOMS
    # --------------------------------------------------------------------------
    # Generic atoms
    # --------------------------------------------------------------------------
    Atom("SystemPrincipal", "[System Principal]"),
    Atom("_empty", ""),
    Atom("_0", "0"),
    Atom("_1", "1"),
    Atom("mozframetype", "mozframetype"),
    Atom("_moz_abspos", "_moz_abspos"),
    Atom("_moz_activated", "_moz_activated"),
    Atom("_moz_anonclass", "_moz_anonclass"),
    Atom("_moz_resizing", "_moz_resizing"),
    Atom("moztype", "_moz-type"),
    Atom("mozdirty", "_moz_dirty"),
    Atom("mozdisallowselectionprint", "mozdisallowselectionprint"),
    Atom("mozdonotsend", "moz-do-not-send"),
    Atom("mozfwcontainer", "moz-forward-container"),  # Used by MailNews.
    Atom("mozgeneratedcontentbefore", "_moz_generated_content_before"),
    Atom("mozgeneratedcontentafter", "_moz_generated_content_after"),
    Atom("mozgeneratedcontentmarker", "_moz_generated_content_marker"),
    Atom("mozgeneratedcontentimage", "_moz_generated_content_image"),
    Atom("mozquote", "_moz_quote"),
    Atom("mozsignature", "moz-signature"),  # Used by MailNews.
    Atom("_moz_bullet_font", "-moz-bullet-font"),
    Atom("_moz_is_glyph", "-moz-is-glyph"),
    Atom("_moz_in_android_pip_mode", "-moz-in-android-pip-mode"),
    Atom("_moz_original_size", "_moz_original_size"),
    Atom("_moz_print_preview", "-moz-print-preview"),
    Atom("menuactive", "_moz-menuactive"),
    Atom("_poundDefault", "#default"),
    Atom("_asterisk", "*"),
    Atom("a", "a"),
    Atom("abbr", "abbr"),
    Atom("abort", "abort"),
    Atom("above", "above"),
    Atom("acceltext", "acceltext"),
    Atom("accept", "accept"),
    Atom("acceptcharset", "accept-charset"),
    Atom("accessiblenode", "accessible-node"),
    Atom("accesskey", "accesskey"),
    Atom("acronym", "acronym"),
    Atom("action", "action"),
    Atom("active", "active"),
    Atom("activateontab", "activateontab"),
    Atom("actuate", "actuate"),
    Atom("address", "address"),
    Atom("adoptedsheetclones", "adoptedsheetclones"),
    Atom("after", "after"),
    Atom("align", "align"),
    Atom("alink", "alink"),
    Atom("all", "all"),
    Atom("allow", "allow"),
    Atom("allowdownloads", "allow-downloads"),
    Atom("allowevents", "allowevents"),
    Atom("allowforms", "allow-forms"),
    Atom("allowfullscreen", "allowfullscreen"),
    Atom("allowmodals", "allow-modals"),
    Atom("alloworientationlock", "allow-orientation-lock"),
    Atom("allowpointerlock", "allow-pointer-lock"),
    Atom("allowpopupstoescapesandbox", "allow-popups-to-escape-sandbox"),
    Atom("allowpopups", "allow-popups"),
    Atom("allowpresentation", "allow-presentation"),
    Atom("allowstorageaccessbyuseractivatetion", "allow-storage-access-by-user-activation"),
    Atom("allowsameorigin", "allow-same-origin"),
    Atom("allowscripts", "allow-scripts"),
    Atom("allowscriptstoclose", "allowscriptstoclose"),
    Atom("allowtopnavigation", "allow-top-navigation"),
    Atom("allowtopnavigationbyuseractivation", "allow-top-navigation-by-user-activation"),
    Atom("allowtopnavigationcustomprotocols", "allow-top-navigation-to-custom-protocols"),
    Atom("allowuntrusted", "allowuntrusted"),
    Atom("alt", "alt"),
    Atom("alternate", "alternate"),
    Atom("always", "always"),
    Atom("ancestor", "ancestor"),
    Atom("ancestorOrSelf", "ancestor-or-self"),
    Atom("anchor", "anchor"),
    Atom("_and", "and"),
    Atom("animations", "animations"),
    Atom("anonid", "anonid"),
    Atom("anonlocation", "anonlocation"),
    Atom("any", "any"),
    Atom("any_hover", "any-hover"),
    Atom("any_pointer", "any-pointer"),
    Atom("applet", "applet"),
    Atom("applyImports", "apply-imports"),
    Atom("applyTemplates", "apply-templates"),
    Atom("archive", "archive"),
    Atom("area", "area"),
    Atom("aria", "aria"),
    Atom("aria_activedescendant", "aria-activedescendant"),
    Atom("aria_atomic", "aria-atomic"),
    Atom("aria_autocomplete", "aria-autocomplete"),
    Atom("aria_braillelabel", "aria-braillelabel"),
    Atom("aria_brailleroledescription", "aria-brailleroledescription"),
    Atom("aria_busy", "aria-busy"),
    Atom("aria_checked", "aria-checked"),
    Atom("aria_controls", "aria-controls"),
    Atom("aria_current", "aria-current"),
    Atom("aria_describedby", "aria-describedby"),
    Atom("aria_description", "aria-description"),
    Atom("aria_disabled", "aria-disabled"),
    Atom("aria_dropeffect", "aria-dropeffect"),
    Atom("aria_expanded", "aria-expanded"),
    Atom("aria_flowto", "aria-flowto"),
    Atom("aria_haspopup", "aria-haspopup"),
    Atom("aria_hidden", "aria-hidden"),
    Atom("aria_invalid", "aria-invalid"),
    Atom("aria_labelledby", "aria-labelledby"),
    Atom("aria_level", "aria-level"),
    Atom("aria_live", "aria-live"),
    Atom("aria_multiline", "aria-multiline"),
    Atom("aria_multiselectable", "aria-multiselectable"),
    Atom("aria_owns", "aria-owns"),
    Atom("aria_posinset", "aria-posinset"),
    Atom("aria_pressed", "aria-pressed"),
    Atom("aria_readonly", "aria-readonly"),
    Atom("aria_relevant", "aria-relevant"),
    Atom("aria_required", "aria-required"),
    Atom("aria_selected", "aria-selected"),
    Atom("aria_setsize", "aria-setsize"),
    Atom("aria_sort", "aria-sort"),
    Atom("aria_valuemax", "aria-valuemax"),
    Atom("aria_valuemin", "aria-valuemin"),
    Atom("aria_valuenow", "aria-valuenow"),
    Atom("arrow", "arrow"),
    Atom("article", "article"),
    Atom("as", "as"),
    Atom("ascending", "ascending"),
    Atom("aside", "aside"),
    Atom("aspectRatio", "aspect-ratio"),
    Atom("async", "async"),
    Atom("attribute", "attribute"),
    Atom("attributes", "attributes"),
    Atom("attributeSet", "attribute-set"),
    Atom("_auto", "auto"),
    Atom("autocapitalize", "autocapitalize"),
    Atom("autocheck", "autocheck"),
    Atom("autocomplete", "autocomplete"),
    Atom("autocomplete_richlistbox", "autocomplete-richlistbox"),
    Atom("autofocus", "autofocus"),
    Atom("autoplay", "autoplay"),
    Atom("axis", "axis"),
    Atom("b", "b"),
    Atom("background", "background"),
    Atom("badge", "badge"),
    Atom("bar", "bar"),
    Atom("base", "base"),
    Atom("basefont", "basefont"),
    Atom("baseline", "baseline"),
    Atom("bdi", "bdi"),
    Atom("bdo", "bdo"),
    Atom("before", "before"),
    Atom("behavior", "behavior"),
    Atom("below", "below"),
    Atom("bgcolor", "bgcolor"),
    Atom("bgsound", "bgsound"),
    Atom("big", "big"),
    Atom("binding", "binding"),
    Atom("bindings", "bindings"),
    Atom("bindToUntrustedContent", "bindToUntrustedContent"),
    Atom("black", "black"),
    Atom("block", "block"),
    Atom("block_size", "block-size"),
    Atom("blocking", "blocking"),
    Atom("blockquote", "blockquote"),
    Atom("blur", "blur"),
    Atom("body", "body"),
    Atom("boolean", "boolean"),
    Atom("border", "border"),
    Atom("bordercolor", "bordercolor"),
    Atom("both", "both"),
    Atom("bottom", "bottom"),
    Atom("bottomend", "bottomend"),
    Atom("bottomstart", "bottomstart"),
    Atom("bottomleft", "bottomleft"),
    Atom("bottommargin", "bottommargin"),
    Atom("bottomright", "bottomright"),
    Atom("box", "box"),
    Atom("br", "br"),
    Atom("browser", "browser"),
    Atom("button", "button"),
    Atom("callTemplate", "call-template"),
    Atom("canvas", "canvas"),
    Atom("caption", "caption"),
    Atom("captionBox", "caption-box"),
    Atom("capture", "capture"),
    Atom("caseOrder", "case-order"),
    Atom("cdataSectionElements", "cdata-section-elements"),
    Atom("ceiling", "ceiling"),
    Atom("cell", "cell"),
    Atom("cellpadding", "cellpadding"),
    Atom("cellspacing", "cellspacing"),
    Atom("center", "center"),
    Atom("change", "change"),
    Atom("_char", "char"),
    Atom("characterData", "characterData"),
    Atom("charcode", "charcode"),
    Atom("charoff", "charoff"),
    Atom("charset", "charset"),
    Atom("checkbox", "checkbox"),
    Atom("checkboxLabel", "checkbox-label"),
    Atom("checked", "checked"),
    Atom("child", "child"),
    Atom("children", "children"),
    Atom("childList", "childList"),
    Atom("child_item_count", "child-item-count"),
    Atom("choose", "choose"),
    Atom("customtitlebar", "customtitlebar"),
    Atom("exposeToUntrustedContent", "exposeToUntrustedContent"),
    Atom("circ", "circ"),
    Atom("circle", "circle"),
    Atom("cite", "cite"),
    Atom("_class", "class"),
    Atom("classid", "classid"),
    Atom("clear", "clear"),
    Atom("click", "click"),
    Atom("clickcount", "clickcount"),
    Atom("movetoclick", "movetoclick"),
    Atom("clip", "clip"),
    Atom("close", "close"),
    Atom("closed", "closed"),
    Atom("closedby", "closedby"),
    Atom("closemenu", "closemenu"),
    Atom("code", "code"),
    Atom("codebase", "codebase"),
    Atom("codetype", "codetype"),
    Atom("col", "col"),
    Atom("colgroup", "colgroup"),
    Atom("collapse", "collapse"),
    Atom("collapsed", "collapsed"),
    Atom("color", "color"),
    Atom("color_gamut", "color-gamut"),
    Atom("color_index", "color-index"),
    Atom("color_scheme", "color-scheme"),
    Atom("cols", "cols"),
    Atom("colspan", "colspan"),
    Atom("combobox", "combobox"),
    Atom("command", "command"),
    Atom("commandfor", "commandfor"),
    Atom("commandupdater", "commandupdater"),
    Atom("comment", "comment"),
    Atom("compact", "compact"),
    Atom("concat", "concat"),
    Atom("constructor", "constructor"),
    Atom("consumeoutsideclicks", "consumeoutsideclicks"),
    Atom("container", "container"),
    Atom("contains", "contains"),
    Atom("content", "content"),
    Atom("contenteditable", "contenteditable"),
    Atom("headerContentDisposition", "content-disposition"),
    Atom("headerContentLanguage", "content-language"),
    Atom("contentLocation", "content-location"),
    Atom("headerContentScriptType", "content-script-type"),
    Atom("headerContentStyleType", "content-style-type"),
    Atom("headerContentType", "content-type"),
    Atom("consumeanchor", "consumeanchor"),
    Atom("context", "context"),
    Atom("contextmenu", "contextmenu"),
    Atom("control", "control"),
    Atom("controls", "controls"),
    Atom("coords", "coords"),
    Atom("copy", "copy"),
    Atom("copyOf", "copy-of"),
    Atom("count", "count"),
    Atom("crop", "crop"),
    Atom("crossorigin", "crossorigin"),
    Atom("curpos", "curpos"),
    Atom("current", "current"),
    Atom("cutoutregion", "cutoutregion"),
    Atom("cycler", "cycler"),
    Atom("dashed", "dashed"),
    Atom("data", "data"),
    Atom("dataAtShortcutkeys", "data-at-shortcutkeys"),
    Atom("datalist", "datalist"),
    Atom("datal10nid", "data-l10n-id"),
    Atom("datal10nargs", "data-l10n-args"),
    Atom("datal10nattrs", "data-l10n-attrs"),
    Atom("datal10nname", "data-l10n-name"),
    Atom("datal10nsync", "data-l10n-sync"),
    Atom("dataType", "data-type"),
    Atom("dateTime", "date-time"),
    Atom("date", "date"),
    Atom("datetime", "datetime"),
    Atom("datetime_local", "datetime-local"),
    Atom("datetimeInputBoxWrapper", "datetime-input-box-wrapper"),
    Atom("dd", "dd"),
    Atom("decimal", "decimal"),
    Atom("decimalFormat", "decimal-format"),
    Atom("decimalSeparator", "decimal-separator"),
    Atom("declare", "declare"),
    Atom("decoderDoctor", "decoder-doctor"),
    Atom("decoding", "decoding"),
    Atom("decrement", "decrement"),
    Atom("_default", "default"),
    Atom("headerDefaultStyle", "default-style"),
    Atom("defer", "defer"),
    Atom("del", "del"),
    Atom("delegatesanchor", "delegatesanchor"),
    Atom("deletion", "deletion"),
    Atom("deprecation", "deprecation"),
    Atom("descendant", "descendant"),
    Atom("descendantOrSelf", "descendant-or-self"),
    Atom("descending", "descending"),
    Atom("description", "description"),
    Atom("destructor", "destructor"),
    Atom("details", "details"),
    Atom("deviceAspectRatio", "device-aspect-ratio"),
    Atom("deviceHeight", "device-height"),
    Atom("devicePixelRatio", "device-pixel-ratio"),
    Atom("deviceWidth", "device-width"),
    Atom("dfn", "dfn"),
    Atom("dialog", "dialog"),
    Atom("difference", "difference"),
    Atom("digit", "digit"),
    Atom("dir", "dir"),
    Atom("directory", "directory"),
    Atom("dirname", "dirname"),
    Atom("disableOutputEscaping", "disable-output-escaping"),
    Atom("disabled", "disabled"),
    Atom("disableglobalhistory", "disableglobalhistory"),
    Atom("disablehistory", "disablehistory"),
    Atom("disablefullscreen", "disablefullscreen"),
    Atom("disablepictureinpicture", "disablepictureinpicture"),
    Atom("disclosure_closed", "disclosure-closed"),
    Atom("disclosure_open", "disclosure-open"),
    Atom("display", "display"),
    Atom("displayMode", "display-mode"),
    Atom("distinct", "distinct"),
    Atom("div", "div"),
    Atom("dl", "dl"),
    Atom("docAbstract", "doc-abstract"),
    Atom("docAcknowledgments", "doc-acknowledgments"),
    Atom("docAfterword", "doc-afterword"),
    Atom("docAppendix", "doc-appendix"),
    Atom("docBacklink", "doc-backlink"),
    Atom("docBiblioentry", "doc-biblioentry"),
    Atom("docBibliography", "doc-bibliography"),
    Atom("docBiblioref", "doc-biblioref"),
    Atom("docChapter", "doc-chapter"),
    Atom("docColophon", "doc-colophon"),
    Atom("docConclusion", "doc-conclusion"),
    Atom("docCover", "doc-cover"),
    Atom("docCredit", "doc-credit"),
    Atom("docCredits", "doc-credits"),
    Atom("docDedication", "doc-dedication"),
    Atom("docEndnote", "doc-endnote"),
    Atom("docEndnotes", "doc-endnotes"),
    Atom("docEpigraph", "doc-epigraph"),
    Atom("docEpilogue", "doc-epilogue"),
    Atom("docErrata", "doc-errata"),
    Atom("docExample", "doc-example"),
    Atom("docFootnote", "doc-footnote"),
    Atom("docForeword", "doc-foreword"),
    Atom("docGlossary", "doc-glossary"),
    Atom("docGlossref", "doc-glossref"),
    Atom("docIndex", "doc-index"),
    Atom("docIntroduction", "doc-introduction"),
    Atom("docNoteref", "doc-noteref"),
    Atom("docNotice", "doc-notice"),
    Atom("docPagebreak", "doc-pagebreak"),
    Atom("docPagefooter", "doc-pagefooter"),
    Atom("docPageheader", "doc-pageheader"),
    Atom("docPagelist", "doc-pagelist"),
    Atom("docPart", "doc-part"),
    Atom("docPreface", "doc-preface"),
    Atom("docPrologue", "doc-prologue"),
    Atom("docPullquote", "doc-pullquote"),
    Atom("docQna", "doc-qna"),
    Atom("docSubtitle", "doc-subtitle"),
    Atom("docTip", "doc-tip"),
    Atom("docToc", "doc-toc"),
    Atom("doctypePublic", "doctype-public"),
    Atom("doctypeSystem", "doctype-system"),
    Atom("document", "document"),
    Atom("down", "down"),
    Atom("download", "download"),
    Atom("drag", "drag"),
    Atom("draggable", "draggable"),
    Atom("dragging", "dragging"),
    Atom("dragSession", "dragSession"),
    Atom("drawtitle", "drawtitle"),
    Atom("dropAfter", "dropAfter"),
    Atom("dropBefore", "dropBefore"),
    Atom("dropOn", "dropOn"),
    Atom("dropMarker", "dropmarker"),
    Atom("dt", "dt"),
    Atom("e", "e"),
    Atom("editable", "editable"),
    Atom("editing", "editing"),
    Atom("editor", "editor"),
    Atom("element", "element"),
    Atom("elementAvailable", "element-available"),
    Atom("elements", "elements"),
    Atom("em", "em"),
    Atom("embed", "embed"),
    Atom("emphasis", "emphasis"),
    Atom("empty", "empty"),
    Atom("encoding", "encoding"),
    Atom("enctype", "enctype"),
    Atom("end", "end"),
    Atom("endEvent", "endEvent"),
    Atom("enterkeyhint", "enterkeyhint"),
    Atom("error", "error"),
    Atom("ethiopic_numeric", "ethiopic-numeric"),
    Atom("even", "even"),
    Atom("event", "event"),
    Atom("events", "events"),
    Atom("excludeResultPrefixes", "exclude-result-prefixes"),
    Atom("exportparts", "exportparts"),
    Atom("explicit_name", "explicit-name"),
    Atom("extends", "extends"),
    Atom("extensionElementPrefixes", "extension-element-prefixes"),
    Atom("face", "face"),
    Atom("fallback", "fallback"),
    Atom("_false", "false"),
    Atom("farthest", "farthest"),
    Atom("featurePolicyViolation", "feature-policy-violation"),
    Atom("fetchpriority", "fetchpriority"),
    Atom("field", "field"),
    Atom("fieldset", "fieldset"),
    Atom("file", "file"),
    Atom("figcaption", "figcaption"),
    Atom("figure", "figure"),
    Atom("findbar", "findbar"),
    Atom("firstColumn", "first-column"),
    Atom("firstInput", "first-input"),
    Atom("fixed", "fixed"),
    Atom("flags", "flags"),
    Atom("flex", "flex"),
    Atom("flip", "flip"),
    Atom("floor", "floor"),
    Atom("flowlength", "flowlength"),
    Atom("focus", "focus"),
    Atom("focused", "focused"),
    Atom("followanchor", "followanchor"),
    Atom("following", "following"),
    Atom("followingSibling", "following-sibling"),
    Atom("font", "font"),
    Atom("footer", "footer"),
    Atom("_for", "for"),
    Atom("forEach", "for-each"),
    Atom("forcedColors", "forced-colors"),
    Atom("invertedColors", "inverted-colors"),
    Atom("forceOwnRefreshDriver", "forceOwnRefreshDriver"),
    Atom("form", "form"),
    Atom("formaction", "formaction"),
    Atom("format", "format"),
    Atom("formatNumber", "format-number"),
    Atom("formenctype", "formenctype"),
    Atom("formmethod", "formmethod"),
    Atom("formnovalidate", "formnovalidate"),
    Atom("formtarget", "formtarget"),
    Atom("frame", "frame"),
    Atom("frameborder", "frameborder"),
    Atom("frameset", "frameset"),
    Atom("from", "from"),
    Atom("fullscreenchange", "fullscreenchange"),
    Atom("fullscreenerror", "fullscreenerror"),
    Atom("functionAvailable", "function-available"),
    Atom("generateId", "generate-id"),
    Atom("generic", "generic"),
    Atom("getter", "getter"),
    Atom("graphicsDocument", "graphics-document"),
    Atom("graphicsObject", "graphics-object"),
    Atom("graphicsSymbol", "graphics-symbol"),
    Atom("grid", "grid"),
    Atom("group", "group"),
    Atom("groups", "groups"),
    Atom("groupbox", "groupbox"),
    Atom("groupingSeparator", "grouping-separator"),
    Atom("groupingSize", "grouping-size"),
    Atom("grow", "grow"),
    Atom("h1", "h1"),
    Atom("h2", "h2"),
    Atom("h3", "h3"),
    Atom("h4", "h4"),
    Atom("h5", "h5"),
    Atom("h6", "h6"),
    Atom("hairline", "hairline"),
    Atom("handheldFriendly", "HandheldFriendly"),
    Atom("handler", "handler"),
    Atom("handlers", "handlers"),
    Atom("HARD", "HARD"),
    Atom("hasSameNode", "has-same-node"),
    Atom("hbox", "hbox"),
    Atom("head", "head"),
    Atom("header", "header"),
    Atom("headers", "headers"),
    Atom("hebrew", "hebrew"),
    Atom("height", "height"),
    Atom("hgroup", "hgroup"),
    Atom("hidden", "hidden"),
    Atom("hidechrome", "hidechrome"),
    Atom("hidecolumnpicker", "hidecolumnpicker"),
    Atom("hide_popover", "hide-popover"),
    Atom("high", "high"),
    Atom("highest", "highest"),
    Atom("horizontal", "horizontal"),
    Atom("hover", "hover"),
    Atom("hr", "hr"),
    Atom("href", "href"),
    Atom("hreflang", "hreflang"),
    Atom("hsides", "hsides"),
    Atom("hspace", "hspace"),
    Atom("html", "html"),
    Atom("httpEquiv", "http-equiv"),
    Atom("i", "i"),
    Atom("icon", "icon"),
    Atom("id", "id"),
    Atom("_if", "if"),
    Atom("iframe", "iframe"),
    Atom("ignorekeys", "ignorekeys"),
    Atom("ignoreuserfocus", "ignoreuserfocus"),
    Atom("image", "image"),
    Atom("imageClickedPoint", "image-clicked-point"),
    Atom("imagesizes", "imagesizes"),
    Atom("imagesrcset", "imagesrcset"),
    Atom("img", "img"),
    Atom("implementation", "implementation"),
    Atom("implements", "implements"),
    Atom("import", "import"),
    Atom("include", "include"),
    Atom("includes", "includes"),
    Atom("incontentshell", "incontentshell"),
    Atom("increment", "increment"),
    Atom("indent", "indent"),
    Atom("indeterminate", "indeterminate"),
    Atom("index", "index"),
    Atom("inert", "inert"),
    Atom("innerHTML", "innerHTML"),
    Atom("innerText", "innerText"),
    Atom("infinity", "infinity"),
    Atom("inherits", "inherits"),
    Atom("inheritOverflow", "inherit-overflow"),
    Atom("inheritstyle", "inheritstyle"),
    Atom("initial_scale", "initial-scale"),
    Atom("input", "input"),
    Atom("inputmode", "inputmode"),
    Atom("ins", "ins"),
    Atom("insertafter", "insertafter"),
    Atom("insertbefore", "insertbefore"),
    Atom("insertion", "insertion"),
    Atom("integer", "integer"),
    Atom("integrity", "integrity"),
    Atom("interactive_widget", "interactive-widget"),
    Atom("internal", "internal"),
    Atom("internals", "internals"),
    Atom("intersection", "intersection"),
    Atom("is", "is"),
    Atom("ismap", "ismap"),
    Atom("ispopup", "ispopup"),
    Atom("itemid", "itemid"),
    Atom("itemprop", "itemprop"),
    Atom("itemref", "itemref"),
    Atom("itemscope", "itemscope"),
    Atom("itemtype", "itemtype"),
    Atom("japanese_formal", "japanese-formal"),
    Atom("japanese_informal", "japanese-informal"),
    Atom("kbd", "kbd"),
    Atom("keepcurrentinview", "keepcurrentinview"),
    Atom("key", "key"),
    Atom("keycode", "keycode"),
    Atom("keydown", "keydown"),
    Atom("keygen", "keygen"),
    Atom("keypress", "keypress"),
    Atom("keyset", "keyset"),
    Atom("keysystem", "keysystem"),
    Atom("keyup", "keyup"),
    Atom("kind", "kind"),
    Atom("korean_hangul_formal", "korean-hangul-formal"),
    Atom("korean_hanja_formal", "korean-hanja-formal"),
    Atom("korean_hanja_informal", "korean-hanja-informal"),
    Atom("label", "label"),
    Atom("lang", "lang"),
    Atom("language", "language"),
    Atom("last", "last"),
    Atom("layer", "layer"),
    Atom("LayerActivity", "LayerActivity"),
    Atom("layout_guess", "layout-guess"),
    Atom("leading", "leading"),
    Atom("leaf", "leaf"),
    Atom("left", "left"),
    Atom("leftmargin", "leftmargin"),
    Atom("legend", "legend"),
    Atom("length", "length"),
    Atom("letterValue", "letter-value"),
    Atom("level", "level"),
    Atom("lhs", "lhs"),
    Atom("li", "li"),
    Atom("line", "line"),
    Atom("link", "link"),
    Atom("linkset", "linkset"),
    # Atom("list", "list"),  # "list" is present below
    Atom("listbox", "listbox"),
    Atom("listener", "listener"),
    Atom("listheader", "listheader"),
    Atom("listing", "listing"),
    Atom("listitem", "listitem"),
    Atom("load", "load"),
    Atom("loading", "loading"),
    Atom("touchmode", "touchmode"),
    Atom("triggeringprincipal", "triggeringprincipal"),
    Atom("localedir", "localedir"),
    Atom("localName", "local-name"),
    Atom("localization", "localization"),
    Atom("longdesc", "longdesc"),
    Atom("loop", "loop"),
    Atom("low", "low"),
    Atom("lowerFirst", "lower-first"),
    Atom("lowest", "lowest"),
    Atom("lowsrc", "lowsrc"),
    Atom("ltr", "ltr"),
    Atom("main", "main"),
    Atom("map", "map"),
    Atom("manifest", "manifest"),
    Atom("marginBottom", "margin-bottom"),
    Atom("marginLeft", "margin-left"),
    Atom("marginRight", "margin-right"),
    Atom("marginTop", "margin-top"),
    Atom("marginheight", "marginheight"),
    Atom("marginwidth", "marginwidth"),
    Atom("mark", "mark"),
    Atom("marquee", "marquee"),
    Atom("match", "match"),
    Atom("max", "max"),
    Atom("maxheight", "maxheight"),
    Atom("maximum_scale", "maximum-scale"),
    Atom("maxlength", "maxlength"),
    Atom("maxpos", "maxpos"),
    Atom("maxwidth", "maxwidth"),
    Atom("measure", "measure"),
    Atom("media", "media"),
    Atom("mediaType", "media-type"),
    Atom("menu", "menu"),
    Atom("menubar", "menubar"),
    Atom("menucaption", "menucaption"),
    Atom("menugroup", "menugroup"),
    Atom("menuitem", "menuitem"),
    Atom("menulist", "menulist"),
    Atom("menupopup", "menupopup"),
    Atom("menuseparator", "menuseparator"),
    Atom("mesh", "mesh"),
    Atom("message", "message"),
    Atom("meta", "meta"),
    Atom("referrer", "referrer"),
    Atom("referrerpolicy", "referrerpolicy"),
    Atom("renderroot", "renderroot"),
    Atom("headerReferrerPolicy", "referrer-policy"),
    Atom("meter", "meter"),
    Atom("method", "method"),
    Atom("middle", "middle"),
    Atom("min", "min"),
    Atom("minheight", "minheight"),
    Atom("minimum_scale", "minimum-scale"),
    Atom("minlength", "minlength"),
    Atom("minpos", "minpos"),
    Atom("minusSign", "minus-sign"),
    Atom("minwidth", "minwidth"),
    Atom("mixed", "mixed"),
    Atom("messagemanagergroup", "messagemanagergroup"),
    Atom("mod", "mod"),
    Atom("_module", "module"),
    Atom("mode", "mode"),
    Atom("modifiers", "modifiers"),
    Atom("monochrome", "monochrome"),
    Atom("mouseover", "mouseover"),
    Atom("mozAccessiblecaret", "moz-accessiblecaret"),
    Atom("mozCustomContentContainer", "moz-custom-content-container"),
    Atom("mozGrabber", "mozGrabber"),
    Atom("mozNativeAnonymous", "-moz-native-anonymous"),
    Atom("mozprivatebrowsing", "mozprivatebrowsing"),
    Atom("mozResizer", "mozResizer"),
    Atom("mozResizingInfo", "mozResizingInfo"),
    Atom("mozResizingShadow", "mozResizingShadow"),
    Atom("mozTableAddColumnAfter", "mozTableAddColumnAfter"),
    Atom("mozTableAddColumnBefore", "mozTableAddColumnBefore"),
    Atom("mozTableAddRowAfter", "mozTableAddRowAfter"),
    Atom("mozTableAddRowBefore", "mozTableAddRowBefore"),
    Atom("mozTableRemoveRow", "mozTableRemoveRow"),
    Atom("mozTableRemoveColumn", "mozTableRemoveColumn"),
    Atom("moz_opaque", "moz-opaque"),
    Atom("multicol", "multicol"),
    Atom("multiple", "multiple"),
    Atom("muted", "muted"),
    Atom("name", "name"),
    Atom("_namespace", "namespace"),
    Atom("namespaceAlias", "namespace-alias"),
    Atom("namespaceUri", "namespace-uri"),
    Atom("NaN", "NaN"),
    Atom("n", "n"),
    Atom("nav", "nav"),
    Atom("ne", "ne"),
    Atom("never", "never"),
    Atom("_new", "new"),
    Atom("newline", "newline"),
    Atom("nextRemoteTabId", "nextRemoteTabId"),
    Atom("no", "no"),
    Atom("noautofocus", "noautofocus"),
    Atom("noautohide", "noautohide"),
    Atom("norolluponanchor", "norolluponanchor"),
    Atom("noBar", "no-bar"),
    Atom("nobr", "nobr"),
    Atom("nodefaultsrc", "nodefaultsrc"),
    Atom("nodeSet", "node-set"),
    Atom("noembed", "noembed"),
    Atom("noframes", "noframes"),
    Atom("nohref", "nohref"),
    Atom("noinitialselection", "noinitialselection"),
    Atom("nomodule", "nomodule"),
    Atom("nonce", "nonce"),
    Atom("none", "none"),
    Atom("noresize", "noresize"),
    Atom("normal", "normal"),
    Atom("normalizeSpace", "normalize-space"),
    Atom("noscript", "noscript"),
    Atom("noshade", "noshade"),
    Atom("notification", "notification"),
    Atom("novalidate", "novalidate"),
    Atom("_not", "not"),
    Atom("nowrap", "nowrap"),
    Atom("number", "number"),
    Atom("nw", "nw"),
    Atom("object", "object"),
    Atom("objectType", "object-type"),
    Atom("observes", "observes"),
    Atom("odd", "odd"),
    Atom("OFF", "OFF"),
    Atom("ol", "ol"),
    Atom("omitXmlDeclaration", "omit-xml-declaration"),
    Atom("onabort", "onabort"),
    Atom("onmozaccesskeynotfound", "onmozaccesskeynotfound"),
    Atom("onactivate", "onactivate"),
    Atom("onafterprint", "onafterprint"),
    Atom("onafterscriptexecute", "onafterscriptexecute"),
    Atom("onanimationcancel", "onanimationcancel"),
    Atom("onanimationend", "onanimationend"),
    Atom("onanimationiteration", "onanimationiteration"),
    Atom("onanimationstart", "onanimationstart"),
    Atom("onAppCommand", "onAppCommand"),
    Atom("onaudioprocess", "onaudioprocess"),
    Atom("onauxclick", "onauxclick"),
    Atom("onbeforecopy", "onbeforecopy"),
    Atom("onbeforecut", "onbeforecut"),
    Atom("onbeforeinput", "onbeforeinput"),
    Atom("onbeforematch", "onbeforematch"),
    Atom("onbeforepaste", "onbeforepaste"),
    Atom("onbeforeprint", "onbeforeprint"),
    Atom("onbeforescriptexecute", "onbeforescriptexecute"),
    Atom("onbeforeunload", "onbeforeunload"),
    Atom("onblocked", "onblocked"),
    Atom("onblur", "onblur"),
    Atom("onboundschange", "onboundschange"),
    Atom("onbroadcast", "onbroadcast"),
    Atom("onbufferedamountlow", "onbufferedamountlow"),
    Atom("oncached", "oncached"),
    Atom("oncancel", "oncancel"),
    Atom("onchange", "onchange"),
    Atom("onchargingchange", "onchargingchange"),
    Atom("onchargingtimechange", "onchargingtimechange"),
    Atom("onchecking", "onchecking"),
    Atom("onCheckboxStateChange", "onCheckboxStateChange"),
    Atom("onCheckKeyPressEventModel", "onCheckKeyPressEventModel"),
    Atom("onclick", "onclick"),
    Atom("onclose", "onclose"),
    Atom("oncontentvisibilityautostatechange", "oncontentvisibilityautostatechange"),
    Atom("oncommand", "oncommand"),
    Atom("oncommandupdate", "oncommandupdate"),
    Atom("oncomplete", "oncomplete"),
    Atom("oncompositionend", "oncompositionend"),
    Atom("oncompositionstart", "oncompositionstart"),
    Atom("oncompositionupdate", "oncompositionupdate"),
    Atom("onconnect", "onconnect"),
    Atom("onconnectionavailable", "onconnectionavailable"),
    Atom("oncontextmenu", "oncontextmenu"),
    Atom("oncontextlost", "oncontextlost"),
    Atom("oncontextrestored", "oncontextrestored"),
    Atom("oncookiechange", "oncookiechange"),
    Atom("oncopy", "oncopy"),
    Atom("oncut", "oncut"),
    Atom("oncurrententrychange", "oncurrententrychange"),
    Atom("ondblclick", "ondblclick"),
    Atom("ondischargingtimechange", "ondischargingtimechange"),
    Atom("ondispose", "ondispose"),
    Atom("ondownloading", "ondownloading"),
    Atom("onDOMActivate", "onDOMActivate"),
    Atom("onDOMAttrModified", "onDOMAttrModified"),
    Atom("onDOMCharacterDataModified", "onDOMCharacterDataModified"),
    Atom("onDOMFocusIn", "onDOMFocusIn"),
    Atom("onDOMFocusOut", "onDOMFocusOut"),
    Atom("onDOMMouseScroll", "onDOMMouseScroll"),
    Atom("onDOMNodeInserted", "onDOMNodeInserted"),
    Atom("onDOMNodeInsertedIntoDocument", "onDOMNodeInsertedIntoDocument"),
    Atom("onDOMNodeRemoved", "onDOMNodeRemoved"),
    Atom("onDOMNodeRemovedFromDocument", "onDOMNodeRemovedFromDocument"),
    Atom("onDOMSubtreeModified", "onDOMSubtreeModified"),
    Atom("ondata", "ondata"),
    Atom("ondrag", "ondrag"),
    Atom("ondragdrop", "ondragdrop"),
    Atom("ondragend", "ondragend"),
    Atom("ondragenter", "ondragenter"),
    Atom("ondragexit", "ondragexit"),
    Atom("ondragleave", "ondragleave"),
    Atom("ondragover", "ondragover"),
    Atom("ondragstart", "ondragstart"),
    Atom("ondrain", "ondrain"),
    Atom("ondrop", "ondrop"),
    Atom("onerror", "onerror"),
    Atom("onfinish", "onfinish"),
    Atom("onfocus", "onfocus"),
    Atom("onfocusin", "onfocusin"),
    Atom("onfocusout", "onfocusout"),
    Atom("onfullscreenchange", "onfullscreenchange"),
    Atom("onfullscreenerror", "onfullscreenerror"),
    Atom("ongatheringstatechange", "ongatheringstatechange"),
    Atom("onget", "onget"),
    Atom("onhashchange", "onhashchange"),
    Atom("oninput", "oninput"),
    Atom("oninputsourceschange", "oninputsourceschange"),
    Atom("oninstall", "oninstall"),
    Atom("oninvalid", "oninvalid"),
    Atom("onkeydown", "onkeydown"),
    Atom("onkeypress", "onkeypress"),
    Atom("onkeyup", "onkeyup"),
    Atom("onlanguagechange", "onlanguagechange"),
    Atom("onlevelchange", "onlevelchange"),
    Atom("onload", "onload"),
    Atom("onloading", "onloading"),
    Atom("onloadingdone", "onloadingdone"),
    Atom("onloadingerror", "onloadingerror"),
    Atom("onpopstate", "onpopstate"),
    Atom("only", "only"),  # this one is not an event
    Atom("onmerchantvalidation", "onmerchantvalidation"),
    Atom("onmessage", "onmessage"),
    Atom("onmessageerror", "onmessageerror"),
    Atom("onmidimessage", "onmidimessage"),
    Atom("onmousedown", "onmousedown"),
    Atom("onmouseenter", "onmouseenter"),
    Atom("onmouseleave", "onmouseleave"),
    Atom("onmouselongtap", "onmouselongtap"),
    Atom("onmousemove", "onmousemove"),
    Atom("onmouseout", "onmouseout"),
    Atom("onmouseover", "onmouseover"),
    Atom("onMozMouseHittest", "onMozMouseHittest"),
    Atom("onMozMouseExploreByTouch", "onMozMouseExploreByTouch"),
    Atom("onmouseup", "onmouseup"),
    Atom("onMozAfterPaint", "onMozAfterPaint"),
    Atom("onmozfullscreenchange", "onmozfullscreenchange"),
    Atom("onmozfullscreenerror", "onmozfullscreenerror"),
    Atom("onmozpointerlockchange", "onmozpointerlockchange"),
    Atom("onmozpointerlockerror", "onmozpointerlockerror"),
    Atom("onMozMousePixelScroll", "onMozMousePixelScroll"),
    Atom("onMozScrolledAreaChanged", "onMozScrolledAreaChanged"),
    Atom("onmute", "onmute"),
    Atom("onnavigate", "onnavigate"),
    Atom("onnavigatesuccess", "onnavigatesuccess"),
    Atom("onnavigateerror", "onnavigateerror"),
    Atom("onnotificationclick", "onnotificationclick"),
    Atom("onnotificationclose", "onnotificationclose"),
    Atom("onnoupdate", "onnoupdate"),
    Atom("onobsolete", "onobsolete"),
    Atom("ononline", "ononline"),
    Atom("onoffline", "onoffline"),
    Atom("onopen", "onopen"),
    Atom("onorientationchange", "onorientationchange"),
    Atom("onoverflow", "onoverflow"),
    Atom("onpagehide", "onpagehide"),
    Atom("onpageshow", "onpageshow"),
    Atom("onpaste", "onpaste"),
    Atom("onpayerdetailchange", "onpayerdetailchange"),
    Atom("onpaymentmethodchange", "onpaymentmethodchange"),
    Atom("onpointerlockchange", "onpointerlockchange"),
    Atom("onpointerlockerror", "onpointerlockerror"),
    Atom("onpopuphidden", "onpopuphidden"),
    Atom("onpopuphiding", "onpopuphiding"),
    Atom("onpopuppositioned", "onpopuppositioned"),
    Atom("onpopupshowing", "onpopupshowing"),
    Atom("onpopupshown", "onpopupshown"),
    Atom("onprocessorerror", "onprocessorerror"),
    Atom("onprioritychange", "onprioritychange"),
    Atom("onpush", "onpush"),
    Atom("onpushsubscriptionchange", "onpushsubscriptionchange"),
    Atom("onRadioStateChange", "onRadioStateChange"),
    Atom("onreadystatechange", "onreadystatechange"),
    Atom("onrejectionhandled", "onrejectionhandled"),
    Atom("onremove", "onremove"),
    Atom("onrequestprogress", "onrequestprogress"),
    Atom("onresourcetimingbufferfull", "onresourcetimingbufferfull"),
    Atom("onresponseprogress", "onresponseprogress"),
    Atom("onRequest", "onRequest"),
    Atom("onreset", "onreset"),
    Atom("onresize", "onresize"),
    Atom("onscroll", "onscroll"),
    Atom("onsecuritypolicyviolation", "onsecuritypolicyviolation"),
    Atom("onselect", "onselect"),
    Atom("onselectionchange", "onselectionchange"),
    Atom("onselectend", "onselectend"),
    Atom("onselectstart", "onselectstart"),
    Atom("onset", "onset"),
    Atom("onshippingaddresschange", "onshippingaddresschange"),
    Atom("onshippingoptionchange", "onshippingoptionchange"),
    Atom("onshow", "onshow"),
    Atom("onslotchange", "onslotchange"),
    Atom("onsqueeze", "onsqueeze"),
    Atom("onsqueezeend", "onsqueezeend"),
    Atom("onsqueezestart", "onsqueezestart"),
    Atom("onstatechange", "onstatechange"),
    Atom("onstorage", "onstorage"),
    Atom("onsubmit", "onsubmit"),
    Atom("onsuccess", "onsuccess"),
    Atom("onsystemstatusbarclick", "onsystemstatusbarclick"),
    Atom("ontypechange", "ontypechange"),
    Atom("onterminate", "onterminate"),
    Atom("ontext", "ontext"),
    Atom("ontextInput", "ontextInput"),
    Atom("ontoggle", "ontoggle"),
    Atom("ontonechange", "ontonechange"),
    Atom("ontouchstart", "ontouchstart"),
    Atom("ontouchend", "ontouchend"),
    Atom("ontouchmove", "ontouchmove"),
    Atom("ontouchcancel", "ontouchcancel"),
    Atom("ontransitioncancel", "ontransitioncancel"),
    Atom("ontransitionend", "ontransitionend"),
    Atom("ontransitionrun", "ontransitionrun"),
    Atom("ontransitionstart", "ontransitionstart"),
    Atom("onuncapturederror", "onuncapturederror"),
    Atom("onunderflow", "onunderflow"),
    Atom("onunhandledrejection", "onunhandledrejection"),
    Atom("onunload", "onunload"),
    Atom("onunmute", "onunmute"),
    Atom("onupdatefound", "onupdatefound"),
    Atom("onupdateready", "onupdateready"),
    Atom("onupgradeneeded", "onupgradeneeded"),
    Atom("onversionchange", "onversionchange"),
    Atom("onvisibilitychange", "onvisibilitychange"),
    Atom("onvoiceschanged", "onvoiceschanged"),
    Atom("onvrdisplayactivate", "onvrdisplayactivate"),
    Atom("onvrdisplayconnect", "onvrdisplayconnect"),
    Atom("onvrdisplaydeactivate", "onvrdisplaydeactivate"),
    Atom("onvrdisplaydisconnect", "onvrdisplaydisconnect"),
    Atom("onvrdisplaypresentchange", "onvrdisplaypresentchange"),
    Atom("onwebkitAnimationEnd", "onwebkitAnimationEnd"),
    Atom("onwebkitAnimationIteration", "onwebkitAnimationIteration"),
    Atom("onwebkitAnimationStart", "onwebkitAnimationStart"),
    Atom("onwebkitTransitionEnd", "onwebkitTransitionEnd"),
    Atom("onwebkitanimationend", "onwebkitanimationend"),
    Atom("onwebkitanimationiteration", "onwebkitanimationiteration"),
    Atom("onwebkitanimationstart", "onwebkitanimationstart"),
    Atom("onwebkittransitionend", "onwebkittransitionend"),
    Atom("onwheel", "onwheel"),
    Atom("open", "open"),
    Atom("optgroup", "optgroup"),
    Atom("optimum", "optimum"),
    Atom("option", "option"),
    Atom("_or", "or"),
    Atom("order", "order"),
    Atom("ordinal", "ordinal"),
    Atom("orient", "orient"),
    Atom("orientation", "orientation"),
    Atom("origin_trial", "origin-trial"),
    Atom("otherwise", "otherwise"),
    Atom("outerHTML", "outerHTML"),
    Atom("output", "output"),
    Atom("overflow", "overflow"),
    Atom("overflowBlock", "overflow-block"),
    Atom("overflowInline", "overflow-inline"),
    Atom("overlay", "overlay"),
    Atom("p", "p"),
    Atom("pack", "pack"),
    Atom("page", "page"),
    Atom("pageincrement", "pageincrement"),
    Atom("paint", "paint"),
    Atom("paint_order", "paint-order"),
    Atom("panel", "panel"),
    Atom("paragraph", "paragraph"),
    Atom("param", "param"),
    Atom("parameter", "parameter"),
    Atom("parent", "parent"),
    Atom("parsererror", "parsererror"),
    Atom("part", "part"),
    Atom("password", "password"),
    Atom("pattern", "pattern"),
    Atom("patternSeparator", "pattern-separator"),
    Atom("perMille", "per-mille"),
    Atom("percent", "percent"),
    Atom("persist", "persist"),
    Atom("phase", "phase"),
    Atom("picture", "picture"),
    Atom("ping", "ping"),
    Atom("pinned", "pinned"),
    Atom("placeholder", "placeholder"),
    Atom("plaintext", "plaintext"),
    Atom("plaintextOnly", "plaintext-only"),
    Atom("playbackrate", "playbackrate"),
    Atom("pointSize", "point-size"),
    Atom("poly", "poly"),
    Atom("polygon", "polygon"),
    Atom("popover", "popover"),
    Atom("popovertarget", "popovertarget"),
    Atom("popovertargetaction", "popovertargetaction"),
    Atom("popup", "popup"),
    Atom("popupalign", "popupalign"),
    Atom("popupanchor", "popupanchor"),
    Atom("popupgroup", "popupgroup"),
    Atom("popupset", "popupset"),
    Atom("popupsinherittooltip", "popupsinherittooltip"),
    Atom("portal", "portal"),
    Atom("position", "position"),
    Atom("poster", "poster"),
    Atom("pre", "pre"),
    Atom("preceding", "preceding"),
    Atom("precedingSibling", "preceding-sibling"),
    Atom("prefersReducedMotion", "prefers-reduced-motion"),
    Atom("prefersReducedTransparency", "prefers-reduced-transparency"),
    Atom("prefersColorScheme", "prefers-color-scheme"),
    Atom("prefersContrast", "prefers-contrast"),
    Atom("prefix", "prefix"),
    Atom("prefwidth", "prefwidth"),
    Atom("queue", "queue"),
    Atom("dynamicRange", "dynamic-range"),
    Atom("videoDynamicRange", "video-dynamic-range"),
    Atom("scripting", "scripting"),
    Atom("preload", "preload"),
    Atom("preserve", "preserve"),
    Atom("preserveSpace", "preserve-space"),
    Atom("preventdefault", "preventdefault"),
    Atom("previewDiv", "preview-div"),
    Atom("primary", "primary"),
    Atom("print", "print"),
    Atom("printisfocuseddoc", "printisfocuseddoc"),
    Atom("printselectionranges", "printselectionranges"),
    Atom("priority", "priority"),
    Atom("processingInstruction", "processing-instruction"),
    Atom("profile", "profile"),
    Atom("progress", "progress"),
    Atom("prompt", "prompt"),
    Atom("properties", "properties"),
    Atom("property", "property"),
    Atom("pubdate", "pubdate"),
    Atom("q", "q"),
    Atom("radio", "radio"),
    Atom("radioLabel", "radio-label"),
    Atom("radiogroup", "radiogroup"),
    Atom("range", "range"),
    Atom("readonly", "readonly"),
    Atom("rect", "rect"),
    Atom("rectangle", "rectangle"),
    Atom("refresh", "refresh"),
    Atom("rel", "rel"),
    Atom("relativeBounds", "relative-bounds"),
    Atom("rem", "rem"),
    Atom("remote", "remote"),
    Atom("removeelement", "removeelement"),
    Atom("render", "render"),
    Atom("renderingobserverset", "renderingobserverset"),
    Atom("repeat", "repeat"),
    Atom("replace", "replace"),
    Atom("requestcontextid", "requestcontextid"),
    Atom("required", "required"),
    Atom("reserved", "reserved"),
    Atom("reset", "reset"),
    Atom("resizeafter", "resizeafter"),
    Atom("resizebefore", "resizebefore"),
    Atom("resizer", "resizer"),
    Atom("resolution", "resolution"),
    Atom("resources", "resources"),
    Atom("result", "result"),
    Atom("resultPrefix", "result-prefix"),
    Atom("retargetdocumentfocus", "retargetdocumentfocus"),
    Atom("rev", "rev"),
    Atom("reverse", "reverse"),
    Atom("reversed", "reversed"),
    Atom("rhs", "rhs"),
    Atom("richlistbox", "richlistbox"),
    Atom("richlistitem", "richlistitem"),
    Atom("right", "right"),
    Atom("rightmargin", "rightmargin"),
    Atom("role", "role"),
    Atom("rolluponmousewheel", "rolluponmousewheel"),
    Atom("round", "round"),
    Atom("row", "row"),
    Atom("rows", "rows"),
    Atom("rowspan", "rowspan"),
    Atom("rb", "rb"),
    Atom("rp", "rp"),
    Atom("rt", "rt"),
    Atom("rtc", "rtc"),
    Atom("rtl", "rtl"),
    Atom("ruby", "ruby"),
    Atom("rubyBase", "ruby-base"),
    Atom("rubyBaseContainer", "ruby-base-container"),
    Atom("rubyText", "ruby-text"),
    Atom("rubyTextContainer", "ruby-text-container"),
    Atom("rules", "rules"),
    Atom("s", "s"),
    Atom("safe_area_inset_top", "safe-area-inset-top"),
    Atom("safe_area_inset_bottom", "safe-area-inset-bottom"),
    Atom("safe_area_inset_left", "safe-area-inset-left"),
    Atom("safe_area_inset_right", "safe-area-inset-right"),
    Atom("samp", "samp"),
    Atom("sandbox", "sandbox"),
    Atom("sbattr", "sbattr"),
    Atom("scale", "scale"),
    Atom("scan", "scan"),
    Atom("scheme", "scheme"),
    Atom("scope", "scope"),
    Atom("scoped", "scoped"),
    Atom("screen", "screen"),
    Atom("screenX", "screenX"),
    Atom("screenx", "screenx"),
    Atom("screenY", "screenY"),
    Atom("screeny", "screeny"),
    Atom("script", "script"),
    Atom("scrollbar", "scrollbar"),
    Atom("scrollbarThumb", "scrollbar-thumb"),
    Atom("scrollamount", "scrollamount"),
    Atom("scrollbarbutton", "scrollbarbutton"),
    Atom("scrollbarDownBottom", "scrollbar-down-bottom"),
    Atom("scrollbarDownTop", "scrollbar-down-top"),
    Atom("scrollbarInlineSize", "scrollbar-inline-size"),
    Atom("scrollbarUpBottom", "scrollbar-up-bottom"),
    Atom("scrollbarUpTop", "scrollbar-up-top"),
    Atom("scrollbox", "scrollbox"),
    Atom("scrollcorner", "scrollcorner"),
    Atom("scrolldelay", "scrolldelay"),
    Atom("scrolling", "scrolling"),
    Atom("scrollPosition", "scroll-position"),
    Atom("se", "se"),
    Atom("section", "section"),
    Atom("select", "select"),
    Atom("selected", "selected"),
    Atom("selectedIndex", "selectedIndex"),
    Atom("selectedindex", "selectedindex"),
    Atom("selectmenu", "selectmenu"),
    Atom("self", "self"),
    Atom("seltype", "seltype"),
    Atom("setcookie", "set-cookie"),
    Atom("setter", "setter"),
    Atom("shadow", "shadow"),
    Atom("shape", "shape"),
    Atom("show", "show"),
    Atom("showcaret", "showcaret"),
    Atom("show_modal", "show-modal"),
    Atom("show_popover", "show-popover"),
    Atom("showservicesmenu", "showservicesmenu"),
    Atom("sibling", "sibling"),
    Atom("simple", "simple"),
    Atom("simp_chinese_formal", "simp-chinese-formal"),
    Atom("simp_chinese_informal", "simp-chinese-informal"),
    Atom("single", "single"),
    Atom("size", "size"),
    Atom("sizes", "sizes"),
    Atom("sizemode", "sizemode"),
    Atom("sizetopopup", "sizetopopup"),
    Atom("slider", "slider"),
    Atom("small", "small"),
    Atom("smooth", "smooth"),
    Atom("snap", "snap"),
    Atom("solid", "solid"),
    Atom("sort", "sort"),
    Atom("sortActive", "sortActive"),
    Atom("sortDirection", "sortDirection"),
    Atom("sorted", "sorted"),
    Atom("sorthints", "sorthints"),
    Atom("source", "source"),
    Atom("sourcetext", "sourcetext"),
    Atom("space", "space"),
    Atom("spacer", "spacer"),
    Atom("span", "span"),
    Atom("spellcheck", "spellcheck"),
    Atom("split", "split"),
    Atom("splitter", "splitter"),
    Atom("square", "square"),
    Atom("src", "src"),
    Atom("srcdoc", "srcdoc"),
    Atom("srclang", "srclang"),
    Atom("srcset", "srcset"),
    Atom("standalone", "standalone"),
    Atom("standby", "standby"),
    Atom("start", "start"),
    Atom("startsWith", "starts-with"),
    Atom("state", "state"),
    Atom("statusbar", "statusbar"),
    Atom("step", "step"),
    Atom("stop", "stop"),
    Atom("stretch", "stretch"),
    Atom("strike", "strike"),
    Atom("string", "string"),
    Atom("stringLength", "string-length"),
    Atom("stripSpace", "strip-space"),
    Atom("strong", "strong"),
    Atom("style", "style"),
    Atom("stylesheet", "stylesheet"),
    Atom("stylesheetPrefix", "stylesheet-prefix"),
    Atom("submit", "submit"),
    Atom("substate", "substate"),
    Atom("substring", "substring"),
    Atom("substringAfter", "substring-after"),
    Atom("substringBefore", "substring-before"),
    Atom("sub", "sub"),
    Atom("suggestion", "suggestion"),
    Atom("sum", "sum"),
    Atom("sup", "sup"),
    Atom("summary", "summary"),
    Atom("sw", "sw"),
    # Atom("_switch", "switch"),  # "switch" is present below
    Atom("systemProperty", "system-property"),
    Atom("tab", "tab"),
    Atom("tabindex", "tabindex"),
    Atom("table", "table"),
    Atom("tabpanel", "tabpanel"),
    Atom("tabpanels", "tabpanels"),
    Atom("tag", "tag"),
    Atom("target", "target"),
    Atom("targets", "targets"),
    Atom("tbody", "tbody"),
    Atom("td", "td"),
    Atom("tel", "tel"),
    Atom("_template", "template"),
    Atom("text_decoration", "text-decoration"),
    Atom("terminate", "terminate"),
    Atom("term", "term"),
    Atom("test", "test"),
    Atom("text", "text"),
    Atom("textAlign", "text-align"),
    Atom("textarea", "textarea"),
    Atom("textbox", "textbox"),
    Atom("textContent", "textContent"),
    Atom("textLink", "text-link"),
    Atom("textOverlay", "text-overlay"),
    Atom("tfoot", "tfoot"),
    Atom("th", "th"),
    Atom("thead", "thead"),
    Atom("thumb", "thumb"),
    Atom("time", "time"),
    Atom("title", "title"),
    Atom("titletip", "titletip"),
    Atom("toggle", "toggle"),
    Atom("toggle_popover", "toggle-popover"),
    Atom("token", "token"),
    Atom("tokenize", "tokenize"),
    Atom("toolbar", "toolbar"),
    Atom("toolbarbutton", "toolbarbutton"),
    Atom("toolbaritem", "toolbaritem"),
    Atom("toolbarpaletteitem", "toolbarpaletteitem"),
    Atom("toolbox", "toolbox"),
    Atom("tooltip", "tooltip"),
    Atom("tooltiptext", "tooltiptext"),
    Atom("top", "top"),
    Atom("topleft", "topleft"),
    Atom("topmargin", "topmargin"),
    Atom("topright", "topright"),
    Atom("tr", "tr"),
    Atom("track", "track"),
    Atom("trad_chinese_formal", "trad-chinese-formal"),
    Atom("trad_chinese_informal", "trad-chinese-informal"),
    Atom("trailing", "trailing"),
    Atom("transform", "transform"),
    Atom("transform_3d", "transform-3d"),
    Atom("transformiix", "transformiix"),
    Atom("translate", "translate"),
    Atom("transparent", "transparent"),
    Atom("tree", "tree"),
    Atom("treecell", "treecell"),
    Atom("treechildren", "treechildren"),
    Atom("treecol", "treecol"),
    Atom("treecolpicker", "treecolpicker"),
    Atom("treecols", "treecols"),
    Atom("treeitem", "treeitem"),
    Atom("treerow", "treerow"),
    Atom("treeseparator", "treeseparator"),
    Atom("_true", "true"),
    Atom("truespeed", "truespeed"),
    Atom("tt", "tt"),
    Atom("type", "type"),
    Atom("u", "u"),
    Atom("ul", "ul"),
    Atom("unparsedEntityUri", "unparsed-entity-uri"),
    Atom("untilFound", "until-found"),
    Atom("up", "up"),
    Atom("update", "update"),
    Atom("upperFirst", "upper-first"),
    Atom("use", "use"),
    Atom("useAttributeSets", "use-attribute-sets"),
    Atom("usemap", "usemap"),
    Atom("user_scalable", "user-scalable"),
    Atom("validate", "validate"),
    Atom("valign", "valign"),
    Atom("value", "value"),
    Atom("values", "values"),
    Atom("valueOf", "value-of"),
    Atom("valuetype", "valuetype"),
    Atom("var", "var"),
    Atom("variable", "variable"),
    Atom("vendor", "vendor"),
    Atom("vendorUrl", "vendor-url"),
    Atom("version", "version"),
    Atom("vertical", "vertical"),
    Atom("audio", "audio"),
    Atom("video", "video"),
    Atom("viewport", "viewport"),
    Atom("viewport_fit", "viewport-fit"),
    Atom("viewport_height", "viewport-height"),
    Atom("viewport_initial_scale", "viewport-initial-scale"),
    Atom("viewport_maximum_scale", "viewport-maximum-scale"),
    Atom("viewport_minimum_scale", "viewport-minimum-scale"),
    Atom("viewport_user_scalable", "viewport-user-scalable"),
    Atom("viewport_width", "viewport-width"),
    Atom("visibility", "visibility"),
    Atom("visuallyselected", "visuallyselected"),
    Atom("vlink", "vlink"),
    Atom("_void", "void"),
    Atom("vsides", "vsides"),
    Atom("vspace", "vspace"),
    Atom("w", "w"),
    Atom("wbr", "wbr"),
    Atom("webkitdirectory", "webkitdirectory"),
    Atom("when", "when"),
    Atom("white", "white"),
    Atom("width", "width"),
    Atom("willChange", "will-change"),
    Atom("window", "window"),
    Atom("headerWindowTarget", "window-target"),
    Atom("windowtype", "windowtype"),
    Atom("withParam", "with-param"),
    Atom("wrap", "wrap"),
    Atom("headerDNSPrefetchControl", "x-dns-prefetch-control"),
    Atom("headerCSP", "content-security-policy"),
    Atom("headerCSPReportOnly", "content-security-policy-report-only"),
    Atom("headerXFO", "x-frame-options"),
    Atom("x_western", "x-western"),
    Atom("xml", "xml"),
    Atom("xml_stylesheet", "xml-stylesheet"),
    Atom("xmlns", "xmlns"),
    Atom("xmp", "xmp"),
    Atom("xul", "xul"),
    Atom("yes", "yes"),
    Atom("z_index", "z-index"),
    Atom("zeroDigit", "zero-digit"),
    Atom("zlevel", "zlevel"),
    Atom("percentage", "%"),
    Atom("A", "A"),
    Atom("alignment_baseline", "alignment-baseline"),
    Atom("amplitude", "amplitude"),
    Atom("animate", "animate"),
    Atom("animateColor", "animateColor"),
    Atom("animateMotion", "animateMotion"),
    Atom("animateTransform", "animateTransform"),
    Atom("arithmetic", "arithmetic"),
    Atom("atop", "atop"),
    Atom("azimuth", "azimuth"),
    Atom("B", "B"),
    Atom("background_color", "background-color"),
    Atom("background_image", "background-image"),
    Atom("baseFrequency", "baseFrequency"),
    Atom("baseline_shift", "baseline-shift"),
    Atom("bias", "bias"),
    Atom("caption_side", "caption-side"),
    Atom("clip_path", "clip-path"),
    Atom("clip_rule", "clip-rule"),
    Atom("clipPath", "clipPath"),
    Atom("clipPathUnits", "clipPathUnits"),
    Atom("cm", "cm"),
    Atom("color_burn", "color-burn"),
    Atom("color_dodge", "color-dodge"),
    Atom("color_interpolation", "color-interpolation"),
    Atom("color_interpolation_filters", "color-interpolation-filters"),
    Atom("color_profile", "color-profile"),
    Atom("cursor", "cursor"),
    Atom("cx", "cx"),
    Atom("cy", "cy"),
    Atom("d", "d"),
    Atom("darken", "darken"),
    Atom("defs", "defs"),
    Atom("deg", "deg"),
    Atom("desc", "desc"),
    Atom("diffuseConstant", "diffuseConstant"),
    Atom("dilate", "dilate"),
    Atom("direction", "direction"),
    Atom("disable", "disable"),
    Atom("disc", "disc"),
    Atom("discrete", "discrete"),
    Atom("divisor", "divisor"),
    Atom("dominant_baseline", "dominant-baseline"),
    Atom("duplicate", "duplicate"),
    Atom("dx", "dx"),
    Atom("dy", "dy"),
    Atom("edgeMode", "edgeMode"),
    Atom("ellipse", "ellipse"),
    Atom("elevation", "elevation"),
    Atom("erode", "erode"),
    Atom("ex", "ex"),
    Atom("exact", "exact"),
    Atom("exclusion", "exclusion"),
    Atom("exponent", "exponent"),
    Atom("feBlend", "feBlend"),
    Atom("feColorMatrix", "feColorMatrix"),
    Atom("feComponentTransfer", "feComponentTransfer"),
    Atom("feComposite", "feComposite"),
    Atom("feConvolveMatrix", "feConvolveMatrix"),
    Atom("feDiffuseLighting", "feDiffuseLighting"),
    Atom("feDisplacementMap", "feDisplacementMap"),
    Atom("feDistantLight", "feDistantLight"),
    Atom("feDropShadow", "feDropShadow"),
    Atom("feFlood", "feFlood"),
    Atom("feFuncA", "feFuncA"),
    Atom("feFuncB", "feFuncB"),
    Atom("feFuncG", "feFuncG"),
    Atom("feFuncR", "feFuncR"),
    Atom("feGaussianBlur", "feGaussianBlur"),
    Atom("feImage", "feImage"),
    Atom("feMerge", "feMerge"),
    Atom("feMergeNode", "feMergeNode"),
    Atom("feMorphology", "feMorphology"),
    Atom("feOffset", "feOffset"),
    Atom("fePointLight", "fePointLight"),
    Atom("feSpecularLighting", "feSpecularLighting"),
    Atom("feSpotLight", "feSpotLight"),
    Atom("feTile", "feTile"),
    Atom("feTurbulence", "feTurbulence"),
    Atom("fill", "fill"),
    Atom("fill_opacity", "fill-opacity"),
    Atom("fill_rule", "fill-rule"),
    Atom("filter", "filter"),
    Atom("filterUnits", "filterUnits"),
    Atom("_float", "float"),
    Atom("flood_color", "flood-color"),
    Atom("flood_opacity", "flood-opacity"),
    Atom("font_face", "font-face"),
    Atom("font_face_format", "font-face-format"),
    Atom("font_face_name", "font-face-name"),
    Atom("font_face_src", "font-face-src"),
    Atom("font_face_uri", "font-face-uri"),
    Atom("font_family", "font-family"),
    Atom("font_size", "font-size"),
    Atom("font_size_adjust", "font-size-adjust"),
    Atom("font_stretch", "font-stretch"),
    Atom("font_style", "font-style"),
    Atom("font_variant", "font-variant"),
    Atom("font_weight", "font-weight"),
    Atom("formatting", "formatting"),
    Atom("foreignObject", "foreignObject"),
    Atom("fractalNoise", "fractalNoise"),
    Atom("fr", "fr"),
    Atom("fx", "fx"),
    Atom("fy", "fy"),
    Atom("G", "G"),
    Atom("g", "g"),
    Atom("gamma", "gamma"),
    Atom("glyphRef", "glyphRef"),
    Atom("grad", "grad"),
    Atom("gradientTransform", "gradientTransform"),
    Atom("gradientUnits", "gradientUnits"),
    Atom("hardLight", "hard-light"),
    Atom("hue", "hue"),
    Atom("hueRotate", "hueRotate"),
    Atom("identity", "identity"),
    Atom("image_rendering", "image-rendering"),
    Atom("in", "in"),
    Atom("in2", "in2"),
    Atom("intercept", "intercept"),
    Atom("k1", "k1"),
    Atom("k2", "k2"),
    Atom("k3", "k3"),
    Atom("k4", "k4"),
    Atom("kernelMatrix", "kernelMatrix"),
    Atom("kernelUnitLength", "kernelUnitLength"),
    Atom("lengthAdjust", "lengthAdjust"),
    Atom("letter_spacing", "letter-spacing"),
    Atom("lighten", "lighten"),
    Atom("lighter", "lighter"),
    Atom("lighting_color", "lighting-color"),
    Atom("limitingConeAngle", "limitingConeAngle"),
    Atom("linear", "linear"),
    Atom("linearGradient", "linearGradient"),
    Atom("list_item", "list-item"),
    Atom("list_style_type", "list-style-type"),
    Atom("luminanceToAlpha", "luminanceToAlpha"),
    Atom("luminosity", "luminosity"),
    Atom("magnify", "magnify"),
    Atom("marker", "marker"),
    Atom("marker_end", "marker-end"),
    Atom("marker_mid", "marker-mid"),
    Atom("marker_start", "marker-start"),
    Atom("markerHeight", "markerHeight"),
    Atom("markerUnits", "markerUnits"),
    Atom("markerWidth", "markerWidth"),
    Atom("mask", "mask"),
    Atom("maskContentUnits", "maskContentUnits"),
    Atom("mask_type", "mask-type"),
    Atom("maskUnits", "maskUnits"),
    Atom("matrix", "matrix"),
    Atom("metadata", "metadata"),
    Atom("missingGlyph", "missing-glyph"),
    Atom("mm", "mm"),
    Atom("mpath", "mpath"),
    Atom("noStitch", "noStitch"),
    Atom("numOctaves", "numOctaves"),
    Atom("multiply", "multiply"),
    Atom("objectBoundingBox", "objectBoundingBox"),
    Atom("offset", "offset"),
    Atom("onSVGLoad", "onSVGLoad"),
    Atom("onSVGScroll", "onSVGScroll"),
    Atom("onzoom", "onzoom"),
    Atom("opacity", "opacity"),
    Atom("_operator", "operator"),
    Atom("out", "out"),
    Atom("over", "over"),
    Atom("overridePreserveAspectRatio", "overridePreserveAspectRatio"),
    Atom("pad", "pad"),
    Atom("path", "path"),
    Atom("pathLength", "pathLength"),
    Atom("patternContentUnits", "patternContentUnits"),
    Atom("patternTransform", "patternTransform"),
    Atom("patternUnits", "patternUnits"),
    Atom("pc", "pc"),
    Atom("pointer", "pointer"),
    Atom("pointer_events", "pointer-events"),
    Atom("points", "points"),
    Atom("pointsAtX", "pointsAtX"),
    Atom("pointsAtY", "pointsAtY"),
    Atom("pointsAtZ", "pointsAtZ"),
    Atom("polyline", "polyline"),
    Atom("preserveAlpha", "preserveAlpha"),
    Atom("preserveAspectRatio", "preserveAspectRatio"),
    Atom("primitiveUnits", "primitiveUnits"),
    Atom("pt", "pt"),
    Atom("px", "px"),
    Atom("R", "R"),
    Atom("r", "r"),
    Atom("rad", "rad"),
    Atom("radialGradient", "radialGradient"),
    Atom("radius", "radius"),
    Atom("reflect", "reflect"),
    Atom("refX", "refX"),
    Atom("refY", "refY"),
    Atom("requiredExtensions", "requiredExtensions"),
    Atom("requiredFeatures", "requiredFeatures"),
    Atom("rotate", "rotate"),
    Atom("rx", "rx"),
    Atom("ry", "ry"),
    Atom("saturate", "saturate"),
    Atom("saturation", "saturation"),
    Atom("set", "set"),
    Atom("seed", "seed"),
    Atom("shape_rendering", "shape-rendering"),
    Atom("simpleScopeChain", "simpleScopeChain"),
    Atom("skewX", "skewX"),
    Atom("skewY", "skewY"),
    Atom("slope", "slope"),
    Atom("slot", "slot"),
    Atom("softLight", "soft-light"),
    Atom("spacing", "spacing"),
    Atom("spacingAndGlyphs", "spacingAndGlyphs"),
    Atom("specularConstant", "specularConstant"),
    Atom("specularExponent", "specularExponent"),
    Atom("spreadMethod", "spreadMethod"),
    Atom("startOffset", "startOffset"),
    Atom("stdDeviation", "stdDeviation"),
    Atom("stitch", "stitch"),
    Atom("stitchTiles", "stitchTiles"),
    Atom("stop_color", "stop-color"),
    Atom("stop_opacity", "stop-opacity"),
    Atom("stroke", "stroke"),
    Atom("stroke_dasharray", "stroke-dasharray"),
    Atom("stroke_dashoffset", "stroke-dashoffset"),
    Atom("stroke_linecap", "stroke-linecap"),
    Atom("stroke_linejoin", "stroke-linejoin"),
    Atom("stroke_miterlimit", "stroke-miterlimit"),
    Atom("stroke_opacity", "stroke-opacity"),
    Atom("stroke_width", "stroke-width"),
    Atom("strokeWidth", "strokeWidth"),
    Atom("surfaceScale", "surfaceScale"),
    Atom("svg", "svg"),
    Atom("svgSwitch", "switch"),
    Atom("symbol", "symbol"),
    Atom("systemLanguage", "systemLanguage"),
    Atom("tableValues", "tableValues"),
    Atom("targetX", "targetX"),
    Atom("targetY", "targetY"),
    Atom("text_anchor", "text-anchor"),
    Atom("text_overflow", "text-overflow"),
    Atom("text_rendering", "text-rendering"),
    Atom("textLength", "textLength"),
    Atom("textPath", "textPath"),
    Atom("transform_origin", "transform-origin"),
    Atom("tref", "tref"),
    Atom("tspan", "tspan"),
    Atom("turbulence", "turbulence"),
    Atom("unicode_bidi", "unicode-bidi"),
    Atom("userSpaceOnUse", "userSpaceOnUse"),
    Atom("view", "view"),
    Atom("viewBox", "viewBox"),
    Atom("viewTarget", "viewTarget"),
    Atom("white_space", "white-space"),
    Atom("windowsmica", "windowsmica"),
    Atom("word_spacing", "word-spacing"),
    Atom("writing_mode", "writing-mode"),
    Atom("x", "x"),
    Atom("x1", "x1"),
    Atom("x2", "x2"),
    Atom("xChannelSelector", "xChannelSelector"),
    Atom("xor_", "xor"),
    Atom("y", "y"),
    Atom("y1", "y1"),
    Atom("y2", "y2"),
    Atom("yChannelSelector", "yChannelSelector"),
    Atom("z", "z"),
    Atom("zoomAndPan", "zoomAndPan"),
    Atom("vector_effect", "vector-effect"),
    Atom("vertical_align", "vertical-align"),
    Atom("accumulate", "accumulate"),
    Atom("additive", "additive"),
    Atom("attributeName", "attributeName"),
    Atom("attributeType", "attributeType"),
    Atom("auto_reverse", "auto-reverse"),
    Atom("begin", "begin"),
    Atom("beginEvent", "beginEvent"),
    Atom("by", "by"),
    Atom("calcMode", "calcMode"),
    Atom("dur", "dur"),
    Atom("keyPoints", "keyPoints"),
    Atom("keySplines", "keySplines"),
    Atom("keyTimes", "keyTimes"),
    Atom("mozAnimateMotionDummyAttr", "_mozAnimateMotionDummyAttr"),
    Atom("onbegin", "onbegin"),
    Atom("onbeginEvent", "onbeginEvent"),
    Atom("onend", "onend"),
    Atom("onendEvent", "onendEvent"),
    Atom("onrelease", "onrelease"),
    Atom("onrepeat", "onrepeat"),
    Atom("onrepeatEvent", "onrepeatEvent"),
    Atom("repeatCount", "repeatCount"),
    Atom("repeatDur", "repeatDur"),
    Atom("repeatEvent", "repeatEvent"),
    Atom("restart", "restart"),
    Atom("to", "to"),
    Atom("abs", "abs"),
    Atom("accent", "accent"),
    Atom("accentunder", "accentunder"),
    Atom("actiontype", "actiontype"),
    Atom("alignmentscope", "alignmentscope"),
    Atom("altimg", "altimg"),
    Atom("altimg_height", "altimg-height"),
    Atom("altimg_valign", "altimg-valign"),
    Atom("altimg_width", "altimg-width"),
    Atom("annotation", "annotation"),
    Atom("annotation_xml", "annotation-xml"),
    Atom("apply", "apply"),
    Atom("approx", "approx"),
    Atom("arccos", "arccos"),
    Atom("arccosh", "arccosh"),
    Atom("arccot", "arccot"),
    Atom("arccoth", "arccoth"),
    Atom("arccsc", "arccsc"),
    Atom("arccsch", "arccsch"),
    Atom("arcsec", "arcsec"),
    Atom("arcsech", "arcsech"),
    Atom("arcsin", "arcsin"),
    Atom("arcsinh", "arcsinh"),
    Atom("arctan", "arctan"),
    Atom("arctanh", "arctanh"),
    Atom("arg", "arg"),
    Atom("bevelled", "bevelled"),
    Atom("bind", "bind"),
    Atom("bvar", "bvar"),
    Atom("card", "card"),
    Atom("cartesianproduct", "cartesianproduct"),
    Atom("cbytes", "cbytes"),
    Atom("cd", "cd"),
    Atom("cdgroup", "cdgroup"),
    Atom("cerror", "cerror"),
    Atom("charalign", "charalign"),
    Atom("ci", "ci"),
    Atom("closure", "closure"),
    Atom("cn", "cn"),
    Atom("codomain", "codomain"),
    Atom("columnalign", "columnalign"),
    Atom("columnalignment", "columnalignment"),
    Atom("columnlines", "columnlines"),
    Atom("columnspacing", "columnspacing"),
    Atom("columnspan", "columnspan"),
    Atom("columnwidth", "columnwidth"),
    Atom("complexes", "complexes"),
    Atom("compose", "compose"),
    Atom("condition", "condition"),
    Atom("conjugate", "conjugate"),
    Atom("cos", "cos"),
    Atom("cosh", "cosh"),
    Atom("cot", "cot"),
    Atom("coth", "coth"),
    Atom("crossout", "crossout"),
    Atom("csc", "csc"),
    Atom("csch", "csch"),
    Atom("cs", "cs"),
    Atom("csymbol", "csymbol"),
    Atom("csp", "csp"),
    Atom("cspViolation", "csp-violation"),
    Atom("curl", "curl"),
    Atom("decimalpoint", "decimalpoint"),
    Atom("definition", "definition"),
    Atom("definitionURL", "definitionURL"),
    Atom("degree", "degree"),
    Atom("denomalign", "denomalign"),
    Atom("depth", "depth"),
    Atom("determinant", "determinant"),
    Atom("diff", "diff"),
    Atom("displaystyle", "displaystyle"),
    Atom("divergence", "divergence"),
    Atom("divide", "divide"),
    Atom("dom_viewTransitions_enabled", "dom.viewTransitions.enabled"),
    Atom("domain", "domain"),
    Atom("domainofapplication", "domainofapplication"),
    Atom("edge", "edge"),
    Atom("el", "el"),
    Atom("emptyset", "emptyset"),
    Atom("eq", "eq"),
    Atom("equalcolumns", "equalcolumns"),
    Atom("equalrows", "equalrows"),
    Atom("equivalent", "equivalent"),
    Atom("eulergamma", "eulergamma"),
    Atom("exists", "exists"),
    Atom("exp", "exp"),
    Atom("exponentiale", "exponentiale"),
    Atom("factorial", "factorial"),
    Atom("factorof", "factorof"),
    Atom("fence", "fence"),
    Atom("fn", "fn"),
    Atom("fontfamily", "fontfamily"),
    Atom("fontsize", "fontsize"),
    Atom("fontstyle", "fontstyle"),
    Atom("fontweight", "fontweight"),
    Atom("forall", "forall"),
    Atom("framespacing", "framespacing"),
    Atom("gcd", "gcd"),
    Atom("geq", "geq"),
    Atom("groupalign", "groupalign"),
    Atom("gt", "gt"),
    Atom("ident", "ident"),
    Atom("imaginaryi", "imaginaryi"),
    Atom("imaginary", "imaginary"),
    Atom("implies", "implies"),
    Atom("indentalignfirst", "indentalignfirst"),
    Atom("indentalign", "indentalign"),
    Atom("indentalignlast", "indentalignlast"),
    Atom("indentshiftfirst", "indentshiftfirst"),
    Atom("indentshift", "indentshift"),
    Atom("indenttarget", "indenttarget"),
    Atom("integers", "integers"),
    Atom("intersect", "intersect"),
    Atom("interval", "interval"),
    Atom("int_", "int"),
    Atom("inverse", "inverse"),
    Atom("lambda", "lambda"),
    Atom("laplacian", "laplacian"),
    Atom("largeop", "largeop"),
    Atom("lcm", "lcm"),
    Atom("leq", "leq"),
    Atom("limit", "limit"),
    Atom("linebreak", "linebreak"),
    Atom("linebreakmultchar", "linebreakmultchar"),
    Atom("linebreakstyle", "linebreakstyle"),
    Atom("linethickness", "linethickness"),
    Atom("list", "list"),
    Atom("ln", "ln"),
    Atom("location", "location"),
    Atom("logbase", "logbase"),
    Atom("log", "log"),
    Atom("longdivstyle", "longdivstyle"),
    Atom("lowlimit", "lowlimit"),
    Atom("lquote", "lquote"),
    Atom("lspace", "lspace"),
    Atom("lt", "lt"),
    Atom("maction", "maction"),
    Atom("maligngroup", "maligngroup"),
    Atom("malignmark", "malignmark"),
    Atom("mathbackground", "mathbackground"),
    Atom("mathcolor", "mathcolor"),
    Atom("mathsize", "mathsize"),
    Atom("mathvariant", "mathvariant"),
    Atom("matrixrow", "matrixrow"),
    Atom("maxsize", "maxsize"),
    Atom("mean", "mean"),
    Atom("median", "median"),
    Atom("menclose", "menclose"),
    Atom("merror", "merror"),
    Atom("mfenced", "mfenced"),
    Atom("mfrac", "mfrac"),
    Atom("mglyph", "mglyph"),
    Atom("mi", "mi"),
    Atom("minlabelspacing", "minlabelspacing"),
    Atom("minsize", "minsize"),
    Atom("minus", "minus"),
    Atom("mlabeledtr", "mlabeledtr"),
    Atom("mlongdiv", "mlongdiv"),
    Atom("mmultiscripts", "mmultiscripts"),
    Atom("mn", "mn"),
    Atom("momentabout", "momentabout"),
    Atom("moment", "moment"),
    Atom("mo", "mo"),
    Atom("movablelimits", "movablelimits"),
    Atom("mover", "mover"),
    Atom("mpadded", "mpadded"),
    Atom("mphantom", "mphantom"),
    Atom("mprescripts", "mprescripts"),
    Atom("mroot", "mroot"),
    Atom("mrow", "mrow"),
    Atom("mscarries", "mscarries"),
    Atom("mscarry", "mscarry"),
    Atom("msgroup", "msgroup"),
    Atom("msline", "msline"),
    Atom("ms", "ms"),
    Atom("mspace", "mspace"),
    Atom("msqrt", "msqrt"),
    Atom("msrow", "msrow"),
    Atom("mstack", "mstack"),
    Atom("mstyle", "mstyle"),
    Atom("msub", "msub"),
    Atom("msubsup", "msubsup"),
    Atom("msup", "msup"),
    Atom("mtable", "mtable"),
    Atom("mtd", "mtd"),
    Atom("mtext", "mtext"),
    Atom("mtr", "mtr"),
    Atom("munder", "munder"),
    Atom("munderover", "munderover"),
    Atom("naturalnumbers", "naturalnumbers"),
    Atom("neq", "neq"),
    Atom("notanumber", "notanumber"),
    Atom("notation", "notation"),
    Atom("note", "note"),
    Atom("notin", "notin"),
    Atom("notprsubset", "notprsubset"),
    Atom("notsubset", "notsubset"),
    Atom("numalign", "numalign"),
    Atom("other", "other"),
    Atom("outerproduct", "outerproduct"),
    Atom("partialdiff", "partialdiff"),
    Atom("piece", "piece"),
    Atom("piecewise", "piecewise"),
    Atom("pi", "pi"),
    Atom("plus", "plus"),
    Atom("power", "power"),
    Atom("primes", "primes"),
    Atom("product", "product"),
    Atom("prsubset", "prsubset"),
    Atom("quotient", "quotient"),
    Atom("rationals", "rationals"),
    Atom("real", "real"),
    Atom("reals", "reals"),
    Atom("reln", "reln"),
    Atom("root", "root"),
    Atom("rowalign", "rowalign"),
    Atom("rowlines", "rowlines"),
    Atom("rowspacing", "rowspacing"),
    Atom("rquote", "rquote"),
    Atom("rspace", "rspace"),
    Atom("scalarproduct", "scalarproduct"),
    Atom("schemaLocation", "schemaLocation"),
    Atom("scriptlevel", "scriptlevel"),
    Atom("scriptminsize", "scriptminsize"),
    Atom("scriptsizemultiplier", "scriptsizemultiplier"),
    Atom("scriptsize", "scriptsize"),
    Atom("sdev", "sdev"),
    Atom("sech", "sech"),
    Atom("sec", "sec"),
    Atom("selection", "selection"),
    Atom("selector", "selector"),
    Atom("semantics", "semantics"),
    Atom("separator", "separator"),
    Atom("separators", "separators"),
    Atom("sep", "sep"),
    Atom("setdiff", "setdiff"),
    # Atom("set", "set"),  # "set" is present above
    Atom("share", "share"),
    Atom("shift", "shift"),
    Atom("side", "side"),
    Atom("sinh", "sinh"),
    Atom("sin", "sin"),
    Atom("stackalign", "stackalign"),
    Atom("stretchy", "stretchy"),
    Atom("subscriptshift", "subscriptshift"),
    Atom("subset", "subset"),
    Atom("superscriptshift", "superscriptshift"),
    Atom("symmetric", "symmetric"),
    Atom("tanh", "tanh"),
    Atom("tan", "tan"),
    Atom("tendsto", "tendsto"),
    Atom("times", "times"),
    Atom("transpose", "transpose"),
    Atom("union_", "union"),
    Atom("uplimit", "uplimit"),
    Atom("variance", "variance"),
    Atom("vectorproduct", "vectorproduct"),
    Atom("vector", "vector"),
    Atom("voffset", "voffset"),
    Atom("xref", "xref"),
    Atom("math", "math"),  # the only one without an underscore
    Atom("booleanFromString", "boolean-from-string"),
    Atom("countNonEmpty", "count-non-empty"),
    Atom("daysFromDate", "days-from-date"),
    Atom("secondsFromDateTime", "seconds-from-dateTime"),
    Atom("tabbrowser_arrowscrollbox", "tabbrowser-arrowscrollbox"),
    # Simple gestures support
    Atom("onMozSwipeGestureMayStart", "onMozSwipeGestureMayStart"),
    Atom("onMozSwipeGestureStart", "onMozSwipeGestureStart"),
    Atom("onMozSwipeGestureUpdate", "onMozSwipeGestureUpdate"),
    Atom("onMozSwipeGestureEnd", "onMozSwipeGestureEnd"),
    Atom("onMozSwipeGesture", "onMozSwipeGesture"),
    Atom("onMozMagnifyGestureStart", "onMozMagnifyGestureStart"),
    Atom("onMozMagnifyGestureUpdate", "onMozMagnifyGestureUpdate"),
    Atom("onMozMagnifyGesture", "onMozMagnifyGesture"),
    Atom("onMozRotateGestureStart", "onMozRotateGestureStart"),
    Atom("onMozRotateGestureUpdate", "onMozRotateGestureUpdate"),
    Atom("onMozRotateGesture", "onMozRotateGesture"),
    Atom("onMozTapGesture", "onMozTapGesture"),
    Atom("onMozPressTapGesture", "onMozPressTapGesture"),
    Atom("onMozEdgeUIStarted", "onMozEdgeUIStarted"),
    Atom("onMozEdgeUICanceled", "onMozEdgeUICanceled"),
    Atom("onMozEdgeUICompleted", "onMozEdgeUICompleted"),
    # Pointer events
    Atom("onpointerdown", "onpointerdown"),
    Atom("onpointermove", "onpointermove"),
    Atom("onpointerup", "onpointerup"),
    Atom("onpointercancel", "onpointercancel"),
    Atom("onpointerover", "onpointerover"),
    Atom("onpointerout", "onpointerout"),
    Atom("onpointerenter", "onpointerenter"),
    Atom("onpointerleave", "onpointerleave"),
    Atom("onpointerrawupdate", "onpointerrawupdate"),
    Atom("ongotpointercapture", "ongotpointercapture"),
    Atom("onlostpointercapture", "onlostpointercapture"),
    # orientation support
    Atom("ondevicemotion", "ondevicemotion"),
    Atom("ondeviceorientation", "ondeviceorientation"),
    Atom("ondeviceorientationabsolute", "ondeviceorientationabsolute"),
    Atom("onmozorientationchange", "onmozorientationchange"),
    Atom("onuserproximity", "onuserproximity"),
    # light sensor support
    Atom("ondevicelight", "ondevicelight"),
    # MediaDevices device change event
    Atom("ondevicechange", "ondevicechange"),
    # WebRTC events
    Atom("onrtctransform", "onrtctransform"),
    # Internal Visual Viewport events
    Atom("onmozvisualresize", "onmozvisualresize"),
    Atom("onmozvisualscroll", "onmozvisualscroll"),
    # Miscellaneous events included for memory usage optimization (see bug 1542885)
    Atom("onDOMContentLoaded", "onDOMContentLoaded"),
    Atom("onDOMDocElementInserted", "onDOMDocElementInserted"),
    Atom("onDOMFormBeforeSubmit", "onDOMFormBeforeSubmit"),
    Atom("onDOMFormHasPassword", "onDOMFormHasPassword"),
    Atom("onDOMFrameContentLoaded", "onDOMFrameContentLoaded"),
    Atom("onDOMHeadElementParsed", "onDOMHeadElementParsed"),
    Atom("onDOMInputPasswordAdded", "onDOMInputPasswordAdded"),
    Atom("onDOMLinkAdded", "onDOMLinkAdded"),
    Atom("onDOMLinkChanged", "onDOMLinkChanged"),
    Atom("onDOMMetaAdded", "onDOMMetaAdded"),
    Atom("onDOMMetaChanged", "onDOMMetaChanged"),
    Atom("onDOMMetaRemoved", "onDOMMetaRemoved"),
    Atom("onDOMPopupBlocked", "onDOMPopupBlocked"),
    Atom("onDOMTitleChanged", "onDOMTitleChanged"),
    Atom("onDOMWindowClose", "onDOMWindowClose"),
    Atom("onDOMWindowCreated", "onDOMWindowCreated"),
    Atom("onDOMWindowFocus", "onDOMWindowFocus"),
    Atom("onFullZoomChange", "onFullZoomChange"),
    Atom("onGloballyAutoplayBlocked", "onGloballyAutoplayBlocked"),
    Atom("onMozDOMFullscreen_Entered", "onMozDOMFullscreen:Entered"),
    Atom("onMozDOMFullscreen_Exit", "onMozDOMFullscreen:Exit"),
    Atom("onMozDOMFullscreen_Exited", "onMozDOMFullscreen:Exited"),
    Atom("onMozDOMFullscreen_NewOrigin", "onMozDOMFullscreen:NewOrigin"),
    Atom("onMozDOMFullscreen_Request", "onMozDOMFullscreen:Request"),
    Atom("onMozDOMPointerLock_Entered", "onMozDOMPointerLock:Entered"),
    Atom("onMozDOMPointerLock_Exited", "onMozDOMPointerLock:Exited"),
    Atom("onMozInvalidForm", "onMozInvalidForm"),
    Atom("onMozLocalStorageChanged", "onMozLocalStorageChanged"),
    Atom("onMozOpenDateTimePicker", "onMozOpenDateTimePicker"),
    Atom("onMozSessionStorageChanged", "onMozSessionStorageChanged"),
    Atom("onMozTogglePictureInPicture", "onMozTogglePictureInPicture"),
    Atom("onPluginCrashed", "onPluginCrashed"),
    Atom("onPrintingError", "onPrintingError"),
    Atom("onTextZoomChange", "onTextZoomChange"),
    Atom("onUAWidgetSetupOrChange", "onUAWidgetSetupOrChange"),
    Atom("onUAWidgetTeardown", "onUAWidgetTeardown"),
    Atom("onUnselectedTabHover_Disable", "onUnselectedTabHover:Disable"),
    Atom("onUnselectedTabHover_Enable", "onUnselectedTabHover:Enable"),
    Atom("onmozshowdropdown", "onmozshowdropdown"),
    Atom("onmozshowdropdown_sourcetouch", "onmozshowdropdown-sourcetouch"),
    Atom("onprintPreviewUpdate", "onprintPreviewUpdate"),
    Atom("onscrollend", "onscrollend"),
    Atom("onbeforetoggle", "onbeforetoggle"),
    # WebExtensions
    Atom("moz_extension", "moz-extension"),
    Atom("all_urlsPermission", "<all_urls>"),
    Atom("clipboardRead", "clipboardRead"),
    Atom("clipboardWrite", "clipboardWrite"),
    Atom("debugger", "debugger"),
    Atom("mozillaAddons", "mozillaAddons"),
    Atom("tabs", "tabs"),
    Atom("webRequestBlocking", "webRequestBlocking"),
    Atom("webRequestFilterResponse_serviceWorkerScript", "webRequestFilterResponse.serviceWorkerScript"),
    Atom("blob", "blob"),
    Atom("http", "http"),
    Atom("https", "https"),
    Atom("view_source", "view-source"),
    Atom("ws", "ws"),
    Atom("wss", "wss"),
    Atom("ftp", "ftp"),
    Atom("chrome", "chrome"),
    Atom("moz", "moz"),
    Atom("moz_icon", "moz-icon"),
    Atom("moz_gio", "moz-gio"),
    Atom("proxy", "proxy"),
    Atom("privateBrowsingAllowedPermission", "internal:privateBrowsingAllowed"),
    Atom("svgContextPropertiesAllowedPermission", "internal:svgContextPropertiesAllowed"),
    Atom("theme", "theme"),
    Atom("userScripts", "userScripts"),
    # CSS Counter Styles
    Atom("decimal_leading_zero", "decimal-leading-zero"),
    Atom("arabic_indic", "arabic-indic"),
    Atom("armenian", "armenian"),
    Atom("upper_armenian", "upper-armenian"),
    Atom("lower_armenian", "lower-armenian"),
    Atom("bengali", "bengali"),
    Atom("cambodian", "cambodian"),
    Atom("khmer", "khmer"),
    Atom("cjk_decimal", "cjk-decimal"),
    Atom("devanagari", "devanagari"),
    Atom("georgian", "georgian"),
    Atom("gujarati", "gujarati"),
    Atom("gurmukhi", "gurmukhi"),
    Atom("kannada", "kannada"),
    Atom("lao", "lao"),
    Atom("malayalam", "malayalam"),
    Atom("mongolian", "mongolian"),
    Atom("myanmar", "myanmar"),
    Atom("oriya", "oriya"),
    Atom("persian", "persian"),
    Atom("lower_roman", "lower-roman"),
    Atom("upper_roman", "upper-roman"),
    Atom("tamil", "tamil"),
    Atom("telugu", "telugu"),
    Atom("thai", "thai"),
    Atom("tibetan", "tibetan"),
    Atom("lower_alpha", "lower-alpha"),
    Atom("lower_latin", "lower-latin"),
    Atom("upper_alpha", "upper-alpha"),
    Atom("upper_latin", "upper-latin"),
    Atom("cjk_heavenly_stem", "cjk-heavenly-stem"),
    Atom("cjk_earthly_branch", "cjk-earthly-branch"),
    Atom("lower_greek", "lower-greek"),
    Atom("hiragana", "hiragana"),
    Atom("hiragana_iroha", "hiragana-iroha"),
    Atom("katakana", "katakana"),
    Atom("katakana_iroha", "katakana-iroha"),
    Atom("cjk_ideographic", "cjk-ideographic"),
    Atom("_moz_arabic_indic", "-moz-arabic-indic"),
    Atom("_moz_persian", "-moz-persian"),
    Atom("_moz_urdu", "-moz-urdu"),
    Atom("_moz_devanagari", "-moz-devanagari"),
    Atom("_moz_bengali", "-moz-bengali"),
    Atom("_moz_gurmukhi", "-moz-gurmukhi"),
    Atom("_moz_gujarati", "-moz-gujarati"),
    Atom("_moz_oriya", "-moz-oriya"),
    Atom("_moz_tamil", "-moz-tamil"),
    Atom("_moz_telugu", "-moz-telugu"),
    Atom("_moz_kannada", "-moz-kannada"),
    Atom("_moz_malayalam", "-moz-malayalam"),
    Atom("_moz_thai", "-moz-thai"),
    Atom("_moz_lao", "-moz-lao"),
    Atom("_moz_myanmar", "-moz-myanmar"),
    Atom("_moz_khmer", "-moz-khmer"),
    Atom("_moz_cjk_heavenly_stem", "-moz-cjk-heavenly-stem"),
    Atom("_moz_cjk_earthly_branch", "-moz-cjk-earthly-branch"),
    Atom("_moz_hangul", "-moz-hangul"),
    Atom("_moz_hangul_consonant", "-moz-hangul-consonant"),
    Atom("_moz_ethiopic_halehame", "-moz-ethiopic-halehame"),
    Atom("_moz_ethiopic_halehame_am", "-moz-ethiopic-halehame-am"),
    Atom("_moz_ethiopic_halehame_ti_er", "-moz-ethiopic-halehame-ti-er"),
    Atom("_moz_ethiopic_halehame_ti_et", "-moz-ethiopic-halehame-ti-et"),
    Atom("_moz_trad_chinese_informal", "-moz-trad-chinese-informal"),
    Atom("_moz_trad_chinese_formal", "-moz-trad-chinese-formal"),
    Atom("_moz_simp_chinese_informal", "-moz-simp-chinese-informal"),
    Atom("_moz_simp_chinese_formal", "-moz-simp-chinese-formal"),
    Atom("_moz_japanese_informal", "-moz-japanese-informal"),
    Atom("_moz_japanese_formal", "-moz-japanese-formal"),
    Atom("_moz_ethiopic_numeric", "-moz-ethiopic-numeric"),
    # --------------------------------------------------------------------------
    # Special atoms
    # --------------------------------------------------------------------------
    # Node types
    Atom("cdataTagName", "#cdata-section"),
    Atom("commentTagName", "#comment"),
    Atom("documentNodeName", "#document"),
    Atom("documentFragmentNodeName", "#document-fragment"),
    Atom("documentTypeNodeName", "#document-type"),
    Atom("processingInstructionTagName", "#processing-instruction"),
    Atom("textTagName", "#text"),
    # Frame types
    #
    # TODO(emilio): Rename this? This is only used now to mark the style context of
    # the placeholder with a dummy pseudo.
    Atom("placeholderFrame", "PlaceholderFrame"),
    Atom("onloadend", "onloadend"),
    Atom("onloadstart", "onloadstart"),
    Atom("onprogress", "onprogress"),
    Atom("onsuspend", "onsuspend"),
    Atom("onemptied", "onemptied"),
    Atom("onstalled", "onstalled"),
    Atom("onplay", "onplay"),
    Atom("onpause", "onpause"),
    Atom("onloadedmetadata", "onloadedmetadata"),
    Atom("onloadeddata", "onloadeddata"),
    Atom("onwaiting", "onwaiting"),
    Atom("onplaying", "onplaying"),
    Atom("oncanplay", "oncanplay"),
    Atom("oncanplaythrough", "oncanplaythrough"),
    Atom("onseeking", "onseeking"),
    Atom("onseeked", "onseeked"),
    Atom("ontimeout", "ontimeout"),
    Atom("ontimeupdate", "ontimeupdate"),
    Atom("onended", "onended"),
    Atom("onformdata", "onformdata"),
    Atom("onratechange", "onratechange"),
    Atom("ondurationchange", "ondurationchange"),
    Atom("onvolumechange", "onvolumechange"),
    Atom("onaddtrack", "onaddtrack"),
    Atom("oncontrollerchange", "oncontrollerchange"),
    Atom("oncuechange", "oncuechange"),
    Atom("onenter", "onenter"),
    Atom("onexit", "onexit"),
    Atom("onencrypted", "onencrypted"),
    Atom("onwaitingforkey", "onwaitingforkey"),
    Atom("onkeystatuseschange", "onkeystatuseschange"),
    Atom("onremovetrack", "onremovetrack"),
    Atom("loadstart", "loadstart"),
    Atom("suspend", "suspend"),
    Atom("emptied", "emptied"),
    Atom("play", "play"),
    Atom("pause", "pause"),
    Atom("loadedmetadata", "loadedmetadata"),
    Atom("loadeddata", "loadeddata"),
    Atom("waiting", "waiting"),
    Atom("playing", "playing"),
    Atom("timeupdate", "timeupdate"),
    Atom("canplay", "canplay"),
    Atom("canplaythrough", "canplaythrough"),
    Atom("ondataavailable", "ondataavailable"),
    Atom("onwarning", "onwarning"),
    Atom("onstart", "onstart"),
    Atom("onstop", "onstop"),
    Atom("onphoto", "onphoto"),
    Atom("ongamepadbuttondown", "ongamepadbuttondown"),
    Atom("ongamepadbuttonup", "ongamepadbuttonup"),
    Atom("ongamepadaxismove", "ongamepadaxismove"),
    Atom("ongamepadconnected", "ongamepadconnected"),
    Atom("ongamepaddisconnected", "ongamepaddisconnected"),
    Atom("onfetch", "onfetch"),
    # Content property names
    Atom("afterPseudoProperty", "afterPseudoProperty"),  # nsXMLElement*
    Atom("beforePseudoProperty", "beforePseudoProperty"),  # nsXMLElement*
    Atom("cssPseudoElementBeforeProperty", "CSSPseudoElementBeforeProperty"),  # CSSPseudoElement*
    Atom("cssPseudoElementAfterProperty", "CSSPseudoElementAfterProperty"),  # CSSPseudoElement*
    Atom("cssPseudoElementMarkerProperty", "CSSPseudoElementMarkerProperty"),  # CSSPseudoElement*
    Atom("genConInitializerProperty", "QuoteNodeProperty"),
    Atom("labelMouseDownPtProperty", "LabelMouseDownPtProperty"),
    Atom("lockedStyleStates", "lockedStyleStates"),
    Atom("apzCallbackTransform", "apzCallbackTransform"),
    Atom("apzDisabled", "ApzDisabled"),  # bool
    Atom("restylableAnonymousNode", "restylableAnonymousNode"),  # bool
    Atom("docLevelNativeAnonymousContent", "docLevelNativeAnonymousContent"),  # bool
    Atom("paintRequestTime", "PaintRequestTime"),
    Atom("pseudoProperty", "PseudoProperty"),  # PseudoStyleType
    Atom("manualNACProperty", "ManualNACProperty"),  # ManualNAC*
    Atom("markerPseudoProperty", "markerPseudoProperty"),  # nsXMLElement*
    # Languages for lang-specific transforms
    Atom("Japanese", "ja"),
    Atom("Chinese", "zh-CN"),
    Atom("Taiwanese", "zh-TW"),
    Atom("HongKongChinese", "zh-HK"),
    Atom("Unicode", "x-unicode"),
    # language codes specifically referenced in the gfx code
    Atom("ko", "ko"),
    Atom("zh_cn", "zh-cn"),
    Atom("zh_tw", "zh-tw"),
    # additional codes used in nsUnicodeRange.cpp
    Atom("x_cyrillic", "x-cyrillic"),
    Atom("he", "he"),
    Atom("ar", "ar"),
    Atom("x_devanagari", "x-devanagari"),
    Atom("x_tamil", "x-tamil"),
    Atom("x_armn", "x-armn"),
    Atom("x_beng", "x-beng"),
    Atom("x_cans", "x-cans"),
    Atom("x_ethi", "x-ethi"),
    Atom("x_geor", "x-geor"),
    Atom("x_gujr", "x-gujr"),
    Atom("x_guru", "x-guru"),
    Atom("x_khmr", "x-khmr"),
    Atom("x_knda", "x-knda"),
    Atom("x_mlym", "x-mlym"),
    Atom("x_orya", "x-orya"),
    Atom("x_sinh", "x-sinh"),
    Atom("x_telu", "x-telu"),
    Atom("x_tibt", "x-tibt"),
    # additional languages that have special case transformations
    Atom("az", "az"),
    Atom("ba", "ba"),
    Atom("crh", "crh"),
    # Atom("el", "el"),  # "el" is present above
    Atom("ga", "ga"),
    # Atom("lt", "lt"),  # "lt" is present above (atom name "lt_")
    Atom("nl", "nl"),
    # mathematical language, used for MathML
    Atom("x_math", "x-math"),
    # other languages mentioned in :lang() rules in UA style sheets
    Atom("zh", "zh"),
    # Names for editor transactions
    Atom("TypingTxnName", "Typing"),
    Atom("IMETxnName", "IME"),
    Atom("DeleteTxnName", "Deleting"),
    # Font families
    Atom("serif", "serif"),
    Atom("sans_serif", "sans-serif"),
    Atom("cursive", "cursive"),
    Atom("fantasy", "fantasy"),
    Atom("monospace", "monospace"),
    Atom("mozfixed", "-moz-fixed"),
    Atom("moz_fixed_pos_containing_block", "-moz-fixed-pos-containing-block"),
    # Standard font-palette identifiers
    Atom("light", "light"),
    Atom("dark", "dark"),
    # IPC stuff
    # Atom("Remote", "remote"),  # "remote" is present above
    Atom("RemoteId", "_remote_id"),
    Atom("RemoteType", "remoteType"),
    Atom("DisplayPort", "_displayport"),
    Atom("DisplayPortMargins", "_displayportmargins"),
    Atom("DisplayPortBase", "_displayportbase"),
    Atom("MinimalDisplayPort", "_minimaldisplayport"),
    Atom("forceMousewheelAutodir", "_force_mousewheel_autodir"),
    Atom("forceMousewheelAutodirHonourRoot", "_force_mousewheel_autodir_honourroot"),
    Atom("forcemessagemanager", "forcemessagemanager"),
    Atom("initialBrowsingContextGroupId", "initialBrowsingContextGroupId"),
    Atom("manualactiveness", "manualactiveness"),
    # Names for system metrics.
    Atom("_moz_bool_pref", "-moz-bool-pref"),
    Atom("_moz_scrollbar_start_backward", "-moz-scrollbar-start-backward"),
    Atom("_moz_scrollbar_start_forward", "-moz-scrollbar-start-forward"),
    Atom("_moz_scrollbar_end_backward", "-moz-scrollbar-end-backward"),
    Atom("_moz_scrollbar_end_forward", "-moz-scrollbar-end-forward"),
    Atom("_moz_overlay_scrollbars", "-moz-overlay-scrollbars"),
    Atom("_moz_overlay_scrollbar_fade_duration", "-moz-overlay-scrollbar-fade-duration"),
    Atom("_moz_windows_accent_color_in_titlebar", "-moz-windows-accent-color-in-titlebar"),
    Atom("_moz_windows_accent_color_in_tabs", "-moz-windows-accent-color-in-tabs"),
    Atom("_moz_windows_mica", "-moz-windows-mica"),
    Atom("_moz_windows_mica_popups", "-moz-windows-mica-popups"),
    Atom("_moz_mac_big_sur_theme", "-moz-mac-big-sur-theme"),
    Atom("_moz_mac_rtl", "-moz-mac-rtl"),
    Atom("_moz_mac_titlebar_height", "-moz-mac-titlebar-height"),
    Atom("_moz_platform", "-moz-platform"),
    Atom("_moz_gtk_theme_family", "-moz-gtk-theme-family"),
    Atom("_moz_menubar_drag", "-moz-menubar-drag"),
    Atom("_moz_device_pixel_ratio", "-moz-device-pixel-ratio"),
    Atom("_moz_device_orientation", "-moz-device-orientation"),
    Atom("_moz_is_resource_document", "-moz-is-resource-document"),
    Atom("_moz_swipe_animation_enabled", "-moz-swipe-animation-enabled"),
    Atom("_moz_gtk_csd_available", "-moz-gtk-csd-available"),
    Atom("_moz_gtk_csd_transparency_available", "-moz-gtk-csd-transparency-available"),
    Atom("_moz_gtk_csd_titlebar_radius", "-moz-gtk-csd-titlebar-radius"),
    Atom("_moz_gtk_csd_tooltip_radius", "-moz-gtk-csd-tooltip-radius"),
    Atom("_moz_gtk_csd_titlebar_button_spacing", "-moz-gtk-csd-titlebar-button-spacing"),
    Atom("_moz_gtk_csd_minimize_button", "-moz-gtk-csd-minimize-button"),
    Atom("_moz_gtk_csd_minimize_button_position", "-moz-gtk-csd-minimize-button-position"),
    Atom("_moz_gtk_csd_maximize_button", "-moz-gtk-csd-maximize-button"),
    Atom("_moz_gtk_csd_maximize_button_position", "-moz-gtk-csd-maximize-button-position"),
    Atom("_moz_gtk_csd_close_button", "-moz-gtk-csd-close-button"),
    Atom("_moz_gtk_csd_close_button_position", "-moz-gtk-csd-close-button-position"),
    Atom("_moz_gtk_csd_reversed_placement", "-moz-gtk-csd-reversed-placement"),
    Atom("_moz_gtk_csd_rounded_bottom_corners", "-moz-gtk-csd-rounded-bottom-corners"),
    Atom("_moz_content_prefers_color_scheme", "-moz-content-prefers-color-scheme"),
    Atom("_moz_content_preferred_color_scheme", "-moz-content-preferred-color-scheme"),
    Atom("_moz_system_dark_theme", "-moz-system-dark-theme"),
    Atom("_moz_panel_animations", "-moz-panel-animations"),
    # application commands
    Atom("Back", "Back"),
    Atom("Forward", "Forward"),
    Atom("Reload", "Reload"),
    Atom("Stop", "Stop"),
    Atom("Search", "Search"),
    Atom("Bookmarks", "Bookmarks"),
    Atom("Home", "Home"),
    Atom("NextTrack", "NextTrack"),
    Atom("PreviousTrack", "PreviousTrack"),
    Atom("MediaStop", "MediaStop"),
    Atom("PlayPause", "PlayPause"),
    Atom("New", "New"),
    Atom("Open", "Open"),
    Atom("Close", "Close"),
    Atom("Save", "Save"),
    Atom("Find", "Find"),
    Atom("Help", "Help"),
    Atom("Print", "Print"),
    Atom("SendMail", "SendMail"),
    Atom("ForwardMail", "ForwardMail"),
    Atom("ReplyToMail", "ReplyToMail"),
    Atom("alert", "alert"),
    Atom("alertdialog", "alertdialog"),
    Atom("application", "application"),
    Atom("aria_colcount", "aria-colcount"),
    Atom("aria_colindex", "aria-colindex"),
    Atom("aria_colindextext", "aria-colindextext"),
    Atom("aria_colspan", "aria-colspan"),
    Atom("aria_details", "aria-details"),
    Atom("aria_errormessage", "aria-errormessage"),
    Atom("aria_grabbed", "aria-grabbed"),
    Atom("aria_keyshortcuts", "aria-keyshortcuts"),
    Atom("aria_label", "aria-label"),
    Atom("aria_modal", "aria-modal"),
    Atom("aria_orientation", "aria-orientation"),
    Atom("aria_placeholder", "aria-placeholder"),
    Atom("aria_roledescription", "aria-roledescription"),
    Atom("aria_rowcount", "aria-rowcount"),
    Atom("aria_rowindex", "aria-rowindex"),
    Atom("aria_rowindextext", "aria-rowindextext"),
    Atom("aria_rowspan", "aria-rowspan"),
    Atom("aria_valuetext", "aria-valuetext"),
    Atom("assertive", "assertive"),
    Atom("auto_generated", "auto-generated"),
    Atom("banner", "banner"),
    Atom("checkable", "checkable"),
    Atom("columnheader", "columnheader"),
    Atom("complementary", "complementary"),
    Atom("containerAtomic", "container-atomic"),
    Atom("containerBusy", "container-busy"),
    Atom("containerLive", "container-live"),
    Atom("containerLiveRole", "container-live-role"),
    Atom("containerRelevant", "container-relevant"),
    Atom("contentinfo", "contentinfo"),
    Atom("cycles", "cycles"),
    Atom("datatable", "datatable"),
    Atom("feed", "feed"),
    Atom("grammar", "grammar"),
    Atom("gridcell", "gridcell"),
    Atom("heading", "heading"),
    Atom("inlinevalue", "inline"),
    Atom("inline_size", "inline-size"),
    Atom("invalid", "invalid"),
    Atom("lineNumber", "line-number"),
    Atom("menuitemcheckbox", "menuitemcheckbox"),
    Atom("menuitemradio", "menuitemradio"),
    # Atom("mixed", "mixed"),  # "mixed" is present above
    Atom("navigation", "navigation"),
    Atom("polite", "polite"),
    Atom("posinset", "posinset"),
    Atom("presentation", "presentation"),
    Atom("progressbar", "progressbar"),
    Atom("region", "region"),
    Atom("rowgroup", "rowgroup"),
    Atom("rowheader", "rowheader"),
    Atom("search", "search"),
    Atom("searchbox", "searchbox"),
    Atom("setsize", "setsize"),
    Atom("spelling", "spelling"),
    Atom("spinbutton", "spinbutton"),
    Atom("status", "status"),
    Atom("tableCellIndex", "table-cell-index"),
    Atom("tablist", "tablist"),
    Atom("textIndent", "text-indent"),
    Atom("textInputType", "text-input-type"),
    Atom("textLineThroughColor", "text-line-through-color"),
    Atom("textLineThroughStyle", "text-line-through-style"),
    Atom("textPosition", "text-position"),
    Atom("textUnderlineColor", "text-underline-color"),
    Atom("textUnderlineStyle", "text-underline-style"),
    Atom("timer", "timer"),
    Atom("toolbarname", "toolbarname"),
    Atom("toolbarseparator", "toolbarseparator"),
    Atom("toolbarspacer", "toolbarspacer"),
    Atom("toolbarspring", "toolbarspring"),
    Atom("treegrid", "treegrid"),
    Atom("_undefined", "undefined"),
    Atom("xmlroles", "xml-roles"),
    # MathML xml roles
    Atom("close_fence", "close-fence"),
    Atom("denominator", "denominator"),
    Atom("numerator", "numerator"),
    Atom("open_fence", "open-fence"),
    Atom("overscript", "overscript"),
    Atom("presubscript", "presubscript"),
    Atom("presuperscript", "presuperscript"),
    Atom("root_index", "root-index"),
    Atom("subscript", "subscript"),
    Atom("superscript", "superscript"),
    Atom("underscript", "underscript"),
    Atom("onaudiostart", "onaudiostart"),
    Atom("onaudioend", "onaudioend"),
    Atom("onsoundstart", "onsoundstart"),
    Atom("onsoundend", "onsoundend"),
    Atom("onspeechstart", "onspeechstart"),
    Atom("onspeechend", "onspeechend"),
    Atom("onresult", "onresult"),
    Atom("onnomatch", "onnomatch"),
    Atom("onresume", "onresume"),
    Atom("onmark", "onmark"),
    Atom("onboundary", "onboundary"),
    # Media Controller
    Atom("onactivated", "onactivated"),
    Atom("ondeactivated", "ondeactivated"),
    Atom("onmetadatachange", "onmetadatachange"),
    Atom("onplaybackstatechange", "onplaybackstatechange"),
    Atom("onpositionstatechange", "onpositionstatechange"),
    Atom("onsupportedkeyschange", "onsupportedkeyschange"),
    # Media query prefs for UA sheets.
    Atom("mathml_legacy_mathvariant_attribute_disabled", "mathml.legacy_mathvariant_attribute.disabled"),
    Atom("layout_css_always_underline_links", "layout.css.always_underline_links"),
    Atom("layout_css_android_pip_enabled", "layout.css.android-pip.enabled"),
    Atom("layout_css_cached_scrollbar_styles_enabled", "layout.css.cached-scrollbar-styles.enabled"),
    Atom("layout_testing_scrollbars_always_hidden", "layout.testing.scrollbars.always-hidden"),
    # Contextual Identity / Containers
    Atom("usercontextid", "usercontextid"),
    Atom("geckoViewSessionContextId", "geckoViewSessionContextId"),
    # Namespaces
    Atom("nsuri_xmlns", "http://www.w3.org/2000/xmlns/"),
    Atom("nsuri_xml", "http://www.w3.org/XML/1998/namespace"),
    Atom("nsuri_xhtml", "http://www.w3.org/1999/xhtml"),
    Atom("nsuri_xlink", "http://www.w3.org/1999/xlink"),
    Atom("nsuri_xslt", "http://www.w3.org/1999/XSL/Transform"),
    Atom("nsuri_mathml", "http://www.w3.org/1998/Math/MathML"),
    Atom("nsuri_rdf", "http://www.w3.org/1999/02/22-rdf-syntax-ns#"),
    Atom("nsuri_xul", "http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"),
    Atom("nsuri_svg", "http://www.w3.org/2000/svg"),
    Atom("nsuri_parsererror", "http://www.mozilla.org/newlayout/xml/parsererror.xml"),
    # MSE
    Atom("onsourceopen", "onsourceopen"),
    Atom("onsourceended", "onsourceended"),
    Atom("onsourceclose", "onsourceclose"),
    Atom("onupdatestart", "onupdatestart"),
    Atom("onupdate", "onupdate"),
    Atom("onupdateend", "onupdateend"),
    Atom("onaddsourcebuffer", "onaddsourcebuffer"),
    Atom("onremovesourcebuffer", "onremovesourcebuffer"),
    # WebCodecs
    Atom("ondequeue", "ondequeue"),
    # RDF (not used by mozilla-central, but still used by comm-central)
    Atom("about", "about"),
    Atom("ID", "ID"),
    Atom("nodeID", "nodeID"),
    Atom("aboutEach", "aboutEach"),
    Atom("resource", "resource"),
    Atom("RDF", "RDF"),
    Atom("Description", "Description"),
    Atom("Bag", "Bag"),
    Atom("Seq", "Seq"),
    Atom("Alt", "Alt"),
    # Atom("kLiAtom", "li"),  # "li" is present above
    # Atom("kXMLNSAtom", "xmlns"),  # "xmlns" is present above
    Atom("parseType", "parseType"),
    # Directory service
    Atom("DirectoryService_CurrentProcess", "XCurProcD"),
    Atom("DirectoryService_GRE_Directory", "GreD"),
    Atom("DirectoryService_GRE_BinDirectory", "GreBinD"),
    Atom("DirectoryService_OS_TemporaryDirectory", "TmpD"),
    Atom("DirectoryService_OS_CurrentProcessDirectory", "CurProcD"),
    Atom("DirectoryService_OS_CurrentWorkingDirectory", "CurWorkD"),
    Atom("DirectoryService_OS_SystemConfigDir", "SysConfD"),
    # Atom("DirectoryService_OS_HomeDirectory", "Home"),  # "Home" is present above
    Atom("DirectoryService_OS_DesktopDirectory", "Desk"),
    Atom("DirectoryService_OS_DocumentsDirectory", "Docs"),
    Atom("DirectoryService_InitCurrentProcess_dummy", "MozBinD"),
    Atom("DirectoryService_SystemDirectory", "SysD"),
    Atom("DirectoryService_UserLibDirectory", "ULibDir"),
    Atom("DirectoryService_DefaultDownloadDirectory", "DfltDwnld"),
    Atom("DirectoryService_LocalApplicationsDirectory", "LocApp"),
    Atom("DirectoryService_UserPreferencesDirectory", "UsrPrfs"),
    Atom("DirectoryService_PictureDocumentsDirectory", "Pct"),
    Atom("DirectoryService_DefaultScreenshotDirectory", "Scrnshts"),
    Atom("DirectoryService_WindowsDirectory", "WinD"),
    Atom("DirectoryService_WindowsProgramFiles", "ProgF"),
    Atom("DirectoryService_Programs", "Progs"),
    Atom("DirectoryService_Favorites", "Favs"),
    Atom("DirectoryService_Appdata", "AppData"),
    Atom("DirectoryService_LocalAppdata", "LocalAppData"),
    Atom("DirectoryService_WinCookiesDirectory", "CookD"),
    Atom("_ua_view_transition_fade_out", "-ua-view-transition-fade-out"),
    Atom("_ua_view_transition_fade_in", "-ua-view-transition-fade-in"),
    Atom("_ua_mix_blend_mode_plus_lighter", "-ua-mix-blend-mode-plus-lighter"),
    # CSS pseudo-elements -- these must appear in the same order as
    # in nsCSSPseudoElementList.h
    PseudoElementAtom("PseudoElement_after", ":after"),
    PseudoElementAtom("PseudoElement_before", ":before"),
    PseudoElementAtom("PseudoElement_marker", ":marker"),
    PseudoElementAtom("PseudoElement_backdrop", ":backdrop"),
    PseudoElementAtom("PseudoElement_cue", ":cue"),
    PseudoElementAtom("PseudoElement_firstLetter", ":first-letter"),
    PseudoElementAtom("PseudoElement_firstLine", ":first-line"),
    PseudoElementAtom("PseudoElement_highlight", ":highlight"),
    PseudoElementAtom("PseudoElement_selection", ":selection"),
    PseudoElementAtom("PseudoElement_targetText", ":target-text"),
    PseudoElementAtom("PseudoElement_viewTransition", ":view-transition"),
    PseudoElementAtom("PseudoElement_viewTransitionGroup", ":view-transition-group"),
    PseudoElementAtom("PseudoElement_viewTransitionImagePair", ":view-transition-image-pair"),
    PseudoElementAtom("PseudoElement_viewTransitionOld", ":view-transition-old"),
    PseudoElementAtom("PseudoElement_viewTransitionNew", ":view-transition-new"),
    PseudoElementAtom("PseudoElement_mozSnapshotContainingBlock", ":-moz-snapshot-containing-block"),
    PseudoElementAtom("PseudoElement_mozNumberSpinBox", ":-moz-number-spin-box"),
    PseudoElementAtom("PseudoElement_mozNumberSpinUp", ":-moz-number-spin-up"),
    PseudoElementAtom("PseudoElement_mozNumberSpinDown", ":-moz-number-spin-down"),
    PseudoElementAtom("PseudoElement_mozSearchClearButton", ":-moz-search-clear-button"),
    PseudoElementAtom("PseudoElement_mozProgressBar", ":-moz-progress-bar"),
    PseudoElementAtom("PseudoElement_mozRangeTrack", ":-moz-range-track"),
    PseudoElementAtom("PseudoElement_mozRangeProgress", ":-moz-range-progress"),
    PseudoElementAtom("PseudoElement_mozRangeThumb", ":-moz-range-thumb"),
    PseudoElementAtom("PseudoElement_mozMeterBar", ":-moz-meter-bar"),
    PseudoElementAtom("PseudoElement_placeholder", ":placeholder"),
    PseudoElementAtom("PseudoElement_mozColorSwatch", ":-moz-color-swatch"),
    PseudoElementAtom("PseudoElement_mozTextControlEditingRoot", ":-moz-text-control-editing-root"),
    PseudoElementAtom("PseudoElement_mozTextControlPreview", ":-moz-text-control-preview"),
    PseudoElementAtom("PseudoElement_mozReveal", ":-moz-reveal"),
    PseudoElementAtom("PseudoElement_fileSelectorButton", ":file-selector-button"),
    PseudoElementAtom("PseudoElement_sliderTrack", ":slider-track"),
    PseudoElementAtom("PseudoElement_sliderThumb", ":slider-thumb"),
    PseudoElementAtom("PseudoElement_sliderFill", ":slider-fill"),
    PseudoElementAtom("PseudoElement_detailsContent", ":details-content"),
    # CSS anonymous boxes -- these must appear in the same order as
    # in nsCSSAnonBoxList.h
    NonInheritingAnonBoxAtom("AnonBox_oofPlaceholder", ":-moz-oof-placeholder"),
    NonInheritingAnonBoxAtom("AnonBox_horizontalFramesetBorder", ":-moz-hframeset-border"),
    NonInheritingAnonBoxAtom("AnonBox_verticalFramesetBorder", ":-moz-vframeset-border"),
    NonInheritingAnonBoxAtom("AnonBox_framesetBlank", ":-moz-frameset-blank"),
    NonInheritingAnonBoxAtom("AnonBox_tableColGroup", ":-moz-table-column-group"),
    NonInheritingAnonBoxAtom("AnonBox_tableCol", ":-moz-table-column"),
    NonInheritingAnonBoxAtom("AnonBox_page", ":-moz-page"),
    NonInheritingAnonBoxAtom("AnonBox_pageBreak", ":-moz-page-break"),
    NonInheritingAnonBoxAtom("AnonBox_pageContent", ":-moz-page-content"),
    NonInheritingAnonBoxAtom("AnonBox_printedSheet", ":-moz-printed-sheet"),
    NonInheritingAnonBoxAtom("AnonBox_columnSpanWrapper", ":-moz-column-span-wrapper"),
    InheritingAnonBoxAtom("AnonBox_mozText", ":-moz-text"),
    InheritingAnonBoxAtom("AnonBox_firstLetterContinuation", ":-moz-first-letter-continuation"),
    InheritingAnonBoxAtom("AnonBox_mozBlockInsideInlineWrapper", ":-moz-block-inside-inline-wrapper"),
    InheritingAnonBoxAtom("AnonBox_mozMathMLAnonymousBlock", ":-moz-mathml-anonymous-block"),
    InheritingAnonBoxAtom("AnonBox_mozLineFrame", ":-moz-line-frame"),
    InheritingAnonBoxAtom("AnonBox_buttonContent", ":-moz-button-content"),
    InheritingAnonBoxAtom("AnonBox_cellContent", ":-moz-cell-content"),
    InheritingAnonBoxAtom("AnonBox_dropDownList", ":-moz-dropdown-list"),
    InheritingAnonBoxAtom("AnonBox_fieldsetContent", ":-moz-fieldset-content"),
    InheritingAnonBoxAtom("AnonBox_htmlCanvasContent", ":-moz-html-canvas-content"),
    InheritingAnonBoxAtom("AnonBox_inlineTable", ":-moz-inline-table"),
    InheritingAnonBoxAtom("AnonBox_table", ":-moz-table"),
    InheritingAnonBoxAtom("AnonBox_tableCell", ":-moz-table-cell"),
    InheritingAnonBoxAtom("AnonBox_tableWrapper", ":-moz-table-wrapper"),
    InheritingAnonBoxAtom("AnonBox_tableRowGroup", ":-moz-table-row-group"),
    InheritingAnonBoxAtom("AnonBox_tableRow", ":-moz-table-row"),
    InheritingAnonBoxAtom("AnonBox_canvas", ":-moz-canvas"),
    InheritingAnonBoxAtom("AnonBox_pageSequence", ":-moz-page-sequence"),
    InheritingAnonBoxAtom("AnonBox_scrolledContent", ":-moz-scrolled-content"),
    InheritingAnonBoxAtom("AnonBox_scrolledCanvas", ":-moz-scrolled-canvas"),
    InheritingAnonBoxAtom("AnonBox_columnSet", ":-moz-column-set"),
    InheritingAnonBoxAtom("AnonBox_columnContent", ":-moz-column-content"),
    InheritingAnonBoxAtom("AnonBox_viewport", ":-moz-viewport"),
    InheritingAnonBoxAtom("AnonBox_viewportScroll", ":-moz-viewport-scroll"),
    InheritingAnonBoxAtom("AnonBox_anonymousItem", ":-moz-anonymous-item"),
    InheritingAnonBoxAtom("AnonBox_blockRubyContent", ":-moz-block-ruby-content"),
    InheritingAnonBoxAtom("AnonBox_ruby", ":-moz-ruby"),
    InheritingAnonBoxAtom("AnonBox_rubyBase", ":-moz-ruby-base"),
    InheritingAnonBoxAtom("AnonBox_rubyBaseContainer", ":-moz-ruby-base-container"),
    InheritingAnonBoxAtom("AnonBox_rubyText", ":-moz-ruby-text"),
    InheritingAnonBoxAtom("AnonBox_rubyTextContainer", ":-moz-ruby-text-container"),
    InheritingAnonBoxAtom("AnonBox_mozTreeColumn", ":-moz-tree-column"),
    InheritingAnonBoxAtom("AnonBox_mozTreeRow", ":-moz-tree-row"),
    InheritingAnonBoxAtom("AnonBox_mozTreeSeparator", ":-moz-tree-separator"),
    InheritingAnonBoxAtom("AnonBox_mozTreeCell", ":-moz-tree-cell"),
    InheritingAnonBoxAtom("AnonBox_mozTreeIndentation", ":-moz-tree-indentation"),
    InheritingAnonBoxAtom("AnonBox_mozTreeLine", ":-moz-tree-line"),
    InheritingAnonBoxAtom("AnonBox_mozTreeTwisty", ":-moz-tree-twisty"),
    InheritingAnonBoxAtom("AnonBox_mozTreeImage", ":-moz-tree-image"),
    InheritingAnonBoxAtom("AnonBox_mozTreeCellText", ":-moz-tree-cell-text"),
    InheritingAnonBoxAtom("AnonBox_mozTreeCheckbox", ":-moz-tree-checkbox"),
    InheritingAnonBoxAtom("AnonBox_mozTreeDropFeedback", ":-moz-tree-drop-feedback"),
    InheritingAnonBoxAtom("AnonBox_mozSVGMarkerAnonChild", ":-moz-svg-marker-anon-child"),
    InheritingAnonBoxAtom("AnonBox_mozSVGOuterSVGAnonChild", ":-moz-svg-outer-svg-anon-child"),
    InheritingAnonBoxAtom("AnonBox_mozSVGForeignContent", ":-moz-svg-foreign-content"),
    InheritingAnonBoxAtom("AnonBox_mozSVGText", ":-moz-svg-text"),
    # END ATOMS
] + HTML_PARSER_ATOMS
# fmt: on


def verify():
    idents = set()
    strings = set()
    failed = False
    for atom in STATIC_ATOMS:
        if atom.ident in idents:
            print("StaticAtoms.py: duplicate static atom ident: %s" % atom.ident)
            failed = True
        if atom.string in strings:
            print('StaticAtoms.py: duplicate static atom string: "%s"' % atom.string)
            failed = True
        idents.add(atom.ident)
        strings.add(atom.string)
    if failed:
        sys.exit(1)


def generate_nsgkatomlist_h(output, *ignore):
    verify()
    output.write(
        "/* THIS FILE IS AUTOGENERATED BY StaticAtoms.py.  DO NOT EDIT */\n\n"
        "#ifdef small\n"
        "#undef small\n"
        "#endif\n\n"
        "// GK_ATOM(identifier, string, hash, is_ascii_lower, gecko_type, atom_type)\n"
        + "".join(
            [
                'GK_ATOM(%s, "%s", 0x%08x, %s, %s, %s)\n'
                % (
                    a.ident,
                    a.string,
                    a.hash,
                    str(a.is_ascii_lowercase).lower(),
                    a.ty,
                    a.atom_type,
                )
                for a in STATIC_ATOMS
            ]
        )
    )


def generate_nsgkatomconsts_h(output, *ignore):
    pseudo_index = None
    anon_box_index = None
    pseudo_count = 0
    anon_box_count = 0
    for i, atom in enumerate(STATIC_ATOMS):
        if atom.atom_type == "PseudoElementAtom":
            if pseudo_index is None:
                pseudo_index = i
            pseudo_count += 1
        elif (
            atom.atom_type == "NonInheritingAnonBoxAtom"
            or atom.atom_type == "InheritingAnonBoxAtom"
        ):
            if anon_box_index is None:
                anon_box_index = i
            anon_box_count += 1
    output.write(
        "/* THIS IS AN AUTOGENERATED FILE.  DO NOT EDIT */\n\n"
        "#ifndef nsGkAtomConsts_h\n"
        "#define nsGkAtomConsts_h\n\n"
        "namespace mozilla {\n"
        "  constexpr uint32_t kAtomIndex_PseudoElements = %d;\n"
        "  constexpr uint32_t kAtomCount_PseudoElements = %d;\n"
        "  constexpr uint32_t kAtomIndex_AnonBoxes = %d;\n"
        "  constexpr uint32_t kAtomCount_AnonBoxes = %d;\n"
        "}\n\n"
        "#endif\n" % (pseudo_index, pseudo_count, anon_box_index, anon_box_count)
    )


if __name__ == "__main__":
    generate_nsgkatomlist_h(sys.stdout)
