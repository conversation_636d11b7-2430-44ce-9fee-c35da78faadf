# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
name = "smart-default"
version = "0.7.1"
authors = ["IdanArye <<EMAIL>>"]
description = "Rust custom-derive macro for Default with more control on the fields"
documentation = "https://idanarye.github.io/rust-smart-default/"
readme = "README.md"
keywords = ["default"]
license = "MIT"
repository = "https://github.com/idanarye/rust-smart-default"

[lib]
proc-macro = true

[dependencies.proc-macro2]
version = "1"

[dependencies.quote]
version = "1"

[dependencies.syn]
version = "2"
