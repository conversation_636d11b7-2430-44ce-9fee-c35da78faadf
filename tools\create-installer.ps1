# Megisto Browser - Installer Creation Script
# This script creates a Windows installer for Megisto Browser

param(
    [string]$Version = "1.0.0",
    [string]$Architecture = "x64",
    [switch]$Sign,
    [string]$SigningCert = "",
    [switch]$Verbose
)

$ErrorActionPreference = "Stop"

Write-Host "=== Megisto Browser Installer Creation ===" -ForegroundColor Green

$rootDir = Get-Location
$firefoxDir = Join-Path $rootDir "firefox-source"
$objDir = Join-Path $firefoxDir "obj-megisto"
$distDir = Join-Path $rootDir "dist"
$installerDir = Join-Path $distDir "installer"
$buildDir = Join-Path $objDir "dist"

# Check if build exists
if (-not (Test-Path $buildDir)) {
    Write-Error "Megisto Browser build not found at $buildDir. Please run .\tools\build.ps1 first."
    exit 1
}

# Create installer directory
if (-not (Test-Path $installerDir)) {
    New-Item -ItemType Directory -Path $installerDir -Force | Out-Null
}

Write-Host "Creating installer for Megisto Browser v$Version ($Architecture)" -ForegroundColor Yellow

# Check for NSIS (Nullsoft Scriptable Install System)
$nsisPath = ""
$possibleNsisPaths = @(
    "${env:ProgramFiles}\NSIS\makensis.exe",
    "${env:ProgramFiles(x86)}\NSIS\makensis.exe",
    "C:\Program Files\NSIS\makensis.exe",
    "C:\Program Files (x86)\NSIS\makensis.exe"
)

foreach ($path in $possibleNsisPaths) {
    if (Test-Path $path) {
        $nsisPath = $path
        break
    }
}

if (-not $nsisPath) {
    Write-Warning "NSIS not found. Installing NSIS..."
    
    # Try to install NSIS using Chocolatey
    if (Get-Command "choco" -ErrorAction SilentlyContinue) {
        choco install nsis -y
        
        # Check again after installation
        foreach ($path in $possibleNsisPaths) {
            if (Test-Path $path) {
                $nsisPath = $path
                break
            }
        }
    }
    
    if (-not $nsisPath) {
        Write-Error "NSIS is required to create the installer. Please install NSIS from https://nsis.sourceforge.io/"
        exit 1
    }
}

Write-Host "Using NSIS at: $nsisPath" -ForegroundColor Green

# Create NSIS installer script
$nsisScript = @"
; Megisto Browser Installer Script
; Generated by create-installer.ps1

!define PRODUCT_NAME "Megisto Browser"
!define PRODUCT_VERSION "$Version"
!define PRODUCT_PUBLISHER "Megisto Browser Team"
!define PRODUCT_WEB_SITE "https://github.com/megisto/megisto-browser"
!define PRODUCT_DIR_REGKEY "Software\Microsoft\Windows\CurrentVersion\App Paths\megisto.exe"
!define PRODUCT_UNINST_KEY "Software\Microsoft\Windows\CurrentVersion\Uninstall\`${PRODUCT_NAME}"
!define PRODUCT_UNINST_ROOT_KEY "HKLM"

; Modern UI
!include "MUI2.nsh"

; General
Name "`${PRODUCT_NAME} `${PRODUCT_VERSION}"
OutFile "$installerDir\MegistoBrowser-`${PRODUCT_VERSION}-$Architecture-Setup.exe"
InstallDir "`$PROGRAMFILES64\Megisto Browser"
InstallDirRegKey HKLM "`${PRODUCT_DIR_REGKEY}" ""
ShowInstDetails show
ShowUnInstDetails show

; Interface Settings
!define MUI_ABORTWARNING
!define MUI_ICON "`${NSISDIR}\Contrib\Graphics\Icons\modern-install.ico"
!define MUI_UNICON "`${NSISDIR}\Contrib\Graphics\Icons\modern-uninstall.ico"

; Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "$rootDir\LICENSE"
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_INSTFILES

; Languages
!insertmacro MUI_LANGUAGE "English"

; Sections
Section "Megisto Browser (required)" SEC01
  SectionIn RO
  SetOutPath "`$INSTDIR"
  SetOverwrite ifnewer
  
  ; Copy all files from build directory
  File /r "$buildDir\*.*"
  
  ; Create shortcuts
  CreateDirectory "`$SMPROGRAMS\Megisto Browser"
  CreateShortCut "`$SMPROGRAMS\Megisto Browser\Megisto Browser.lnk" "`$INSTDIR\megisto.exe"
  CreateShortCut "`$DESKTOP\Megisto Browser.lnk" "`$INSTDIR\megisto.exe"
  
  ; Register application
  WriteRegStr HKLM "`${PRODUCT_DIR_REGKEY}" "" "`$INSTDIR\megisto.exe"
  WriteRegStr HKLM "`${PRODUCT_UNINST_KEY}" "DisplayName" "`$(^Name)"
  WriteRegStr HKLM "`${PRODUCT_UNINST_KEY}" "UninstallString" "`$INSTDIR\uninst.exe"
  WriteRegStr HKLM "`${PRODUCT_UNINST_KEY}" "DisplayIcon" "`$INSTDIR\megisto.exe"
  WriteRegStr HKLM "`${PRODUCT_UNINST_KEY}" "DisplayVersion" "`${PRODUCT_VERSION}"
  WriteRegStr HKLM "`${PRODUCT_UNINST_KEY}" "URLInfoAbout" "`${PRODUCT_WEB_SITE}"
  WriteRegStr HKLM "`${PRODUCT_UNINST_KEY}" "Publisher" "`${PRODUCT_PUBLISHER}"
SectionEnd

Section "Desktop Shortcut" SEC02
  CreateShortCut "`$DESKTOP\Megisto Browser.lnk" "`$INSTDIR\megisto.exe"
SectionEnd

Section "Quick Launch Shortcut" SEC03
  CreateShortCut "`$QUICKLAUNCH\Megisto Browser.lnk" "`$INSTDIR\megisto.exe"
SectionEnd

; Section descriptions
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT `${SEC01} "Core Megisto Browser files (required)"
  !insertmacro MUI_DESCRIPTION_TEXT `${SEC02} "Create a desktop shortcut for Megisto Browser"
  !insertmacro MUI_DESCRIPTION_TEXT `${SEC03} "Create a quick launch shortcut for Megisto Browser"
!insertmacro MUI_FUNCTION_DESCRIPTION_END

Section -AdditionalIcons
  WriteIniStr "`$INSTDIR\`${PRODUCT_NAME}.url" "InternetShortcut" "URL" "`${PRODUCT_WEB_SITE}"
  CreateShortCut "`$SMPROGRAMS\Megisto Browser\Website.lnk" "`$INSTDIR\`${PRODUCT_NAME}.url"
  CreateShortCut "`$SMPROGRAMS\Megisto Browser\Uninstall.lnk" "`$INSTDIR\uninst.exe"
SectionEnd

Section -Post
  WriteUninstaller "`$INSTDIR\uninst.exe"
SectionEnd

Function un.onUninstSuccess
  HideWindow
  MessageBox MB_ICONINFORMATION|MB_OK "Megisto Browser was successfully removed from your computer."
FunctionEnd

Function un.onInit
  MessageBox MB_ICONQUESTION|MB_YESNO|MB_DEFBUTTON2 "Are you sure you want to completely remove Megisto Browser and all of its components?" IDYES +2
  Abort
FunctionEnd

Section Uninstall
  Delete "`$INSTDIR\`${PRODUCT_NAME}.url"
  Delete "`$INSTDIR\uninst.exe"
  
  ; Remove all installed files
  RMDir /r "`$INSTDIR"
  
  ; Remove shortcuts
  Delete "`$SMPROGRAMS\Megisto Browser\Uninstall.lnk"
  Delete "`$SMPROGRAMS\Megisto Browser\Website.lnk"
  Delete "`$SMPROGRAMS\Megisto Browser\Megisto Browser.lnk"
  Delete "`$DESKTOP\Megisto Browser.lnk"
  Delete "`$QUICKLAUNCH\Megisto Browser.lnk"
  
  RMDir "`$SMPROGRAMS\Megisto Browser"
  
  ; Remove registry keys
  DeleteRegKey `${PRODUCT_UNINST_ROOT_KEY} "`${PRODUCT_UNINST_KEY}"
  DeleteRegKey HKLM "`${PRODUCT_DIR_REGKEY}"
  
  SetAutoClose true
SectionEnd
"@

$nsisScriptPath = Join-Path $installerDir "megisto-installer.nsi"
$nsisScript | Out-File -FilePath $nsisScriptPath -Encoding UTF8

Write-Host "NSIS script created at: $nsisScriptPath" -ForegroundColor Green

# Create a basic LICENSE file if it doesn't exist
$licensePath = Join-Path $rootDir "LICENSE"
if (-not (Test-Path $licensePath)) {
    $licenseContent = @"
Mozilla Public License Version 2.0
==================================

This Source Code Form is subject to the terms of the Mozilla Public
License, v. 2.0. If a copy of the MPL was not distributed with this
file, You can obtain one at http://mozilla.org/MPL/2.0/.

Megisto Browser is based on Mozilla Firefox and is distributed under
the same license terms.
"@
    $licenseContent | Out-File -FilePath $licensePath -Encoding UTF8
    Write-Host "Created LICENSE file" -ForegroundColor Green
}

# Build the installer
Write-Host "Building installer..." -ForegroundColor Yellow

try {
    $nsisArgs = @(
        "/V2",  # Verbose level 2
        $nsisScriptPath
    )
    
    if ($Verbose) {
        $nsisArgs += "/V4"  # Maximum verbosity
    }
    
    $process = Start-Process -FilePath $nsisPath -ArgumentList $nsisArgs -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-Host "Installer created successfully!" -ForegroundColor Green
        
        # Find the created installer
        $installerPattern = "MegistoBrowser-$Version-$Architecture-Setup.exe"
        $installerPath = Join-Path $installerDir $installerPattern
        
        if (Test-Path $installerPath) {
            $installerSize = (Get-Item $installerPath).Length / 1MB
            Write-Host "Installer: $installerPath" -ForegroundColor Cyan
            Write-Host "Size: $([math]::Round($installerSize, 2)) MB" -ForegroundColor Cyan
            
            # Sign the installer if requested
            if ($Sign -and $SigningCert) {
                Write-Host "Signing installer..." -ForegroundColor Yellow
                
                $signToolPath = "${env:ProgramFiles(x86)}\Windows Kits\10\bin\x64\signtool.exe"
                if (-not (Test-Path $signToolPath)) {
                    $signToolPath = "${env:ProgramFiles}\Windows Kits\10\bin\x64\signtool.exe"
                }
                
                if (Test-Path $signToolPath) {
                    $signArgs = @(
                        "sign",
                        "/f", $SigningCert,
                        "/t", "http://timestamp.digicert.com",
                        "/v",
                        $installerPath
                    )
                    
                    $signProcess = Start-Process -FilePath $signToolPath -ArgumentList $signArgs -Wait -PassThru -NoNewWindow
                    
                    if ($signProcess.ExitCode -eq 0) {
                        Write-Host "Installer signed successfully!" -ForegroundColor Green
                    } else {
                        Write-Warning "Failed to sign installer (exit code: $($signProcess.ExitCode))"
                    }
                } else {
                    Write-Warning "SignTool not found. Please install Windows SDK to sign the installer."
                }
            }
            
        } else {
            Write-Warning "Installer file not found at expected location: $installerPath"
        }
        
    } else {
        Write-Error "NSIS compilation failed (exit code: $($process.ExitCode))"
        exit 1
    }
    
} catch {
    Write-Error "Failed to create installer: $_"
    exit 1
}

# Clean up
Remove-Item $nsisScriptPath -Force -ErrorAction SilentlyContinue

Write-Host "`n=== Installer Creation Complete ===" -ForegroundColor Green
Write-Host "Installer package created successfully!" -ForegroundColor Green
Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Test the installer on a clean system" -ForegroundColor White
Write-Host "2. Run automated tests: .\tools\run-tests.ps1" -ForegroundColor White
Write-Host "3. Distribute the installer package" -ForegroundColor White
