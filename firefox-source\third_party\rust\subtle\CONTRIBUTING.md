# Contributing to subtle

If you have questions or comments, please feel free to email the
authors. 

For feature requests, suggestions, and bug reports, please open an
issue on [our Github](https://github.com/dalek-cryptography/subtle).  (Or,
send us an email if you're opposed to using Github for whatever reason.)

Patches are welcomed as pull requests on
[our Github](https://github.com/dalek-cryptography/subtle), as well as by
email (preferably sent to all of the authors listed in `Cargo.toml`).

We're happy to take generalised utility code, provided the code is:

1. constant time for all potential valid invocations, and
2. applicable to implementations of several different protocols/primitives.

All issues on subtle are mentored, if you want help with a bug just ask
@isislovecruft or @hdevalence.

Some issues are easier than others. The `easy` label can be used to find the
easy issues. If you want to work on an issue, please leave a comment so that we
can assign it to you!

# Code of Conduct

We follow the [Rust Code of Conduct](http://www.rust-lang.org/conduct.html),
with the following additional clauses:

* We respect the rights to privacy and anonymity for contributors and people in
  the community.  If someone wishes to contribute under a pseudonym different to
  their primary identity, that wish is to be respected by all contributors.
