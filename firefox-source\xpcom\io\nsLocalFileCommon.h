/* -*- Mode: C++; tab-width: 8; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* vim: set ts=8 sts=2 et sw=2 tw=80: */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#ifndef _NS_LOCAL_FILE_COMMON_H_
#define _NS_LOCAL_FILE_COMMON_H_

#ifdef MOZ_ESR
extern const char* const sExecutableExts[110];
#else
extern const char* const sExecutableExts[111];
#endif

#endif
