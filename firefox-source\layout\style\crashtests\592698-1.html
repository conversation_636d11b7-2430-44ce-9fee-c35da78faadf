<!DOCTYPE html>
<html class="reftest-wait">
  <iframe id="x"
          srcdoc="<div id='a'>aaa"></iframe>

  <script>
    window.onload = function() {
      window.frames[0].document.getElementById("a").setAttribute("style",
        'transition-property: color;' +
        'transition-duration: 10s;' +
        'transition-property: color;' +
        'transition-duration: 10s; ' +
        'color: red;');

      // And start the transition
      window.frames[0].document.documentElement.getBoundingClientRect();

      // Now kill off the presshell
      var frame = document.getElementById("x");
      frame.style.display = "none";
      document.documentElement.getBoundingClientRect();

      // And wait for the refresh driver to fire
      setTimeout(function() {
          document.documentElement.className = "";
      }, 100);
    }
  </script>
</html>
