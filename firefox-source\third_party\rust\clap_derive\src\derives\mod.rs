// Copyright 2018 <PERSON> (@TeXitoi) <<EMAIL>>,
// <PERSON> (@kbknapp) <<EMAIL>>, and
// <PERSON> (@hoverbear) <<EMAIL>>
//
// Licensed under the Apache License, Version 2.0 <LICENSE-APACHE or
// http://www.apache.org/licenses/LICENSE-2.0> or the MIT license
// <LICENSE-MIT or http://opensource.org/licenses/MIT>, at your
// option. This file may not be copied, modified, or distributed
// except according to those terms.
//
// This work was derived from Structopt (https://github.com/TeXitoi/structopt)
// commit#ea76fa1b1b273e65e3b0b1046643715b49bec51f which is licensed under the
// MIT/Apache 2.0 license.
mod args;
mod into_app;
mod parser;
mod subcommand;
mod value_enum;

pub(crate) use self::parser::derive_parser;
pub(crate) use args::derive_args;
pub(crate) use subcommand::derive_subcommand;
pub(crate) use value_enum::derive_value_enum;
