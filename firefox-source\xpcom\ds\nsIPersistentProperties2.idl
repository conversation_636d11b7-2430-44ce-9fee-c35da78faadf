/* -*- Mode: C++; tab-width: 2; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#include "nsIProperties.idl"

interface nsIInputStream;
interface nsIOutputStream;
interface nsISimpleEnumerator;

%{C++
#include "mozilla/MemoryReporting.h"
%}

native MallocSizeOf(mozilla::MallocSizeOf);

[scriptable, uuid(283EE646-1AEF-11D4-98B3-00C04fA0CE9A)]
interface nsIPropertyElement : nsISupports {
  attribute AUTF8String key;
  attribute AString value;
};

[scriptable, builtinclass, uuid(706867af-0400-4faa-beb1-0dae87308784)]
interface nsIPersistentProperties : nsIProperties
{
  /**
   * load a set of name/value pairs from the input stream
   * names and values should be in UTF8
   */
  void load(in nsIInputStream input);

  /**
   * output the values to the stream - results will be in UTF8
   */
  void save(in nsIOutputStream output, in AUTF8String header);

  /**
   * get an enumeration of nsIPropertyElement objects,
   * which are read-only (i.e. setting properties on the element will
   * not make changes back into the source nsIPersistentProperties
   */
  nsISimpleEnumerator enumerate();

  /**
   * shortcut to nsIProperty's get() which retrieves a string value
   * directly (and thus faster)
   */
  AString getStringProperty(in AUTF8String key);

  /**
   * shortcut to nsIProperty's set() which sets a string value
   * directly (and thus faster). If the given property already exists,
   * then the old value will be returned
   */
  AString setStringProperty(in AUTF8String key, in AString value);

  [notxpcom, nostdcall] size_t sizeOfIncludingThis(in MallocSizeOf aMallocSizeOf);
};
