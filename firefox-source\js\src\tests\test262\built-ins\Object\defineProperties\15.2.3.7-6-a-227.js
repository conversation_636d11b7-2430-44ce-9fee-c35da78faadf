// Copyright (c) 2012 Ecma International.  All rights reserved.
// This code is governed by the BSD license found in the LICENSE file.

/*---
es5id: ********-6-a-227
description: >
    Object.defineProperties - 'O' is an Array, 'P' is an array index
    property, TypeError is thrown if the [[Configurable]] attribute
    value of 'P' is false  and the [[Configurable]] field of 'desc' is
    true  (******** step 4.c)
includes: [propertyHelper.js]
---*/


var arr = [];

Object.defineProperty(arr, "1", {
  value: 3,
  configurable: false
});

try {
  Object.defineProperties(arr, {
    "1": {
      value: 13,
      configurable: true
    }
  });
} catch (e) {
  if (!(e instanceof TypeError)) {
    throw new Test262Error("Expected TypeError, got " + e);
  }
}

verifyProperty(arr, "1", {
  value: 3,
  writable: false,
  enumerable: false,
  configurable: false,
});

reportCompare(0, 0);
