// Copyright (c) 2012 Ecma International.  All rights reserved.
// This code is governed by the BSD license found in the LICENSE file.

/*---
es5id: ********-6-a-214
description: >
    Object.defineProperties - 'O' is an Array, 'name' is an array
    index property, the [[Value]] field of 'desc' is -0, and the
    [[Value]] attribute value of 'name' is +0  (******** step 4.c)
includes: [propertyHelper.js]
---*/

var arr = [];

Object.defineProperty(arr, "0", {
  value: +0
});

try {
  Object.defineProperties(arr, {
    "0": {
      value: -0
    }
  });
  throw new Test262Error("Expected an exception.");
} catch (e) {
  if (!(e instanceof TypeError)) {
    throw new Test262Error("Expected TypeError, got " + e);
  }
}

verifyProperty(arr, "0", {
  value: +0,
  writable: false,
  enumerable: false,
  configurable: false,
});

reportCompare(0, 0);
