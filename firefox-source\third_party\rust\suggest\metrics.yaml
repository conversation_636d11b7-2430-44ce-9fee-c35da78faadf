# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

# This file defines the metrics that will be gathered for the Suggest
# component.  Changes to these metrics require data review.
#
# We can't record metrics from Rust directly.  To work around that we:
#   - Define the metrics in application-services
#   - Define API calls in application-services that return the metrics
#     alongside the normal results.
#   - Record the metrics with Glean in the consumer code

---
$schema: moz://mozilla.org/schemas/glean/metrics/2-0-0

suggest:
  ingest_time:
    type: labeled_timing_distribution
    description: Time for ingestion (excluding download time), labelled by record type
    time_unit: microsecond
    labels:
      - icon
      - data
      - amo-suggestions
      - yelp-suggestions
      - mdn-suggestions
      - weather
      - configuration
      - amp-mobile-suggestions
      - fakespot-suggestions
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1908397
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1911664
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: "never"
    data_sensitivity:
      - technical

  ingest_download_time:
    type: labeled_timing_distribution
    description: Download time for ingestion, labelled by record type
    time_unit: microsecond
    labels:
      - icon
      - data
      - amo-suggestions
      - yelp-suggestions
      - mdn-suggestions
      - weather
      - configuration
      - amp-mobile-suggestions
      - fakespot-suggestions
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1908397
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1911664
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: "never"
    data_sensitivity:
      - technical

  query_time:
    type: labeled_timing_distribution
    description: Time executing queries, labelled by provider type
    time_unit: microsecond
    labels:
      - amp
      - ampmobile
      - wikipedia
      - amo
      - yelp
      - mdn
      - weather
      - fakespot
    bugs:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1908397
    data_reviews:
      - https://bugzilla.mozilla.org/show_bug.cgi?id=1911664
    notification_emails:
      - <EMAIL>
      - <EMAIL>
    expires: "never"
    data_sensitivity:
      - technical
