[tox]
envlist =
    py3{13,12,11,10,9}
    pypy310
    style
    typing
    docs
skip_missing_interpreters = true

[testenv]
package = wheel
pass_env = PYTHON_GIL
constrain_package_deps = true
use_frozen_constraints = true
deps = -r requirements/tests.txt
commands = pytest -v --tb=short --basetemp={envtmpdir} {posargs}

[testenv:style]
deps = pre-commit
skip_install = true
commands = pre-commit run --all-files

[testenv:typing]
deps = -r requirements/typing.txt
commands = mypy

[testenv:docs]
deps = -r requirements/docs.txt
commands = sphinx-build -E -W -b dirhtml docs docs/_build/dirhtml

[testenv:update-actions]
labels = update
deps = gha-update
commands = gha-update

[testenv:update-pre_commit]
labels = update
deps = pre-commit
skip_install = true
commands = pre-commit autoupdate -j4

[testenv:update-requirements]
labels = update
deps = pip-tools
skip_install = true
change_dir = requirements
commands =
    pip-compile build.in -q {posargs:-U}
    pip-compile docs.in -q {posargs:-U}
    pip-compile tests.in -q {posargs:-U}
    pip-compile typing.in -q {posargs:-U}
    pip-compile dev.in -q {posargs:-U}
