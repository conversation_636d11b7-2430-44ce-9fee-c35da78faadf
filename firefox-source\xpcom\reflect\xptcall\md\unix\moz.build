# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

if CONFIG["OS_ARCH"] == "Darwin":
    SOURCES += [
        "xptcinvoke_darwin.cpp",
        "xptcstubs_darwin.cpp",
    ]
    if CONFIG["TARGET_CPU"] == "ppc":
        SOURCES += [
            "!xptcstubs_asm_ppc_darwin.s",
            "xptcinvoke_asm_ppc_rhapsody.s",
        ]
    if CONFIG["TARGET_CPU"] == "x86_64":
        SOURCES += [
            "xptcinvoke_asm_x86_64_unix.S",
        ]
    if CONFIG["TARGET_CPU"] == "aarch64":
        SOURCES += [
            "xptcinvoke_asm_aarch64.S",
            "xptcstubs_asm_aarch64.S",
        ]

if CONFIG["OS_ARCH"] == "GNU":
    if <PERSON>NFIG["TARGET_CPU"] == "x86":
        SOURCES += ["xptcinvoke_gcc_x86_unix.cpp", "xptcstubs_gcc_x86_unix.cpp"]

if CONFIG["OS_ARCH"] in (
    "Linux",
    "Bitrig",
    "DragonFly",
    "FreeBSD",
    "NetBSD",
    "OpenBSD",
    "SunOS",
) or CONFIG["OS_ARCH"].startswith("GNU_"):
    if CONFIG["TARGET_CPU"] == "x86_64":
        SOURCES += [
            "xptcinvoke_asm_x86_64_unix.S",
            "xptcinvoke_x86_64_unix.cpp",
            "xptcstubs_x86_64_linux.cpp",
        ]
    elif CONFIG["TARGET_CPU"] == "x86":
        SOURCES += ["xptcinvoke_gcc_x86_unix.cpp", "xptcstubs_gcc_x86_unix.cpp"]

if CONFIG["OS_ARCH"] in ("Linux", "FreeBSD"):
    if CONFIG["TARGET_CPU"] == "ia64":
        SOURCES += [
            "xptcinvoke_asm_ipf64.s",
            "xptcinvoke_ipf64.cpp",
            "xptcstubs_asm_ipf64.s",
            "xptcstubs_ipf64.cpp",
        ]

if CONFIG["TARGET_CPU"] == "Alpha":
    if CONFIG["OS_ARCH"] in ("Linux", "FreeBSD", "NetBSD"):
        SOURCES += [
            "xptcinvoke_linux_alpha.cpp",
            "xptcstubs_linux_alpha.cpp",
        ]
    elif CONFIG["OS_ARCH"] == "OpenBSD":
        SOURCES += [
            "xptcinvoke_alpha_openbsd.cpp",
            "xptcstubs_alpha_openbsd.cpp",
        ]

if CONFIG["TARGET_CPU"] == "arm":
    if CONFIG["OS_ARCH"] == "Linux":
        SOURCES += ["xptcinvoke_arm.cpp", "xptcstubs_arm.cpp"]
        CXXFLAGS += ["-O2"]
    elif CONFIG["OS_ARCH"] == "NetBSD":
        SOURCES += [
            "xptcinvoke_arm_netbsd.cpp",
            "xptcstubs_arm_netbsd.cpp",
        ]

if CONFIG["TARGET_CPU"] == "arm" and CONFIG["OS_ARCH"] in ("Bitrig", "OpenBSD"):
    SOURCES += [
        "xptcinvoke_arm_openbsd.cpp",
        "xptcstubs_arm_openbsd.cpp",
    ]

if CONFIG["OS_ARCH"] == "Linux":
    if CONFIG["TARGET_CPU"] == "hppa":
        if CONFIG["CC_TYPE"] in ("clang", "gcc"):
            SOURCES += [
                "xptcinvoke_asm_parisc_linux.s",
                "xptcinvoke_pa32.cpp",
                "xptcstubs_asm_parisc_linux.s",
                "xptcstubs_pa32.cpp",
            ]
        elif CONFIG["COMPILE_ENVIRONMENT"]:
            error("Unknown C++ compiler, xptcall assembly will probably be incorrect.")

if CONFIG["OS_ARCH"] in ("Linux", "FreeBSD", "NetBSD", "OpenBSD"):
    if CONFIG["TARGET_CPU"] == "aarch64":
        SOURCES += [
            "xptcinvoke_aarch64.cpp",
            "xptcinvoke_asm_aarch64.S",
            "xptcstubs_aarch64.cpp",
            "xptcstubs_asm_aarch64.S",
        ]
    if CONFIG["TARGET_CPU"] == "mips64":
        SOURCES += [
            "xptcinvoke_asm_mips64.S",
            "xptcinvoke_mips64.cpp",
            "xptcstubs_asm_mips64.S",
            "xptcstubs_mips64.cpp",
        ]
        if CONFIG["CC_TYPE"] == "clang":
            ASFLAGS += [
                "-fno-integrated-as",
            ]
    if CONFIG["TARGET_CPU"] == "mips32":
        SOURCES += [
            "xptcinvoke_asm_mips.S",
            "xptcinvoke_mips.cpp",
            "xptcstubs_asm_mips.S",
            "xptcstubs_mips.cpp",
        ]
        if CONFIG["CC_TYPE"] == "clang":
            ASFLAGS += [
                "-fno-integrated-as",
            ]

if CONFIG["TARGET_CPU"] == "ppc":
    if CONFIG["OS_ARCH"] in ("Linux", "FreeBSD"):
        SOURCES += [
            "xptcinvoke_asm_ppc_linux.S",
            "xptcinvoke_ppc_linux.cpp",
            "xptcstubs_asm_ppc_linux.S",
            "xptcstubs_ppc_linux.cpp",
        ]

if CONFIG["TARGET_CPU"] == "ppc64":
    if CONFIG["OS_ARCH"] in ("Linux", "FreeBSD"):
        SOURCES += [
            "xptcinvoke_asm_ppc64_linux.S",
            "xptcinvoke_ppc64_linux.cpp",
            "xptcstubs_asm_ppc64_linux.S",
            "xptcstubs_ppc64_linux.cpp",
        ]
        if CONFIG["CC_TYPE"] == "clang":
            ASFLAGS += [
                "-fno-integrated-as",
            ]

if CONFIG["OS_ARCH"] == "OpenBSD" and CONFIG["TARGET_CPU"] == "ppc":
    SOURCES += [
        "xptcinvoke_asm_ppc_openbsd.S",
        "xptcinvoke_ppc_openbsd.cpp",
        "xptcstubs_asm_ppc_openbsd.S",
        "xptcstubs_ppc_openbsd.cpp",
    ]

if CONFIG["OS_ARCH"] == "Linux" and CONFIG["TARGET_CPU"] == "sparc":
    SOURCES += [
        "xptcinvoke_asm_sparc_linux_GCC3.s",
        "xptcinvoke_sparc_solaris.cpp",
        "xptcstubs_asm_sparc_solaris.s",
        "xptcstubs_sparc_solaris.cpp",
    ]

if CONFIG["OS_ARCH"] == "NetBSD" and CONFIG["TARGET_CPU"] == "sparc":
    SOURCES += [
        "xptcinvoke_asm_sparc_netbsd.s",
        "xptcinvoke_sparc_netbsd.cpp",
        "xptcstubs_asm_sparc_netbsd.s",
        "xptcstubs_sparc_netbsd.cpp",
    ]

if CONFIG["OS_ARCH"] == "OpenBSD" and CONFIG["TARGET_CPU"] == "sparc":
    SOURCES += [
        "xptcinvoke_asm_sparc_openbsd.s",
        "xptcinvoke_sparc_openbsd.cpp",
        "xptcstubs_asm_sparc_openbsd.s",
        "xptcstubs_sparc_openbsd.cpp",
    ]

if (
    CONFIG["OS_ARCH"] in ("OpenBSD", "FreeBSD", "Linux", "SunOS")
    and CONFIG["TARGET_CPU"] == "sparc64"
):
    SOURCES += [
        "xptcinvoke_asm_sparc64_openbsd.s",
        "xptcinvoke_sparc64_openbsd.cpp",
        "xptcstubs_asm_sparc64_openbsd.s",
        "xptcstubs_sparc64_openbsd.cpp",
    ]

if CONFIG["OS_ARCH"] == "Linux":
    if CONFIG["TARGET_CPU"] == "s390":
        SOURCES += [
            "xptcinvoke_linux_s390.cpp",
            "xptcstubs_linux_s390.cpp",
        ]
        CXXFLAGS += [
            "-fno-strict-aliasing",
            "-fno-inline",
            "-fomit-frame-pointer",
            "-mbackchain",
        ]
    elif CONFIG["TARGET_CPU"] == "s390x":
        SOURCES += [
            "xptcinvoke_linux_s390x.cpp",
            "xptcstubs_linux_s390x.cpp",
        ]
        CXXFLAGS += [
            "-fno-strict-aliasing",
            "-fno-inline",
            "-fomit-frame-pointer",
            "-mbackchain",
        ]
        if CONFIG["CC_TYPE"] == "clang":
            CXXFLAGS += [
                "-fno-integrated-as",
            ]

if CONFIG["OS_ARCH"] in ("Linux", "OpenBSD") and CONFIG["TARGET_CPU"] == "riscv64":
    SOURCES += [
        "xptcinvoke_asm_riscv64.S",
        "xptcinvoke_riscv64.cpp",
        "xptcstubs_asm_riscv64.S",
        "xptcstubs_riscv64.cpp",
    ]

if CONFIG["OS_ARCH"] == "Linux" and CONFIG["TARGET_CPU"] == "loongarch64":
    SOURCES += [
        "xptcinvoke_asm_loongarch64.S",
        "xptcinvoke_loongarch64.cpp",
        "xptcstubs_asm_loongarch64.S",
        "xptcstubs_loongarch64.cpp",
    ]

FINAL_LIBRARY = "xul"

LOCAL_INCLUDES += [
    "../..",
]
