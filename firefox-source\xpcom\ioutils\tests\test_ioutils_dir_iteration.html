<!-- Any copyright is dedicated to the Public Domain.
- http://creativecommons.org/publicdomain/zero/1.0/ -->
<!DOCTYPE HTML>
<html>

<head>
  <meta charset="utf-8">
  <title>Test the IOUtils file I/O API</title>
  <script src="chrome://mochikit/content/tests/SimpleTest/SimpleTest.js"></script>
  <link rel="stylesheet" href="chrome://mochikit/content/tests/SimpleTest/test.css" />
  <script src="file_ioutils_test_fixtures.js"></script>
  <script>
    "use strict";

    const { Assert } = ChromeUtils.importESModule(
      "resource://testing-common/Assert.sys.mjs"
    );

    add_task(async function iterate_dir_failure() {
      let notExists = PathUtils.join(PathUtils.tempDir, 'does_not_exist_dir.tmp.d');

      await Assert.rejects(
        IOUtils.hasChildren(notExists),
        /NotFoundError: Could not check children of `.*': directory does not exist/,
        "IOUtils::getChildren rejects if the file does not exist"
      );
      await Assert.rejects(
        IOUtils.getChildren(notExists),
        /NotFoundError: Could not get children of `.*': directory does not exist/,
        "IOUtils::getChildren rejects if the file does not exist"
      );
      ok(!await fileExists(notExists), `Expected ${notExists} not to exist`);

      info('Try to get the children of a regular file');

      let tmpFileName = PathUtils.join(PathUtils.tempDir, 'iterator_file.tmp');
      await createFile(tmpFileName)
      await Assert.rejects(IOUtils.hasChildren(tmpFileName),
        /InvalidAccessError: Could not check children of `.*': file is not a directory/,
        "IOUtils::getChildren rejects if the file is not a dir"
      );
      await Assert.rejects(IOUtils.getChildren(tmpFileName),
        /InvalidAccessError: Could not get children of `.*': file is not a directory/,
        "IOUtils::getChildren rejects if the file is not a dir"
      );

      await cleanup(tmpFileName);
    });

    add_task(async function iterate_dir() {
      info('Try to get the children of a multi-level directory hierarchy');

      let root = PathUtils.join(PathUtils.tempDir, 'iterator.tmp.d');
      let child1 = PathUtils.join(root, 'child1.tmp');
      let child2 = PathUtils.join(root, 'child2.tmp');
      let grandchild = PathUtils.join(child1, 'grandchild.tmp');

      await createDir(grandchild); // Ancestors will be created.
      await createDir(child2);

      let hasChildren = await IOUtils.hasChildren(root);
      is(hasChildren, true, `Expected some entries below the path at ${root}`);

      let entries = await IOUtils.getChildren(root);

      is(entries.length, 2, `Expected 2 entries below the path at ${root}`);
      ok(!entries.includes(grandchild), "IOUtils::getChildren does not enter subdirectories");

      await cleanup(root);
    });

    add_task(async function iterate_empty_dir() {
      info('Try to get the children of an empty directory');

      let emptyDir = PathUtils.join(PathUtils.tempDir, 'iterator_empty_dir.tmp.d');
      await createDir(emptyDir);

      is(
        (await IOUtils.hasChildren(emptyDir)),
        false,
        "IOUtils::hasChildren returns false when called on an empty dir"
      );

      is(
        (await IOUtils.getChildren(emptyDir)).length,
        0,
        "IOUtils::getChildren return an empty array when called on an empty dir"
      );

      await cleanup(emptyDir);
    });

    add_task(async function iterate_ignore_missing_dir() {
      info("Try to get the children of a missing file with ignoreAbsent");

      const notExists = PathUtils.join(PathUtils.tempDir, "does_not_exist_dir.tmp.d");

      is(
        (await IOUtils.hasChildren(notExists, { ignoreAbsent: true })),
        false,
        "IOUtils::hasChildren returns false when called with ignoreAbsent on a missing file"
      );
      is(
        (await IOUtils.getChildren(notExists, { ignoreAbsent: true })).length,
        0,
        "IOUtils::getChildren returns an empty array when called with ignoreAbsent on a missing file"
      );
      ok(!await fileExists(notExists), `Expected ${notExists} not to exist`);
    });
  </script>
</head>

<body>
  <p id="display"></p>
  <div id="content" style="display: none"></div>
  <pre id="test"></pre>
</body>

</html>
