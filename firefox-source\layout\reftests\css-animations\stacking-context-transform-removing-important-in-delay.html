<!DOCTYPE html>
<html class="reftest-wait">
<title>
Removing !important rule during delay phase of animation creates
a stack context for correct style
</title>
<style>
span {
  height: 100px;
  width: 100px;
  position: fixed;
  background: green;
  top: 50px;
}
@keyframes Transform100px {
  from, to { transform: translate(100px) }
}
#test {
  width: 100px; height: 100px;
  background: blue;
  animation: Transform100px 100s 100s;
}
</style>
<span></span>
<div id="test"></div>
<script>
window.addEventListener("load", () => {
  var target = document.getElementById("test");
  target.style.setProperty("transform", "translateX(200px)", "important");

  requestAnimationFrame(() => {
    // Now the target transform style should be translateX(200px) because of
    // !important rule.

    // Apply transform:none without important directive.
    target.style.setProperty("transform", "none", "");
    requestAnimationFrame(() => {
      // The CSS animation is no longer overridden but it's still in delay
      // phase, so we should create a stacking context for transform:none style.
      document.documentElement.classList.remove("reftest-wait");
    });
  });
});
</script>
