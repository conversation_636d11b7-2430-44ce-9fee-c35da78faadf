/* -*- Mode: C++; tab-width: 8; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* vim: set ts=8 sts=2 et sw=2 tw=80: */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

CSS_COUNTER_DESC(system, System)
CSS_COUNTER_DESC(symbols, Symbols)
CSS_COUNTER_DESC(additive-symbols, AdditiveSymbols)
CSS_COUNTER_DESC(negative, Negative)
CSS_COUNTER_DESC(prefix, Prefix)
CSS_COUNTER_DESC(suffix, Suffix)
CSS_COUNTER_DESC(range, Range)
CSS_COUNTER_DESC(pad, Pad)
CSS_COUNTER_DESC(fallback, Fallback)
CSS_COUNTER_DESC(speak-as, SpeakAs)
