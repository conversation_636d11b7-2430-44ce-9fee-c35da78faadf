<!doctype html>
<style>
  :indeterminate { color: red; }
</style>
<div id="container">
  <fieldset>
    <label>
      <input name="layout" type="radio">Foo
    </label>
    <label>
      <input name="layout" type="radio">Bar
    </label>
    <label>
      <input name="layout" type="radio">Baz
    </label>
    <label>
      <input name="layout" type="radio">Buz
    </label>
  </fieldset>
</div>
<script>
container.querySelector('input').checked = true;
document.body.offsetTop;
container.remove();
document.body.offsetTop;
</script>
