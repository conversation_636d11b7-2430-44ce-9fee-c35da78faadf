/* -*- Mode: C++; tab-width: 2; indent-tabs-mode: nil; c-basic-offset: 4 -*- */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#include "nsISupports.idl"

interface nsIOutputStream;
interface nsIInputStream;

%{C++
/**
 * The signature for the reader function passed to WriteSegments. This
 * is the "provider" of data that gets written into the stream's buffer.
 *
 * @param aOutStream stream being written to
 * @param aClosure opaque parameter passed to WriteSegments
 * @param aToSegment pointer to memory owned by the output stream
 * @param aFromOffset amount already written (since WriteSegments was called)
 * @param aCount length of toSegment
 * @param aReadCount number of bytes written
 *
 * Implementers should return the following:
 *
 * @throws <any-error> if not interested in providing any data
 *
 * Errors are never passed to the caller of WriteSegments.
 */
typedef nsresult (*nsReadSegmentFun)(nsIOutputStream *aOutStream,
                                     void *aClosure,
                                     char *aToSegment,
                                     uint32_t aFromOffset,
                                     uint32_t aCount,
                                     uint32_t *aReadCount);
%}

native nsReadSegmentFun(nsReadSegmentFun);

/**
 * nsIOutputStream
 *
 * An interface describing a writable stream of data.  An output stream may be
 * "blocking" or "non-blocking" (see the IsNonBlocking method).  A blocking
 * output stream may suspend the calling thread in order to satisfy a call to
 * Close, Flush, Write, WriteFrom, or WriteSegments.  A non-blocking output
 * stream, on the other hand, must not block the calling thread of execution.
 *
 * NOTE: blocking output streams are often written to on a background thread to
 * avoid locking up the main application thread.  For this reason, it is
 * generally the case that a blocking output stream should be implemented using
 * thread- safe AddRef and Release.
 */
[scriptable, builtinclass, uuid(0d0acd2a-61b4-11d4-9877-00c04fa0cf4a)]
interface nsIOutputStream : nsISupports
{
    /**
     * Close the stream. Forces the output stream to flush any buffered data.
     * Any subsequent calls to StreamStatus should throw NS_BASE_STREAM_CLOSED.
     * Succeeds without effect if already closed.
     *
     * @throws NS_BASE_STREAM_WOULD_BLOCK if unable to flush without blocking
     *   the calling thread (non-blocking mode only)
     */
    void close();

    /**
     * Flush the stream.
     *
     * @throws NS_BASE_STREAM_WOULD_BLOCK if unable to flush without blocking
     *   the calling thread (non-blocking mode only)
     */
    void flush();

    /**
     * Check the current status of the stream.  A stream that is closed will
     * throw an exception when this method is called.  That enables the caller
     * to know the condition of the stream before attempting to write into it.
     *
     * This method will not throw NS_BASE_STREAM_WOULD_BLOCK, even if the stream
     * is a non-blocking stream with no available space. A non-blocking stream
     * which has not been closed, but has no available room should return NS_OK.
     *
     * NOTE: This method should not block the calling thread (e.g. to query the
     * state of a file descriptor), even when called on a blocking stream.
     *
     * @throws NS_BASE_STREAM_CLOSED if the stream closed normally
     * @throws <other-error> if the stream closed with a different status
     */
    void streamStatus();

    /**
     * Write data into the stream.
     *
     * @param aBuf the buffer containing the data to be written
     * @param aCount the maximum number of bytes to be written
     *
     * @return number of bytes written (may be less than aCount)
     *
     * @throws NS_BASE_STREAM_WOULD_BLOCK if writing to the output stream would
     *   block the calling thread (non-blocking mode only)
     * @throws <other-error> on failure
     */
    unsigned long write(in string aBuf, in unsigned long aCount);

    /**
     * Writes data into the stream from an input stream.
     *
     * @param aFromStream the stream containing the data to be written
     * @param aCount the maximum number of bytes to be written
     *
     * @return number of bytes written (may be less than aCount)
     *
     * @throws NS_BASE_STREAM_WOULD_BLOCK if writing to the output stream would
     *    block the calling thread (non-blocking mode only). This failure
     *    means no bytes were transferred.
     * @throws <other-error> on failure
     *
     * NOTE: This method is defined by this interface in order to allow the
     * output stream to efficiently copy the data from the input stream into
     * its internal buffer (if any). If this method was provided as an external
     * facility, a separate char* buffer would need to be used in order to call
     * the output stream's other Write method.
     */
    unsigned long writeFrom(in nsIInputStream aFromStream,
                            in unsigned long aCount);

    /**
     * Low-level write method that has access to the stream's underlying buffer.
     * The reader function may be called multiple times for segmented buffers.
     * WriteSegments is expected to keep calling the reader until either there
     * is nothing left to write or the reader returns an error.  WriteSegments
     * should not call the reader with zero bytes to provide.
     *
     * @param aReader the "provider" of the data to be written
     * @param aClosure opaque parameter passed to reader
     * @param aCount the maximum number of bytes to be written
     *
     * @return number of bytes written (may be less than aCount)
     *
     * @throws NS_BASE_STREAM_WOULD_BLOCK if writing to the output stream would
     *    block the calling thread (non-blocking mode only). This failure
     *    means no bytes were transferred.
     * @throws NS_ERROR_NOT_IMPLEMENTED if the stream has no underlying buffer
     * @throws <other-error> on failure
     *
     * NOTE: this function may be unimplemented if a stream has no underlying
     * buffer (e.g., socket output stream).
     */
    [noscript] unsigned long writeSegments(in nsReadSegmentFun aReader,
                                           in voidPtr aClosure,
                                           in unsigned long aCount);

    /**
     * @return true if stream is non-blocking
     *
     * NOTE: writing to a blocking output stream will block the calling thread
     * until all given data can be consumed by the stream.
     *
     * NOTE: a non-blocking output stream may implement nsIAsyncOutputStream to
     * provide consumers with a way to wait for the stream to accept more data
     * once its write method is unable to accept any data without blocking.
     */
    boolean isNonBlocking();
};
