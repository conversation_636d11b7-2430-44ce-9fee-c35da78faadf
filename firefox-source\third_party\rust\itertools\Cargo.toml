# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
rust-version = "1.63.0"
name = "itertools"
version = "0.14.0"
authors = ["bluss"]
build = false
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "Extra iterator adaptors, iterator methods, free functions, and macros."
documentation = "https://docs.rs/itertools/"
readme = "README.md"
keywords = [
    "iterator",
    "data-structure",
    "zip",
    "product",
]
categories = [
    "algorithms",
    "rust-patterns",
    "no-std",
    "no-std::no-alloc",
]
license = "MIT OR Apache-2.0"
repository = "https://github.com/rust-itertools/itertools"

[features]
default = ["use_std"]
use_alloc = []
use_std = [
    "use_alloc",
    "either/use_std",
]

[lib]
name = "itertools"
path = "src/lib.rs"
test = false
bench = false

[[example]]
name = "iris"
path = "examples/iris.rs"

[[test]]
name = "adaptors_no_collect"
path = "tests/adaptors_no_collect.rs"

[[test]]
name = "flatten_ok"
path = "tests/flatten_ok.rs"

[[test]]
name = "laziness"
path = "tests/laziness.rs"

[[test]]
name = "macros_hygiene"
path = "tests/macros_hygiene.rs"

[[test]]
name = "merge_join"
path = "tests/merge_join.rs"

[[test]]
name = "peeking_take_while"
path = "tests/peeking_take_while.rs"

[[test]]
name = "quick"
path = "tests/quick.rs"

[[test]]
name = "specializations"
path = "tests/specializations.rs"

[[test]]
name = "test_core"
path = "tests/test_core.rs"

[[test]]
name = "test_std"
path = "tests/test_std.rs"

[[test]]
name = "tuples"
path = "tests/tuples.rs"

[[test]]
name = "zip"
path = "tests/zip.rs"

[[bench]]
name = "bench1"
path = "benches/bench1.rs"
harness = false

[[bench]]
name = "combinations"
path = "benches/combinations.rs"
harness = false

[[bench]]
name = "combinations_with_replacement"
path = "benches/combinations_with_replacement.rs"
harness = false

[[bench]]
name = "fold_specialization"
path = "benches/fold_specialization.rs"
harness = false

[[bench]]
name = "k_smallest"
path = "benches/k_smallest.rs"
harness = false

[[bench]]
name = "powerset"
path = "benches/powerset.rs"
harness = false

[[bench]]
name = "specializations"
path = "benches/specializations.rs"
harness = false

[[bench]]
name = "tree_reduce"
path = "benches/tree_reduce.rs"
harness = false

[[bench]]
name = "tuple_combinations"
path = "benches/tuple_combinations.rs"
harness = false

[[bench]]
name = "tuples"
path = "benches/tuples.rs"
harness = false

[dependencies.either]
version = "1.0"
default-features = false

[dev-dependencies.criterion]
version = "0.4.0"
features = ["html_reports"]

[dev-dependencies.paste]
version = "1.0.0"

[dev-dependencies.permutohedron]
version = "0.2"

[dev-dependencies.quickcheck]
version = "0.9"
default-features = false

[dev-dependencies.rand]
version = "0.7"

[profile.bench]
debug = 2
