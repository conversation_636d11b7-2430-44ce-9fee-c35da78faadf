# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies
#
# If you believe there's an error in this file please file an
# issue against the rust-lang/cargo repository. If you're
# editing this file be aware that the upstream Cargo.toml
# will likely look very different (and much more reasonable)

[package]
name = "khronos_api"
version = "3.1.0"
authors = ["<PERSON> <<EMAIL>>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> <<EMAIL>>"]
include = ["/README.md", "/src/**/*", "/Cargo.toml", "/build.rs", "/api/xml/**/*.xml", "/api_angle/scripts/**/*.xml", "/api_egl/api/**/*.xml", "/api_webgl/specs/latest/**/*.idl", "/api_webgl/extensions/**/extension.xml"]
description = "The Khronos XML API Registry, exposed as byte string constants."
homepage = "https://github.com/brendanzab/gl-rs/"
documentation = "https://docs.rs/khronos_api"
readme = "README.md"
keywords = ["gl", "egl", "opengl", "khronos"]
categories = ["rendering::graphics-api"]
license = "Apache-2.0"
repository = "https://github.com/brendanzab/gl-rs/"
