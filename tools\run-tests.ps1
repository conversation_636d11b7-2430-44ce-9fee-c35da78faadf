# Megisto Browser - Test Runner Script
# This script runs the Playwright test suite for Megisto Browser

param(
    [string]$TestType = "all",     # "all", "cookieblocker", "videomanager"
    [switch]$Headed,               # Run tests in headed mode (visible browser)
    [switch]$Debug,                # Run tests in debug mode
    [switch]$UpdateSnapshots,      # Update visual snapshots
    [string]$Browser = "megisto",  # "megisto" or "firefox"
    [switch]$Verbose
)

$ErrorActionPreference = "Stop"

Write-Host "=== Megisto Browser Test Runner ===" -ForegroundColor Green

$rootDir = Get-Location
$testsDir = Join-Path $rootDir "tests"
$megistoBrowserPath = Join-Path $rootDir "firefox-source\obj-megisto\dist\bin\megisto.exe"

# Check if Node.js is available
if (-not (Get-Command "node" -ErrorAction SilentlyContinue)) {
    Write-Error "Node.js is not installed or not in PATH. Please install Node.js first."
    exit 1
}

# Check if npm is available
if (-not (Get-Command "npm" -ErrorAction SilentlyContinue)) {
    Write-Error "npm is not installed or not in PATH. Please install Node.js with npm first."
    exit 1
}

# Change to tests directory
Set-Location $testsDir

# Install dependencies if node_modules doesn't exist
if (-not (Test-Path "node_modules")) {
    Write-Host "Installing test dependencies..." -ForegroundColor Yellow
    npm install
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to install test dependencies"
        exit 1
    }
    
    Write-Host "Installing Playwright browsers..." -ForegroundColor Yellow
    npx playwright install firefox
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to install Playwright browsers"
        exit 1
    }
}

# Check if Megisto Browser exists
if ($Browser -eq "megisto" -and -not (Test-Path $megistoBrowserPath)) {
    Write-Warning "Megisto Browser not found at: $megistoBrowserPath"
    Write-Warning "Please build Megisto Browser first using: .\tools\build.ps1"
    Write-Host "Falling back to regular Firefox for tests..." -ForegroundColor Yellow
    $Browser = "firefox"
}

# Set environment variables
$env:MEGISTO_BROWSER_PATH = $megistoBrowserPath
$env:NODE_ENV = "test"

# Build test command
$testCommand = "npx playwright test"

# Add project selection
if ($Browser -eq "megisto") {
    $testCommand += " --project=megisto-browser"
    Write-Host "Running tests with Megisto Browser" -ForegroundColor Yellow
} else {
    $testCommand += " --project=firefox-comparison"
    Write-Host "Running tests with regular Firefox" -ForegroundColor Yellow
}

# Add test type selection
switch ($TestType.ToLower()) {
    "cookieblocker" {
        $testCommand += " playwright/test-cookieblocker.spec.js"
        Write-Host "Running Cookie Blocker tests only" -ForegroundColor Yellow
    }
    "videomanager" {
        $testCommand += " playwright/test-videomanager.spec.js"
        Write-Host "Running Video Manager tests only" -ForegroundColor Yellow
    }
    "all" {
        Write-Host "Running all tests" -ForegroundColor Yellow
    }
    default {
        Write-Error "Invalid test type: $TestType. Use 'all', 'cookieblocker', or 'videomanager'"
        exit 1
    }
}

# Add options
if ($Headed) {
    $testCommand += " --headed"
    Write-Host "Running in headed mode (visible browser)" -ForegroundColor Yellow
}

if ($Debug) {
    $testCommand += " --debug"
    Write-Host "Running in debug mode" -ForegroundColor Yellow
}

if ($UpdateSnapshots) {
    $testCommand += " --update-snapshots"
    Write-Host "Updating visual snapshots" -ForegroundColor Yellow
}

if ($Verbose) {
    $testCommand += " --verbose"
}

# Create test results directory
$testResultsDir = Join-Path $testsDir "test-results"
if (-not (Test-Path $testResultsDir)) {
    New-Item -ItemType Directory -Path $testResultsDir -Force | Out-Null
}

Write-Host "`nRunning command: $testCommand" -ForegroundColor Cyan
Write-Host "Test results will be saved to: $testResultsDir" -ForegroundColor Yellow

# Run the tests
try {
    Invoke-Expression $testCommand
    $testExitCode = $LASTEXITCODE
    
    if ($testExitCode -eq 0) {
        Write-Host "`n✅ All tests passed!" -ForegroundColor Green
    } else {
        Write-Host "`n❌ Some tests failed (exit code: $testExitCode)" -ForegroundColor Red
    }
    
    # Show report information
    $htmlReportPath = Join-Path $testResultsDir "html-report\index.html"
    if (Test-Path $htmlReportPath) {
        Write-Host "`n📄 HTML Report available at: $htmlReportPath" -ForegroundColor Cyan
        Write-Host "   Run 'npx playwright show-report' to view it" -ForegroundColor White
    }
    
    $junitReportPath = Join-Path $testResultsDir "junit.xml"
    if (Test-Path $junitReportPath) {
        Write-Host "📄 JUnit Report available at: $junitReportPath" -ForegroundColor Cyan
    }
    
} catch {
    Write-Error "Failed to run tests: $_"
    $testExitCode = 1
} finally {
    # Return to original directory
    Set-Location $rootDir
}

# Exit with the same code as the test runner
exit $testExitCode
