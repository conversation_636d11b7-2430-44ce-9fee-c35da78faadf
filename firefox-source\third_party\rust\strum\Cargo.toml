# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.66.1"
name = "strum"
version = "0.27.1"
authors = ["<PERSON>felt<PERSON> <<EMAIL>>"]
description = "Helpful macros for working with enums and strings"
homepage = "https://github.com/Peternator7/strum"
documentation = "https://docs.rs/strum"
readme = "README.md"
keywords = [
    "enum",
    "string",
    "macros",
    "proc-macros",
]
categories = [
    "development-tools::procedural-macro-helpers",
    "parsing",
]
license = "MIT"
repository = "https://github.com/Peternator7/strum"

[package.metadata.docs.rs]
features = ["derive"]
rustdoc-args = [
    "--cfg",
    "docsrs",
]

[dependencies.phf]
version = "0.11"
features = ["macros"]
optional = true

[dependencies.strum_macros]
version = "0.27.1"
optional = true

[dev-dependencies]

[features]
default = ["std"]
derive = ["strum_macros"]
std = []

[badges.travis-ci]
repository = "Peternator7/strum"
