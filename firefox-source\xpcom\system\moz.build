# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

XPIDL_SOURCES += [
    "nsIBlocklistService.idl",
    "nsICrashReporter.idl",
    "nsIDeviceSensors.idl",
    "nsIGeolocationProvider.idl",
    "nsIGIOService.idl",
    "nsIGSettingsService.idl",
    "nsIHapticFeedback.idl",
    "nsIPlatformInfo.idl",
    "nsISystemInfo.idl",
    "nsIXULAppInfo.idl",
    "nsIXULRuntime.idl",
]

XPIDL_MODULE = "xpcom_system"

with Files("nsIBlocklistService.idl"):
    BUG_COMPONENT = ("Toolkit", "Blocklist Implementation")
