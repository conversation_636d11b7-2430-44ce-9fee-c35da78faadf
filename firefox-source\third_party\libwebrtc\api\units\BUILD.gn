# Copyright (c) 2018 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../webrtc.gni")

rtc_library("data_rate") {
  visibility = [ "*" ]
  sources = [
    "data_rate.cc",
    "data_rate.h",
  ]

  deps = [
    ":data_size",
    ":frequency",
    ":time_delta",
    "..:array_view",
    "../../rtc_base:checks",
    "../../rtc_base:stringutils",
    "../../rtc_base/system:rtc_export",
    "../../rtc_base/units:unit_base",
  ]
}

rtc_library("data_size") {
  visibility = [ "*" ]
  sources = [
    "data_size.cc",
    "data_size.h",
  ]

  deps = [
    "..:array_view",
    "../../rtc_base:checks",
    "../../rtc_base:stringutils",
    "../../rtc_base/system:rtc_export",
    "../../rtc_base/units:unit_base",
  ]
}

rtc_library("time_delta") {
  visibility = [ "*" ]
  sources = [
    "time_delta.cc",
    "time_delta.h",
  ]

  deps = [
    "..:array_view",
    "../../rtc_base:checks",
    "../../rtc_base:stringutils",
    "../../rtc_base/system:rtc_export",
    "../../rtc_base/units:unit_base",
  ]
}

rtc_library("frequency") {
  visibility = [ "*" ]
  sources = [
    "frequency.cc",
    "frequency.h",
  ]

  deps = [
    ":time_delta",
    "..:array_view",
    "../../rtc_base:checks",
    "../../rtc_base:stringutils",
    "../../rtc_base/system:rtc_export",
    "../../rtc_base/units:unit_base",
  ]
}

rtc_library("timestamp") {
  visibility = [ "*" ]
  sources = [
    "timestamp.cc",
    "timestamp.h",
  ]

  deps = [
    ":time_delta",
    "..:array_view",
    "../../rtc_base:checks",
    "../../rtc_base:stringutils",
    "../../rtc_base/system:rtc_export",
    "../../rtc_base/units:unit_base",
  ]
}

if (rtc_include_tests) {
  rtc_library("units_unittests") {
    testonly = true
    sources = [
      "data_rate_unittest.cc",
      "data_size_unittest.cc",
      "frequency_unittest.cc",
      "time_delta_unittest.cc",
      "timestamp_unittest.cc",
    ]
    deps = [
      ":data_rate",
      ":data_size",
      ":frequency",
      ":time_delta",
      ":timestamp",
      "../../rtc_base:checks",
      "../../rtc_base:logging",
      "../../test:test_support",
    ]
  }
}
