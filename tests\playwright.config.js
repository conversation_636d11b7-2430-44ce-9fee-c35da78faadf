/**
 * <PERSON><PERSON><PERSON> Browser - Playwright Configuration
 * Configuration for running tests against Megisto Browser
 */

const { defineConfig, devices } = require('@playwright/test');
const path = require('path');

// Determine the Megisto Browser executable path
const MEGISTO_BROWSER_PATH = process.env.MEGISTO_BROWSER_PATH || 
  path.join(__dirname, '../firefox-source/obj-megisto/dist/bin/megisto.exe');

module.exports = defineConfig({
  testDir: './playwright',
  
  // Run tests in files in parallel
  fullyParallel: true,
  
  // Fail the build on CI if you accidentally left test.only in the source code
  forbidOnly: !!process.env.CI,
  
  // Retry on CI only
  retries: process.env.CI ? 2 : 0,
  
  // Opt out of parallel tests on CI
  workers: process.env.CI ? 1 : undefined,
  
  // Reporter to use
  reporter: [
    ['html', { outputFolder: 'test-results/html-report' }],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    ['list']
  ],
  
  // Shared settings for all the projects below
  use: {
    // Base URL to use in actions like `await page.goto('/')`
    // baseURL: 'http://127.0.0.1:3000',
    
    // Collect trace when retrying the failed test
    trace: 'on-first-retry',
    
    // Capture screenshot after each test failure
    screenshot: 'only-on-failure',
    
    // Record video on failure
    video: 'retain-on-failure',
    
    // Global test timeout
    actionTimeout: 30000,
    navigationTimeout: 30000,
  },

  // Configure projects for major browsers
  projects: [
    {
      name: 'megisto-browser',
      use: {
        ...devices['Desktop Firefox'],
        // Use Megisto Browser instead of regular Firefox
        launchOptions: {
          executablePath: MEGISTO_BROWSER_PATH,
          args: [
            '--no-first-run',
            '--disable-default-browser-check',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--new-instance',
            '--no-remote'
          ],
          // Set to false to see the browser during tests
          headless: process.env.CI ? true : false,
          // Slow down operations for better visibility during development
          slowMo: process.env.CI ? 0 : 100
        }
      },
    },
    
    // Fallback to regular Firefox for comparison tests
    {
      name: 'firefox-comparison',
      use: {
        ...devices['Desktop Firefox'],
        launchOptions: {
          headless: process.env.CI ? true : false,
          slowMo: process.env.CI ? 0 : 100
        }
      },
    }
  ],

  // Global setup and teardown
  globalSetup: require.resolve('./global-setup.js'),
  globalTeardown: require.resolve('./global-teardown.js'),

  // Test timeout
  timeout: 60000,
  
  // Expect timeout
  expect: {
    timeout: 10000
  },

  // Output directory for test artifacts
  outputDir: 'test-results/artifacts',
  
  // Directory for test reports
  reportSlowTests: {
    max: 5,
    threshold: 15000
  }
});
