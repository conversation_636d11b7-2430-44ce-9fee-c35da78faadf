/* -*- Mode: C++; tab-width: 2; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* vim:set ts=2 sw=2 sts=2 et cindent: */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#include "nsIEventTarget.idl"

/**
 * A serial event target is an event dispatching interface like
 * nsIEventTarget. Runnables dispatched to an nsISerialEventTarget are required
 * to execute serially. That is, two different runnables dispatched to the
 * target should never be allowed to execute simultaneously. One exception to
 * this rule is nested event loops. If a runnable spins a nested event loop,
 * causing another runnable dispatched to the target to run, the target may
 * still be considered "serial".
 *
 * Examples:
 * - nsIThread is a serial event target.
 * - Thread pools are not serial event targets.
 * - However, one can "convert" a thread pool into an nsISerialEventTarget
 *   by putting a TaskQueue in front of it.
 */
[builtinclass, scriptable, rust_sync, uuid(9f982380-24b4-49f3-88f6-45e2952036c7)]
interface nsISerialEventTarget : nsIEventTarget
{
};
