# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.60"
name = "byteorder"
version = "1.5.0"
authors = ["<PERSON> <<EMAIL>>"]
description = "Library for reading/writing numbers in big-endian and little-endian."
homepage = "https://github.com/BurntSushi/byteorder"
documentation = "https://docs.rs/byteorder"
readme = "README.md"
keywords = [
    "byte",
    "endian",
    "big-endian",
    "little-endian",
    "binary",
]
categories = [
    "encoding",
    "parsing",
    "no-std",
]
license = "Unlicense OR MIT"
repository = "https://github.com/BurntSushi/byteorder"

[profile.bench]
opt-level = 3

[lib]
name = "byteorder"
bench = false

[dev-dependencies.quickcheck]
version = "0.9.2"
default-features = false

[dev-dependencies.rand]
version = "0.7"

[features]
default = ["std"]
i128 = []
std = []
