// |reftest| shell-option(--enable-temporal) skip-if(!this.hasOwnProperty('Temporal')||!xulRuntime.shell) -- Temporal is not enabled unconditionally, requires shell-options
// Copyright (C) 2024 <PERSON>. All rights reserved.
// This code is governed by the BSD license found in the LICENSE file.

/*---
esid: sec-temporal.instant.fromepochmilliseconds
description: >
  TypeError thrown if input is of wrong primitive type.
info: |
  Temporal.Instant.fromEpochMilliseconds ( epochMilliseconds )

  1. Set epochMilliseconds to ? ToNumber(epochMilliseconds).
  ...
features: [Temporal]
---*/

assert.throws(TypeError, () => Temporal.Instant.fromEpochMilliseconds(42n), "number");
assert.throws(TypeError, () => Temporal.Instant.fromEpochMilliseconds(Symbol()), "symbol");

reportCompare(0, 0);
