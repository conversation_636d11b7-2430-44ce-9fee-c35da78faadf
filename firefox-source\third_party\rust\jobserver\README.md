# jobserver-rs

An implementation of the GNU Make jobserver for Rust.

[![crates.io](https://img.shields.io/crates/v/jobserver.svg?maxAge=2592000)](https://crates.io/crates/jobserver)

[Documentation](https://docs.rs/jobserver)

## Usage

Add this to your `Cargo.toml`:

```toml
[dependencies]
jobserver = "0.1"
```

# License

This project is licensed under either of

 * Apache License, Version 2.0, ([LICENSE-APACHE](LICENSE-APACHE) or
   https://www.apache.org/licenses/LICENSE-2.0)
 * MIT license ([LICENSE-MIT](LICENSE-MIT) or
   https://opensource.org/license/mit)

at your option.

### Contribution

Unless you explicitly state otherwise, any contribution intentionally submitted
for inclusion in jobserver-rs by you, as defined in the Apache-2.0 license,
shall be dual licensed as above, without any additional terms or conditions.
