# Megisto Browser - Simple Build Script
# A simplified build approach that's more reliable

param(
    [switch]$Clean,
    [switch]$Package,
    [switch]$Verbose
)

$ErrorActionPreference = "Stop"

Write-Host "=== Megisto Browser Simple Build ===" -ForegroundColor Green

$rootDir = Get-Location
$firefoxDir = Join-Path $rootDir "firefox-source"
$objDir = Join-Path $firefoxDir "obj-megisto"

# Check if Firefox source exists
if (-not (Test-Path $firefoxDir)) {
    Write-Error "Firefox source not found at $firefoxDir. Please run .\tools\clone-firefox.ps1 first."
    exit 1
}

# Check if MozillaBuild is available
if (-not (Test-Path "C:\mozilla-build")) {
    Write-Error "MozillaBuild not found. Please install MozillaBuild first."
    exit 1
}

# Clean build if requested
if ($Clean -and (Test-Path $objDir)) {
    Write-Host "Cleaning previous build..." -ForegroundColor Yellow
    Remove-Item $objDir -Recurse -Force
}

# Set environment variables
$env:MOZCONFIG = Join-Path $rootDir "mozconfig"
$env:MOZ_OBJDIR = $objDir

Write-Host "MOZCONFIG: $env:MOZCONFIG" -ForegroundColor Yellow
Write-Host "MOZ_OBJDIR: $env:MOZ_OBJDIR" -ForegroundColor Yellow

# Create a simple build script for MozillaBuild
$buildScript = @"
#!/bin/bash
set -e

echo "=== Megisto Browser Build ==="
echo "Starting build process..."

# Change to Firefox source directory
cd "$($firefoxDir -replace '\\', '/')"

# Set environment variables
export MOZCONFIG="$($env:MOZCONFIG -replace '\\', '/')"
export MOZ_OBJDIR="$($objDir -replace '\\', '/')"

echo "Working directory: `$(pwd)"
echo "MOZCONFIG: `$MOZCONFIG"
echo "MOZ_OBJDIR: `$MOZ_OBJDIR"

# Check if mach exists
if [ ! -f "mach" ]; then
    echo "Error: mach not found in `$(pwd)"
    exit 1
fi

# Bootstrap if needed (first time build)
if [ ! -f "`$MOZ_OBJDIR/config.status" ]; then
    echo "Bootstrapping build environment..."
    python3 mach bootstrap --application-choice browser --no-interactive
fi

# Configure build
echo "Configuring build..."
python3 mach configure

# Build Megisto Browser
echo "Building Megisto Browser..."
if [ "$Verbose" = "True" ]; then
    python3 mach build -v
else
    python3 mach build
fi

echo "Build completed successfully!"

# Package if requested
if [ "$Package" = "True" ]; then
    echo "Creating package..."
    python3 mach package
fi

echo "=== Build Complete ==="
"@

$buildScriptPath = Join-Path $rootDir "build-megisto.sh"
# Use UTF8 without BOM
[System.IO.File]::WriteAllText($buildScriptPath, $buildScript, [System.Text.UTF8Encoding]::new($false))

Write-Host "Build script created: $buildScriptPath" -ForegroundColor Green

# Instructions for manual build
Write-Host "`n=== Manual Build Instructions ===" -ForegroundColor Cyan
Write-Host "1. Open MozillaBuild shell:" -ForegroundColor Yellow
Write-Host "   C:\mozilla-build\start-shell.bat" -ForegroundColor White
Write-Host "`n2. In the MozillaBuild shell, run:" -ForegroundColor Yellow
Write-Host "   cd '$($rootDir -replace '\\', '/')'" -ForegroundColor White
Write-Host "   bash build-megisto.sh" -ForegroundColor White

# Try automatic build
Write-Host "`n=== Attempting Automatic Build ===" -ForegroundColor Cyan
Write-Host "Trying to run build automatically..." -ForegroundColor Yellow

try {
    $process = Start-Process -FilePath "C:\mozilla-build\msys2\usr\bin\bash.exe" -ArgumentList "-l", "-c", "cd '$($rootDir -replace '\\', '/')' && bash build-megisto.sh" -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-Host "`nBuild completed successfully!" -ForegroundColor Green
        
        # Show build artifacts
        $buildDir = Join-Path $objDir "dist"
        if (Test-Path $buildDir) {
            $artifacts = Get-ChildItem -Path $buildDir -ErrorAction SilentlyContinue
            if ($artifacts) {
                Write-Host "`nBuild artifacts:" -ForegroundColor Yellow
                $artifacts | ForEach-Object {
                    Write-Host "  $($_.Name)" -ForegroundColor White
                }
            }
        }
    } else {
        Write-Warning "Automatic build failed with exit code $($process.ExitCode)"
        Write-Host "Please try the manual build instructions above." -ForegroundColor Yellow
    }
} catch {
    Write-Warning "Automatic build failed: $($_.Exception.Message)"
    Write-Host "Please try the manual build instructions above." -ForegroundColor Yellow
}

# Clean up
Remove-Item $buildScriptPath -Force -ErrorAction SilentlyContinue

Write-Host "`n=== Next Steps ===" -ForegroundColor Green
if (Test-Path (Join-Path $objDir "dist\bin\megisto.exe")) {
    Write-Host "✓ Build successful! You can run:" -ForegroundColor Green
    Write-Host "  $objDir\dist\bin\megisto.exe" -ForegroundColor White
} else {
    Write-Host "If automatic build failed, use manual instructions above." -ForegroundColor Yellow
    Write-Host "The manual approach is often more reliable for first builds." -ForegroundColor Yellow
}
}
