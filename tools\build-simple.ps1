# Megisto Browser - Simple Build Script
# A simplified build approach that's more reliable

param(
    [switch]$Clean,
    [switch]$Package,
    [switch]$Verbose
)

$ErrorActionPreference = "Stop"

Write-Host "=== Megisto Browser Simple Build ===" -ForegroundColor Green

$rootDir = Get-Location
$firefoxDir = Join-Path $rootDir "firefox-source"
$objDir = Join-Path $firefoxDir "obj-megisto"

# Check if Firefox source exists
if (-not (Test-Path $firefoxDir)) {
    Write-Error "Firefox source not found at $firefoxDir. Please run .\tools\clone-firefox.ps1 first."
    exit 1
}

# Check if MozillaBuild is available
if (-not (Test-Path "C:\mozilla-build")) {
    Write-Error "MozillaBuild not found. Please install MozillaBuild first."
    exit 1
}

# Clean build if requested
if ($Clean -and (Test-Path $objDir)) {
    Write-Host "Cleaning previous build..." -ForegroundColor Yellow
    Remove-Item $objDir -Recurse -Force
}

# Set environment variables
$env:MOZCONFIG = Join-Path $rootDir "mozconfig"
$env:MOZ_OBJDIR = $objDir

Write-Host "MOZCONFIG: $env:MOZCONFIG" -ForegroundColor Yellow
Write-Host "MOZ_OBJDIR: $env:MOZ_OBJDIR" -ForegroundColor Yellow

Write-Host "`n=== Manual Build Instructions ===" -ForegroundColor Cyan
Write-Host "For the most reliable build, please follow these manual steps:" -ForegroundColor Yellow
Write-Host "`n1. Open MozillaBuild shell:" -ForegroundColor Yellow
Write-Host "   C:\mozilla-build\start-shell.bat" -ForegroundColor White
Write-Host "`n2. In the MozillaBuild shell, run these commands:" -ForegroundColor Yellow
Write-Host "   cd /e/megisto/firefox-source" -ForegroundColor White
Write-Host "   export MOZCONFIG=/e/megisto/mozconfig" -ForegroundColor White
Write-Host "   python3 mach bootstrap --application-choice browser --no-interactive" -ForegroundColor White
Write-Host "   python3 mach build" -ForegroundColor White

if ($Package) {
    Write-Host "   python3 mach package" -ForegroundColor White
}

Write-Host "`n=== Alternative: Try Automatic Build ===" -ForegroundColor Cyan
$response = Read-Host "Do you want to try automatic build? (y/n)"

if ($response -eq 'y' -or $response -eq 'Y') {
    Write-Host "Attempting automatic build..." -ForegroundColor Yellow
    
    # Create a simple build script
    $buildScript = @"
#!/bin/bash
set -e
echo "=== Megisto Browser Build ==="
cd /e/megisto/firefox-source
export MOZCONFIG=/e/megisto/mozconfig
export MOZ_OBJDIR=/e/megisto/firefox-source/obj-megisto
echo "Working directory: `$(pwd)"
echo "MOZCONFIG: `$MOZCONFIG"
if [ ! -f "mach" ]; then
    echo "Error: mach not found"
    exit 1
fi
if [ ! -f "`$MOZ_OBJDIR/config.status" ]; then
    echo "Bootstrapping..."
    python3 mach bootstrap --application-choice browser --no-interactive
fi
echo "Building..."
python3 mach build
echo "Build complete!"
"@

    $buildScriptPath = Join-Path $rootDir "build-megisto.sh"
    [System.IO.File]::WriteAllText($buildScriptPath, $buildScript, [System.Text.UTF8Encoding]::new($false))
    
    try {
        $bashPath = "C:\mozilla-build\msys2\usr\bin\bash.exe"
        $process = Start-Process -FilePath $bashPath -ArgumentList "-l", "-c", "cd /e/megisto && bash build-megisto.sh" -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0) {
            Write-Host "Build completed successfully!" -ForegroundColor Green
        } else {
            Write-Host "Automatic build failed. Please use manual instructions above." -ForegroundColor Red
        }
    } catch {
        Write-Host "Automatic build failed. Please use manual instructions above." -ForegroundColor Red
    }
    
    # Clean up
    Remove-Item $buildScriptPath -Force -ErrorAction SilentlyContinue
}

Write-Host "`nAfter successful build, you can run:" -ForegroundColor Green
Write-Host "firefox-source\obj-megisto\dist\bin\megisto.exe" -ForegroundColor White
