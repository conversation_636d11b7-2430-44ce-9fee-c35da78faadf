#
# This file is autogenerated by pip-compile with Python 3.13
# by the following command:
#
#    pip-compile dev.in
#
alabaster==1.0.0
    # via
    #   -r docs.txt
    #   sphinx
babel==2.16.0
    # via
    #   -r docs.txt
    #   sphinx
cachetools==5.5.0
    # via tox
certifi==2024.8.30
    # via
    #   -r docs.txt
    #   requests
cfgv==3.4.0
    # via pre-commit
chardet==5.2.0
    # via tox
charset-normalizer==3.4.0
    # via
    #   -r docs.txt
    #   requests
colorama==0.4.6
    # via tox
distlib==0.3.9
    # via virtualenv
docutils==0.21.2
    # via
    #   -r docs.txt
    #   sphinx
filelock==3.16.1
    # via
    #   tox
    #   virtualenv
identify==2.6.1
    # via pre-commit
idna==3.10
    # via
    #   -r docs.txt
    #   requests
imagesize==1.4.1
    # via
    #   -r docs.txt
    #   sphinx
iniconfig==2.0.0
    # via
    #   -r tests.txt
    #   -r typing.txt
    #   pytest
jinja2==3.1.4
    # via
    #   -r docs.txt
    #   sphinx
markupsafe==3.0.1
    # via
    #   -r docs.txt
    #   jinja2
mypy==1.12.0
    # via -r typing.txt
mypy-extensions==1.0.0
    # via
    #   -r typing.txt
    #   mypy
nodeenv==1.9.1
    # via
    #   -r typing.txt
    #   pre-commit
    #   pyright
packaging==24.1
    # via
    #   -r docs.txt
    #   -r tests.txt
    #   -r typing.txt
    #   pallets-sphinx-themes
    #   pyproject-api
    #   pytest
    #   sphinx
    #   tox
pallets-sphinx-themes==2.2.0
    # via -r docs.txt
platformdirs==4.3.6
    # via
    #   tox
    #   virtualenv
pluggy==1.5.0
    # via
    #   -r tests.txt
    #   -r typing.txt
    #   pytest
    #   tox
pre-commit==4.0.1
    # via -r dev.in
pygments==2.18.0
    # via
    #   -r docs.txt
    #   sphinx
pyproject-api==1.8.0
    # via tox
pyright==1.1.385
    # via -r typing.txt
pytest==8.3.3
    # via
    #   -r tests.txt
    #   -r typing.txt
pyyaml==6.0.2
    # via pre-commit
requests==2.32.3
    # via
    #   -r docs.txt
    #   sphinx
snowballstemmer==2.2.0
    # via
    #   -r docs.txt
    #   sphinx
sphinx==8.1.3
    # via
    #   -r docs.txt
    #   pallets-sphinx-themes
    #   sphinx-notfound-page
    #   sphinxcontrib-log-cabinet
sphinx-notfound-page==1.0.4
    # via
    #   -r docs.txt
    #   pallets-sphinx-themes
sphinxcontrib-applehelp==2.0.0
    # via
    #   -r docs.txt
    #   sphinx
sphinxcontrib-devhelp==2.0.0
    # via
    #   -r docs.txt
    #   sphinx
sphinxcontrib-htmlhelp==2.1.0
    # via
    #   -r docs.txt
    #   sphinx
sphinxcontrib-jsmath==1.0.1
    # via
    #   -r docs.txt
    #   sphinx
sphinxcontrib-log-cabinet==1.0.1
    # via -r docs.txt
sphinxcontrib-qthelp==2.0.0
    # via
    #   -r docs.txt
    #   sphinx
sphinxcontrib-serializinghtml==2.0.0
    # via
    #   -r docs.txt
    #   sphinx
tox==4.22.0
    # via -r dev.in
typing-extensions==4.12.2
    # via
    #   -r typing.txt
    #   mypy
    #   pyright
urllib3==2.2.3
    # via
    #   -r docs.txt
    #   requests
virtualenv==20.26.6
    # via
    #   pre-commit
    #   tox
