# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.73.0"
name = "bumpalo"
version = "3.15.4"
authors = ["<PERSON> <<EMAIL>>"]
exclude = [
    "/.github/*",
    "/benches",
    "/tests",
    "valgrind.supp",
    "bumpalo.png",
]
description = "A fast bump allocation arena for Rust."
documentation = "https://docs.rs/bumpalo"
readme = "README.md"
categories = [
    "memory-management",
    "rust-patterns",
    "no-std",
]
license = "MIT OR Apache-2.0"
repository = "https://github.com/fitzgen/bumpalo"

[package.metadata.docs.rs]
all-features = true

[lib]
path = "src/lib.rs"
bench = false

[[test]]
name = "try_alloc"
path = "tests/try_alloc.rs"
harness = false

[[bench]]
name = "benches"
path = "benches/benches.rs"
harness = false
required-features = ["collections"]

[dependencies.allocator-api2]
version = "0.2.8"
optional = true
default-features = false

[dev-dependencies.criterion]
version = "0.3.6"

[dev-dependencies.quickcheck]
version = "1.0.3"

[dev-dependencies.rand]
version = "0.8.5"

[features]
allocator_api = []
boxed = []
collections = []
default = []
std = []
