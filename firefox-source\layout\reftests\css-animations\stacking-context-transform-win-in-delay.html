<!DOCTYPE html>
<title>
Transform animation winning over another transform animation in delay phase
creates a stacking context
</title>
<style>
span {
  height: 100px;
  width: 100px;
  position: fixed;
  background: green;
  top: 50px;
}
@keyframes TransformNone {
  from, to { transform: none; }
}
@keyframes Transform100px {
  from, to { transform: translateX(100px); }
}
#test {
  width: 100px; height: 100px;
  background: blue;
  animation: Transform100px 100s 100s, TransformNone 100s;
}
</style>
<span></span>
<div id="test"></div>
