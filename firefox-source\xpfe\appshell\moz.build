# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

with Files("**"):
    BUG_COMPONENT = ("Core", "Window Management")

MOCHITEST_CHROME_MANIFESTS += ["test/chrome.toml"]

XPIDL_SOURCES += [
    "nsIAppShellService.idl",
    "nsIAppWindow.idl",
    "nsIWindowlessBrowser.idl",
    "nsIWindowMediator.idl",
    "nsIWindowMediatorListener.idl",
    "nsIXULBrowserWindow.idl",
]

XPIDL_MODULE = "appshell"

EXPORTS += [
    "LiveResizeListener.h",
    "nsAppShellCID.h",
]

UNIFIED_SOURCES += [
    "AppWindow.cpp",
    "nsAppShellService.cpp",
    "nsAppShellWindowEnumerator.cpp",
    "nsChromeTreeOwner.cpp",
    "nsContentTreeOwner.cpp",
    "nsWindowMediator.cpp",
]

XPCOM_MANIFESTS += [
    "components.conf",
]

LOCAL_INCLUDES += [
    "/dom/base",
    "/dom/xul",
]

FINAL_LIBRARY = "xul"

include("/ipc/chromium/chromium-config.mozbuild")
