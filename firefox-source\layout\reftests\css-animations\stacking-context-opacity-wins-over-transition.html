<!DOCTYPE html>
<html class="reftest-wait">
<title>
Opacity animation winning over opacity transition creates a stacking context
for correct style.
</title>
<style>
span {
  height: 100px;
  width: 100px;
  position: fixed;
  background: green;
  top: 50px;
}
@keyframes Opacity1 {
  from, to { opacity: 1; }
}
#test {
  width: 100px; height: 100px;
  background: blue;
  opacity: 1;
  transition: opacity 100s steps(1, start);
  animation: Opacity1 100s;
}
</style>
<span></span>
<div id="test"></div>
<script>
window.addEventListener("load", () => {
  var target = document.getElementById("test");
  getComputedStyle(target).opacity;

  // CSS animation wins over transitions, so transition won't be visible during
  // the CSS animation.
  target.style.opacity = 0;
  getComputedStyle(target).opacity;
  document.documentElement.classList.remove("reftest-wait");
});
</script>
