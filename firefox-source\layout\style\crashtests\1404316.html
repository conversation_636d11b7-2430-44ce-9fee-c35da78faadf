<html contenteditable="">
  <style></style>
  <script>
  try { o1 = document.getSelection() } catch(e) { }
  try { o2 = document.createElement('table') } catch(e) { }
  try { o3 = document.createElement('r') } catch(e) { }
  try { o4 = document.createElement('l') } catch(e) { }
  try { o5 = document.createElement('k') } catch(e) { }
  try { o6 = document.createElement('u') } catch(e) { }
  try { document.documentElement.appendChild(o2) } catch(e) { }
  try { document.documentElement.appendChild(o3) } catch(e) { }
  try { document.documentElement.appendChild(o4) } catch(e) { }
  try { document.documentElement.appendChild(o5) } catch(e) { }
  try { document.designMode = "on"; } catch(e) { }
  try { document.designMode = "off"; } catch(e) { }
  try { o1.selectAllChildren(o2) } catch(e) { }
  try { document.styleSheets[0].insertRule("e{", 0); } catch(e) { }
  try { document.documentElement.appendChild(o6) } catch(e) { }
  </script>
</html>