# Megisto Browser - Portable Package Creation Script
# Creates a portable version of Megisto Browser

param(
    [string]$Version = "1.0.0",
    [string]$Architecture = "x64",
    [switch]$IncludeSource,
    [switch]$Compress,
    [switch]$Verbose
)

$ErrorActionPreference = "Stop"

Write-Host "=== Megisto Browser Portable Package Creation ===" -ForegroundColor Green

$rootDir = Get-Location
$firefoxDir = Join-Path $rootDir "firefox-source"
$objDir = Join-Path $firefoxDir "obj-megisto"
$buildDir = Join-Path $objDir "dist"
$distDir = Join-Path $rootDir "dist"
$portableDir = Join-Path $distDir "portable"

# Check if build exists
if (-not (Test-Path $buildDir)) {
    Write-Error "Megisto Browser build not found at $buildDir. Please run .\tools\build.ps1 first."
    exit 1
}

# Create portable directory
if (Test-Path $portableDir) {
    Remove-Item $portableDir -Recurse -Force
}
New-Item -ItemType Directory -Path $portableDir -Force | Out-Null

Write-Host "Creating portable package for Megisto Browser v$Version ($Architecture)" -ForegroundColor Yellow

# Copy browser files
Write-Host "Copying browser files..." -ForegroundColor Yellow
$browserPortableDir = Join-Path $portableDir "MegistoBrowser-$Version-$Architecture-Portable"
Copy-Item $buildDir $browserPortableDir -Recurse -Force

# Create portable configuration
Write-Host "Creating portable configuration..." -ForegroundColor Yellow

# Create portable launcher
$launcherScript = @"
@echo off
title Megisto Browser (Portable)

REM Set portable mode
set MOZ_LEGACY_PROFILES=1
set MOZ_ALLOW_DOWNGRADE=1

REM Create portable profile directory
if not exist "%~dp0profile" mkdir "%~dp0profile"

REM Launch Megisto Browser in portable mode
"%~dp0megisto.exe" -profile "%~dp0profile" -no-remote %*
"@

$launcherPath = Join-Path $browserPortableDir "MegistoBrowser-Portable.bat"
$launcherScript | Out-File -FilePath $launcherPath -Encoding ASCII

# Create portable configuration file
$portableConfig = @"
# Megisto Browser Portable Configuration
# This file configures Megisto Browser for portable operation

# Profile settings
user_pref("browser.shell.checkDefaultBrowser", false);
user_pref("browser.rights.3.shown", true);
user_pref("browser.startup.homepage_override.mstone", "ignore");
user_pref("startup.homepage_welcome_url", "");
user_pref("startup.homepage_welcome_url.additional", "");

# Disable automatic updates
user_pref("app.update.enabled", false);
user_pref("app.update.auto", false);
user_pref("app.update.mode", 0);
user_pref("app.update.service.enabled", false);

# Privacy settings
user_pref("privacy.trackingprotection.enabled", true);
user_pref("privacy.donottrackheader.enabled", true);
user_pref("datareporting.healthreport.uploadEnabled", false);
user_pref("datareporting.policy.dataSubmissionEnabled", false);

# Megisto-specific settings
user_pref("megisto.cookieBlocking.enabled", true);
user_pref("megisto.youtube.enhancement.enabled", true);
user_pref("megisto.youtube.autoplay", false);
user_pref("megisto.youtube.defaultQuality", "720p");
user_pref("megisto.youtube.blockRelated", true);
"@

$configPath = Join-Path $browserPortableDir "defaults\pref\megisto-portable.js"
$configDir = Split-Path $configPath -Parent
if (-not (Test-Path $configDir)) {
    New-Item -ItemType Directory -Path $configDir -Force | Out-Null
}
$portableConfig | Out-File -FilePath $configPath -Encoding UTF8

# Create README file
$readmeContent = @"
# Megisto Browser Portable v$Version

## What is Megisto Browser?

Megisto Browser is a privacy-focused Firefox fork with built-in cookie banner blocking and YouTube enhancement features.

## Portable Version Features

- No installation required
- Runs from any location (USB drive, network drive, etc.)
- Stores all data in the portable directory
- Does not modify system registry
- Can run alongside other browsers

## How to Use

1. Extract this folder to your desired location
2. Run "MegistoBrowser-Portable.bat" to start the browser
3. Your profile and settings will be stored in the "profile" folder

## Features

### Cookie Banner Blocking
- Automatically blocks cookie consent banners
- Supports major websites and CMPs
- Customizable blocking rules
- Statistics tracking

### YouTube Enhancement
- Replaces YouTube embeds with enhanced Video.js player
- Custom video controls
- Quality selection
- Picture-in-picture support
- Theater mode

### Privacy Features
- Tracking protection enabled by default
- Do Not Track header
- Telemetry disabled
- No data collection

## Files and Folders

- MegistoBrowser-Portable.bat - Main launcher
- megisto.exe - Browser executable
- profile/ - Portable profile directory (created on first run)
- defaults/ - Default configuration files

## System Requirements

- Windows 10/11 (64-bit)
- 4GB RAM minimum, 8GB recommended
- 500MB free disk space

## Support

For support and updates, visit:
https://github.com/megisto/megisto-browser

## License

Megisto Browser is based on Mozilla Firefox and is distributed under the Mozilla Public License 2.0.
"@

$readmePath = Join-Path $browserPortableDir "README.txt"
$readmeContent | Out-File -FilePath $readmePath -Encoding UTF8

# Include source code if requested
if ($IncludeSource) {
    Write-Host "Including source code..." -ForegroundColor Yellow
    $sourceDir = Join-Path $browserPortableDir "source"
    New-Item -ItemType Directory -Path $sourceDir -Force | Out-Null
    
    # Copy essential source files
    $sourceFiles = @(
        "browser\extensions\megisto-addon",
        "docs",
        "tools",
        "tests",
        "README.md",
        "LICENSE"
    )
    
    foreach ($sourceFile in $sourceFiles) {
        $sourcePath = Join-Path $rootDir $sourceFile
        if (Test-Path $sourcePath) {
            $destPath = Join-Path $sourceDir $sourceFile
            $destDir = Split-Path $destPath -Parent
            if (-not (Test-Path $destDir)) {
                New-Item -ItemType Directory -Path $destDir -Force | Out-Null
            }
            Copy-Item $sourcePath $destPath -Recurse -Force
        }
    }
}

# Create version info file
$versionInfo = @"
{
  "name": "Megisto Browser",
  "version": "$Version",
  "architecture": "$Architecture",
  "type": "portable",
  "buildDate": "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')",
  "basedOn": "Mozilla Firefox",
  "features": [
    "Cookie Banner Blocking",
    "YouTube Enhancement",
    "Privacy Protection",
    "Portable Operation"
  ]
}
"@

$versionPath = Join-Path $browserPortableDir "version.json"
$versionInfo | Out-File -FilePath $versionPath -Encoding UTF8

# Compress if requested
if ($Compress) {
    Write-Host "Creating compressed archive..." -ForegroundColor Yellow
    
    $archiveName = "MegistoBrowser-$Version-$Architecture-Portable.zip"
    $archivePath = Join-Path $distDir $archiveName
    
    # Remove existing archive
    if (Test-Path $archivePath) {
        Remove-Item $archivePath -Force
    }
    
    # Create ZIP archive
    try {
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        [System.IO.Compression.ZipFile]::CreateFromDirectory($browserPortableDir, $archivePath)
        
        $archiveSize = (Get-Item $archivePath).Length / 1MB
        Write-Host "Archive created: $archivePath" -ForegroundColor Green
        Write-Host "Archive size: $([math]::Round($archiveSize, 2)) MB" -ForegroundColor Green
        
    } catch {
        Write-Warning "Failed to create ZIP archive: $_"
        Write-Host "You can manually compress the folder: $browserPortableDir" -ForegroundColor Yellow
    }
}

# Calculate package size
$packageSize = (Get-ChildItem $browserPortableDir -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB

Write-Host "`n=== Portable Package Creation Complete ===" -ForegroundColor Green
Write-Host "Portable package created successfully!" -ForegroundColor Green
Write-Host "Location: $browserPortableDir" -ForegroundColor Cyan
Write-Host "Size: $([math]::Round($packageSize, 2)) MB" -ForegroundColor Cyan

if ($Compress) {
    Write-Host "Compressed archive: $archivePath" -ForegroundColor Cyan
}

Write-Host "`nTo use the portable version:" -ForegroundColor Yellow
Write-Host "1. Copy the folder to your desired location" -ForegroundColor White
Write-Host "2. Run MegistoBrowser-Portable.bat" -ForegroundColor White
Write-Host "3. Enjoy privacy-focused browsing!" -ForegroundColor White
