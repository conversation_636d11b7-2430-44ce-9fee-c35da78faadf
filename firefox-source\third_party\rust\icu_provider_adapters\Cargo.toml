# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.82"
name = "icu_provider_adapters"
version = "2.0.0"
authors = ["The ICU4X Project Developers"]
build = false
include = [
    "data/**/*",
    "src/**/*",
    "examples/**/*",
    "benches/**/*",
    "tests/**/*",
    "Cargo.toml",
    "LICENSE",
    "README.md",
    "build.rs",
]
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "Adapters for composing and manipulating data providers."
homepage = "https://icu4x.unicode.org"
readme = "README.md"
categories = ["internationalization"]
license = "Unicode-3.0"
repository = "https://github.com/unicode-org/icu4x"

[features]
export = ["icu_provider/export"]

[lib]
name = "icu_provider_adapters"
path = "src/lib.rs"

[dependencies.databake]
version = "0.2.0"
features = ["derive"]
optional = true
default-features = false

[dependencies.icu_locale]
version = "~2.0.0"
default-features = false

[dependencies.icu_provider]
version = "2.0.0"
features = ["alloc"]
default-features = false

[dependencies.serde]
version = "1.0.110"
features = [
    "derive",
    "alloc",
]
optional = true
default-features = false

[dependencies.tinystr]
version = "0.8.0"
features = ["zerovec"]
default-features = false

[dependencies.zerovec]
version = "0.11.1"
features = ["yoke"]
default-features = false

[dev-dependencies]
