/* -*- Mode: C++; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 4 -*- */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#include "nsISupports.idl"

/**
 * This interface is implemented by an object that wants
 * to observe an event corresponding to a topic.
 */

[scriptable, function, uuid(DB242E01-E4D9-11d2-9DDE-000064657374)]
interface nsIObserver : nsISupports {

   /**
    * Observe will be called when there is a notification for the
    * topic |aTopic|.  This assumes that the object implementing
    * this interface has been registered with an observer service
    * such as the nsIObserverService.
    *
    * If you expect multiple topics/subjects, the impl is
    * responsible for filtering.
    *
    * You should not modify, add, remove, or enumerate
    * notifications in the implemention of observe.
    *
    * @param aSubject : Notification specific interface pointer.
    * @param aTopic   : The notification topic or subject.
    * @param aData    : Notification specific wide string.
    *                    subject event.
    */
    void observe( in nsISupports aSubject,
                  in string      aTopic,
                  in wstring     aData );

};
