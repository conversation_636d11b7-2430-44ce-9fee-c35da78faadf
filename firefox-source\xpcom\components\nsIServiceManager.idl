/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */


#include "nsISupports.idl"

/**
 * The nsIServiceManager manager interface provides a means to obtain
 * global services in an application. The service manager depends on the
 * repository to find and instantiate factories to obtain services.
 *
 * Users of the service manager must first obtain a pointer to the global
 * service manager by calling NS_GetServiceManager. After that,
 * they can request specific services by calling GetService. When they are
 * finished they can NS_RELEASE() the service as usual.
 *
 * A user of a service may keep references to particular services indefinitely
 * and only must call Release when it shuts down.
 */

[builtinclass, scriptable, uuid(8bb35ed9-e332-462d-9155-4a002ab5c958)]
interface nsIServiceManager : nsISupports
{
    /**
     * getServiceByContractID
     *
     * Returns the instance that implements aClass or aContractID and the
     * interface aIID.  This may result in the instance being created.
     *
     * @param aClass or aContractID : aClass or aContractID of object
     *                                instance requested
     * @param aIID : IID of interface requested
     * @param result : resulting service
     */
    void getService(in nsCIDRef aClass,
                    in nsIIDRef aIID,
                    [iid_is(aIID),retval] out nsQIResult result);

    void getServiceByContractID(in string aContractID,
                                in nsIIDRef aIID,
                                [iid_is(aIID),retval] out nsQIResult result);

    /**
     * isServiceInstantiated
     *
     * isServiceInstantiated will return a true if the service has already
     * been created, or false otherwise. Throws if the service does not
     * implement the given IID.
     *
     * @param aClass or aContractID : aClass or aContractID of object
     *                                instance requested
     * @param aIID : IID of interface requested
     * @throws NS_NOINTERFACE if the IID given isn't supported by the object
     */
    boolean isServiceInstantiated(in nsCIDRef aClass, in nsIIDRef aIID);
    boolean isServiceInstantiatedByContractID(in string aContractID, in nsIIDRef aIID);
};


%{C++
#ifdef MOZILLA_INTERNAL_API
#include "nsXPCOM.h"
#include "nsComponentManagerUtils.h"
#include "nsServiceManagerUtils.h"
#endif
%}
