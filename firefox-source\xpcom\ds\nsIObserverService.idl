/* -*- Mode: C++; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 4 -*- */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#include "nsISupports.idl"

interface nsIObserver;
interface nsISimpleEnumerator;

/**
 * nsIObserverService
 *
 * Service allows a client listener (nsIObserver) to register and unregister for
 * notifications of specific string referenced topic. Service also provides a
 * way to notify registered listeners and a way to enumerate registered client
 * listeners.
 */

[scriptable, builtinclass, uuid(D07F5192-E3D1-11d2-8ACD-00105A1B8860)]
interface nsIObserverService : nsISupports
{

    /**
     * AddObserver
     *
     * Registers a given listener for a notifications regarding the specified
     * topic.
     *
     * @param anObserve : The interface pointer which will receive notifications.
     * @param aTopic    : The notification topic or subject.
     * @param ownsWeak  : If set to false, the nsIObserverService will hold a
     *                    strong reference to |anObserver|.  If set to true and
     *                    |anObserver| supports the nsIWeakReference interface,
     *                    a weak reference will be held.  Otherwise an error will be
     *                    returned.
     */
    void addObserver( in nsIObserver anObserver, in string aTopic,
                      [optional] in boolean ownsWeak);

    /**
     * removeObserver
     *
     * Unregisters a given listener from notifications regarding the specified
     * topic.
     *
     * @param anObserver : The interface pointer which will stop recieving
     *                     notifications.
     * @param aTopic     : The notification topic or subject.
     */
    void removeObserver( in nsIObserver anObserver, in string aTopic );

    /**
     * notifyObservers
     *
     * Notifies all registered listeners of the given topic.
     * Must not be used with shutdown topics (will assert
     * on the parent process).
     *
     * @param aSubject : Notification specific interface pointer.
     * @param aTopic   : The notification topic or subject.
     * @param someData : Notification specific wide string.
     */
    void notifyObservers( in nsISupports aSubject,
                          in string aTopic,
                          [optional] in wstring someData );

    /**
     * hasObservers
     *
     * Checks to see if there are registered listeners for the given topic.
     *
     * Implemented in "nsObserverService.cpp".
     *
     * @param aTopic   : The notification topic or subject.
     * @param aFound : An out parameter; True if there are registered observers,
     * False otherwise.
     */
    [noscript, notxpcom, nostdcall] boolean hasObservers(in string aTopic);

    %{C++
    /**
     * notifyWhenScriptSafe
     *
     * Notifies all registered listeners of the given topic once it is safe to
     * run script.
     *
     * Implemented in "nsObserverService.cpp".
     *
     * @param aSubject : Notification specific interface pointer.
     * @param aTopic   : The notification topic or subject.
     * @param someData : Notification specific wide string.
     */
    nsresult NotifyWhenScriptSafe(nsISupports* aSubject,
                                  const char* aTopic,
                                  const char16_t* aData = nullptr);
    %}

    /**
     * enumerateObservers
     *
     * Returns an enumeration of all registered listeners.
     *
     * @param aTopic   : The notification topic or subject.
     */
    nsISimpleEnumerator enumerateObservers( in string aTopic );


};
