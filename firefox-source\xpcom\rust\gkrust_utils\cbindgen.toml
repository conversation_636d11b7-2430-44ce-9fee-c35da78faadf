header = """/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */"""
autogen_warning = """/* DO NOT MODIFY THIS MANUALLY! This file was generated using cbindgen.
 * To generate this file:
 *   1. Get the latest cbindgen using `cargo install --force cbindgen`
 *      a. Alternatively, you can clone `https://github.com/eqrion/cbindgen` and use a tagged release
 *   2. Run `rustup run nightly cbindgen xpcom/rust/gkrust_utils --lockfile Cargo.lock --crate gkrust_utils -o xpcom/base/gk_rust_utils_ffi_generated.h`
 */
#include "nsError.h"
#include "nsString.h"
"""
include_version = true
braces = "SameLine"
line_length = 100
tab_width = 2
language = "C++"
namespaces = ["mozilla"]

[export]
# Skip constants because we don't have any
item_types = ["globals", "enums", "structs", "unions", "typedefs", "opaque", "functions"]

[enum]
add_sentinel = true
derive_helper_methods = true

[defines]
"target_os = windows" = "XP_WIN"
"target_os = macos" = "XP_MACOSX"
"target_os = android" = "ANDROID"
