{"name": "megisto-browser-tests", "version": "1.0.0", "description": "Test suite for <PERSON><PERSON><PERSON>", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:cookieblocker": "playwright test test-cookieblocker.spec.js", "test:videomanager": "playwright test test-videomanager.spec.js", "test:report": "playwright show-report", "install-browsers": "playwright install firefox"}, "keywords": ["megisto", "browser", "testing", "playwright", "firefox"], "author": "Megisto Browser Team", "license": "MPL-2.0", "devDependencies": {"@playwright/test": "^1.40.0"}, "engines": {"node": ">=16.0.0"}}