{"files": {"Cargo.lock": "d535abf03b4414217a669a79886fe2040e09502f7d483482759869b53faa5899", "Cargo.toml": "f269244c7b17dc6f5b3b855decc03382e09f9e5f9f155bc24aed03c221362f32", "LICENSE": "f367c1b8e1aa262435251e442901da4607b4650e0e63a026f5044473ecfb90f2", "README.md": "d91500cb944c05c04ed9e6920d139199a9d1892346de50dccbdce5cbef3b9174", "src/either.rs": "326038f20e4e5e1a5d8c2c9fd935abdcba7d7ad7ebda0b29346be71bde964bde", "src/empty.rs": "0c9350b90efd4b8d7c6f0ee969926a21ccb5a4fabea232d249d266e6bda299e5", "src/fallback/mod.rs": "ac73ce4e0186547f4ef6c0cf02fc29d021e89376ce1ed48e84a09a6e6323f383", "src/filter/impls.rs": "4ffaa1eddf517eadcf1f3903c088d8506e3bef62370169d84b0ccc1b5d29ea72", "src/filter/mod.rs": "2932ba1df032188f468748114638f4c7335320a60476063a7618908540470a88", "src/fixed.rs": "2ee0f892a9b6d1b9c8c17c18dee90c9ff92e1864a764bad76404e17e3b4e5d8d", "src/fork/by_error.rs": "63ac54d3ab2edf44206443f7be9d69e08c73cf3450101a721a73f7b8e8c75cf5", "src/fork/mod.rs": "1cc5d6a1b3f4cceeb0084d008029ef72c2b8345ba74654ba58d188f7bbe49635", "src/fork/predicates.rs": "40ec14c3c37b90449c3f4dbc58baccd0b5a4e9171fefcbee4db208db36cf4222", "src/lib.rs": "83eb9e474da22e1f72aee01c162d3deb3440dd3626cabdf2a8cf945ae4fdfd64"}, "package": "85c260e90e962088d46a1605687f78d3293cc3a34ba66b08ff7361084bc7895e"}