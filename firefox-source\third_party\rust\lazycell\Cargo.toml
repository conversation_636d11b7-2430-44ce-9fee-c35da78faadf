# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies
#
# If you believe there's an error in this file please file an
# issue against the rust-lang/cargo repository. If you're
# editing this file be aware that the upstream Cargo.toml
# will likely look very different (and much more reasonable)

[package]
name = "lazycell"
version = "1.3.0"
authors = ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"]
include = ["CHANGELOG.md", "Cargo.toml", "LICENSE-MIT", "LICENSE-APACHE", "README.md", "src/**/*.rs"]
description = "A library providing a lazily filled Cell struct"
documentation = "http://indiv0.github.io/lazycell/lazycell/"
readme = "README.md"
keywords = ["lazycell", "lazy", "cell", "library"]
license = "MIT/Apache-2.0"
repository = "https://github.com/indiv0/lazycell"
[dependencies.clippy]
version = "0.0"
optional = true

[dependencies.serde]
version = "^1"
optional = true

[features]
nightly = []
nightly-testing = ["clippy", "nightly"]
