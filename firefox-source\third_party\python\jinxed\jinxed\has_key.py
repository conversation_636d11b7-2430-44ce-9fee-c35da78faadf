"""
key mapping numeric to cap
"""

from jinxed import _keys


_capability_names = {  # pylint: disable=invalid-name
    _keys.KEY_A1: 'ka1',
    _keys.KEY_A3: 'ka3',
    _keys.KEY_B2: 'kb2',
    _keys.KEY_BACKSPACE: 'kbs',
    _keys.KEY_BEG: 'kbeg',
    _keys.KEY_BTAB: 'kcbt',
    _keys.KEY_C1: 'kc1',
    _keys.KEY_C3: 'kc3',
    _keys.KEY_CANCEL: 'kcan',
    _keys.KEY_CATAB: 'ktbc',
    _keys.KEY_CLEAR: 'kclr',
    _keys.KEY_CLOSE: 'kclo',
    _keys.KEY_COMMAND: 'kcmd',
    _keys.KEY_COPY: 'kcpy',
    _keys.KEY_CREATE: 'kcrt',
    _keys.KEY_CTAB: 'kctab',
    _keys.KEY_DC: 'kdch1',
    _keys.KEY_DL: 'kdl1',
    _keys.KEY_DOWN: 'kcud1',
    _keys.KEY_EIC: 'krmir',
    _keys.KEY_END: 'kend',
    _keys.KEY_ENTER: 'kent',
    _keys.KEY_EOL: 'kel',
    _keys.KEY_EOS: 'ked',
    _keys.KEY_EXIT: 'kext',
    _keys.KEY_F0: 'kf0',
    _keys.KEY_F1: 'kf1',
    _keys.KEY_F10: 'kf10',
    _keys.KEY_F11: 'kf11',
    _keys.KEY_F12: 'kf12',
    _keys.KEY_F13: 'kf13',
    _keys.KEY_F14: 'kf14',
    _keys.KEY_F15: 'kf15',
    _keys.KEY_F16: 'kf16',
    _keys.KEY_F17: 'kf17',
    _keys.KEY_F18: 'kf18',
    _keys.KEY_F19: 'kf19',
    _keys.KEY_F2: 'kf2',
    _keys.KEY_F20: 'kf20',
    _keys.KEY_F21: 'kf21',
    _keys.KEY_F22: 'kf22',
    _keys.KEY_F23: 'kf23',
    _keys.KEY_F24: 'kf24',
    _keys.KEY_F25: 'kf25',
    _keys.KEY_F26: 'kf26',
    _keys.KEY_F27: 'kf27',
    _keys.KEY_F28: 'kf28',
    _keys.KEY_F29: 'kf29',
    _keys.KEY_F3: 'kf3',
    _keys.KEY_F30: 'kf30',
    _keys.KEY_F31: 'kf31',
    _keys.KEY_F32: 'kf32',
    _keys.KEY_F33: 'kf33',
    _keys.KEY_F34: 'kf34',
    _keys.KEY_F35: 'kf35',
    _keys.KEY_F36: 'kf36',
    _keys.KEY_F37: 'kf37',
    _keys.KEY_F38: 'kf38',
    _keys.KEY_F39: 'kf39',
    _keys.KEY_F4: 'kf4',
    _keys.KEY_F40: 'kf40',
    _keys.KEY_F41: 'kf41',
    _keys.KEY_F42: 'kf42',
    _keys.KEY_F43: 'kf43',
    _keys.KEY_F44: 'kf44',
    _keys.KEY_F45: 'kf45',
    _keys.KEY_F46: 'kf46',
    _keys.KEY_F47: 'kf47',
    _keys.KEY_F48: 'kf48',
    _keys.KEY_F49: 'kf49',
    _keys.KEY_F5: 'kf5',
    _keys.KEY_F50: 'kf50',
    _keys.KEY_F51: 'kf51',
    _keys.KEY_F52: 'kf52',
    _keys.KEY_F53: 'kf53',
    _keys.KEY_F54: 'kf54',
    _keys.KEY_F55: 'kf55',
    _keys.KEY_F56: 'kf56',
    _keys.KEY_F57: 'kf57',
    _keys.KEY_F58: 'kf58',
    _keys.KEY_F59: 'kf59',
    _keys.KEY_F6: 'kf6',
    _keys.KEY_F60: 'kf60',
    _keys.KEY_F61: 'kf61',
    _keys.KEY_F62: 'kf62',
    _keys.KEY_F63: 'kf63',
    _keys.KEY_F7: 'kf7',
    _keys.KEY_F8: 'kf8',
    _keys.KEY_F9: 'kf9',
    _keys.KEY_FIND: 'kfnd',
    _keys.KEY_HELP: 'khlp',
    _keys.KEY_HOME: 'khome',
    _keys.KEY_IC: 'kich1',
    _keys.KEY_IL: 'kil1',
    _keys.KEY_LEFT: 'kcub1',
    _keys.KEY_LL: 'kll',
    _keys.KEY_MARK: 'kmrk',
    _keys.KEY_MESSAGE: 'kmsg',
    _keys.KEY_MOVE: 'kmov',
    _keys.KEY_NEXT: 'knxt',
    _keys.KEY_NPAGE: 'knp',
    _keys.KEY_OPEN: 'kopn',
    _keys.KEY_OPTIONS: 'kopt',
    _keys.KEY_PPAGE: 'kpp',
    _keys.KEY_PREVIOUS: 'kprv',
    _keys.KEY_PRINT: 'kprt',
    _keys.KEY_REDO: 'krdo',
    _keys.KEY_REFERENCE: 'kref',
    _keys.KEY_REFRESH: 'krfr',
    _keys.KEY_REPLACE: 'krpl',
    _keys.KEY_RESTART: 'krst',
    _keys.KEY_RESUME: 'kres',
    _keys.KEY_RIGHT: 'kcuf1',
    _keys.KEY_SAVE: 'ksav',
    _keys.KEY_SBEG: 'kBEG',
    _keys.KEY_SCANCEL: 'kCAN',
    _keys.KEY_SCOMMAND: 'kCMD',
    _keys.KEY_SCOPY: 'kCPY',
    _keys.KEY_SCREATE: 'kCRT',
    _keys.KEY_SDC: 'kDC',
    _keys.KEY_SDL: 'kDL',
    _keys.KEY_SELECT: 'kslt',
    _keys.KEY_SEND: 'kEND',
    _keys.KEY_SEOL: 'kEOL',
    _keys.KEY_SEXIT: 'kEXT',
    _keys.KEY_SF: 'kind',
    _keys.KEY_SFIND: 'kFND',
    _keys.KEY_SHELP: 'kHLP',
    _keys.KEY_SHOME: 'kHOM',
    _keys.KEY_SIC: 'kIC',
    _keys.KEY_SLEFT: 'kLFT',
    _keys.KEY_SMESSAGE: 'kMSG',
    _keys.KEY_SMOVE: 'kMOV',
    _keys.KEY_SNEXT: 'kNXT',
    _keys.KEY_SOPTIONS: 'kOPT',
    _keys.KEY_SPREVIOUS: 'kPRV',
    _keys.KEY_SPRINT: 'kPRT',
    _keys.KEY_SR: 'kri',
    _keys.KEY_SREDO: 'kRDO',
    _keys.KEY_SREPLACE: 'kRPL',
    _keys.KEY_SRIGHT: 'kRIT',
    _keys.KEY_SRSUME: 'kRES',
    _keys.KEY_SSAVE: 'kSAV',
    _keys.KEY_SSUSPEND: 'kSPD',
    _keys.KEY_STAB: 'khts',
    _keys.KEY_SUNDO: 'kUND',
    _keys.KEY_SUSPEND: 'kspd',
    _keys.KEY_UNDO: 'kund',
    _keys.KEY_UP: 'kcuu1'
    }
