# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g. crates.io) dependencies
#
# If you believe there's an error in this file please file an
# issue against the rust-lang/cargo repository. If you're
# editing this file be aware that the upstream Cargo.toml
# will likely look very different (and much more reasonable)

[package]
name = "id-arena"
version = "2.2.1"
authors = ["<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>"]
description = "A simple, id-based arena."
documentation = "https://docs.rs/id-arena"
readme = "README.md"
categories = ["memory-management", "rust-patterns", "no-std"]
license = "MIT/Apache-2.0"
repository = "https://github.com/fitzgen/id-arena"
[package.metadata.docs.rs]
features = ["rayon"]
[dependencies.rayon]
version = "1.0.3"
optional = true

[features]
default = ["std"]
std = []
