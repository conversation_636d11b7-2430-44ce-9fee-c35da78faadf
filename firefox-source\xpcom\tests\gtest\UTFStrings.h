/* -*- Mode: C++; tab-width: 8; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* vim: set ts=8 sts=2 et sw=2 tw=80: */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#ifndef utfstrings_h__
#define utfstrings_h__

struct UTFStringsStringPair {
  char16_t m16[16];
  char m8[16];
};

static const UTFStringsStringPair ValidStrings[] = {
    {{'a', 'b', 'c', 'd'}, {'a', 'b', 'c', 'd'}},
    {{'1', '2', '3', '4'}, {'1', '2', '3', '4'}},
    {{0x7F, 'A', 0x80, 'B', 0x101, 0x200},
     {0x7F, 'A', char(0xC2), char(0x80), 'B', char(0xC4), char(0x81),
      char(0xC8), char(0x80)}},
    {{0x7FF, 0x800, 0x1000},
     {char(0xDF), char(0xBF), char(0xE0), char(0xA0), char(0x80), char(0xE1),
      char(0x80), char(0x80)}},
    {{0xD7FF, 0xE000, 0xF00F, 'A', 0xFFF0},
     {char(0xED), char(0x9F), char(0xBF), char(0xEE), char(0x80), char(0x80),
      char(0xEF), char(0x80), char(0x8F), 'A', char(0xEF), char(0xBF),
      char(0xB0)}},
    {{0xFFF7, 0xFFFC, 0xFFFD, 0xFFFD},
     {char(0xEF), char(0xBF), char(0xB7), char(0xEF), char(0xBF), char(0xBC),
      char(0xEF), char(0xBF), char(0xBD), char(0xEF), char(0xBF), char(0xBD)}},
    {{0xD800, 0xDC00, 0xD800, 0xDCFF},
     {char(0xF0), char(0x90), char(0x80), char(0x80), char(0xF0), char(0x90),
      char(0x83), char(0xBF)}},
    {{0xDBFF, 0xDFFF, 0xDBB7, 0xDCBA},
     {char(0xF4), char(0x8F), char(0xBF), char(0xBF), char(0xF3), char(0xBD),
      char(0xB2), char(0xBA)}},
    {{0xFFFD, 0xFFFF},
     {char(0xEF), char(0xBF), char(0xBD), char(0xEF), char(0xBF), char(0xBF)}},
    {{0xFFFD, 0xFFFE, 0xFFFF},
     {char(0xEF), char(0xBF), char(0xBD), char(0xEF), char(0xBF), char(0xBE),
      char(0xEF), char(0xBF), char(0xBF)}},
};

static const UTFStringsStringPair Invalid16Strings[] = {
    {{'a', 'b', 0xD800}, {'a', 'b', char(0xEF), char(0xBF), char(0xBD)}},
    {{0xD8FF, 'b'}, {char(0xEF), char(0xBF), char(0xBD), 'b'}},
    {{0xD821}, {char(0xEF), char(0xBF), char(0xBD)}},
    {{0xDC21}, {char(0xEF), char(0xBF), char(0xBD)}},
    {{0xDC00, 0xD800, 'b'},
     {char(0xEF), char(0xBF), char(0xBD), char(0xEF), char(0xBF), char(0xBD),
      'b'}},
    {{'b', 0xDC00, 0xD800},
     {'b', char(0xEF), char(0xBF), char(0xBD), char(0xEF), char(0xBF),
      char(0xBD)}},
    {{0xDC00, 0xD800},
     {char(0xEF), char(0xBF), char(0xBD), char(0xEF), char(0xBF), char(0xBD)}},
    {{0xDC00, 0xD800, 0xDC00, 0xD800},
     {char(0xEF), char(0xBF), char(0xBD), char(0xF0), char(0x90), char(0x80),
      char(0x80), char(0xEF), char(0xBF), char(0xBD)}},
    {{0xDC00, 0xD800, 0xD800, 0xDC00},
     {char(0xEF), char(0xBF), char(0xBD), char(0xEF), char(0xBF), char(0xBD),
      char(0xF0), char(0x90), char(0x80), char(0x80)}},
};

static const UTFStringsStringPair Invalid8Strings[] = {
    {{'a', 0xFFFD, 0xFFFD, 'b'}, {'a', char(0xC0), char(0x80), 'b'}},
    {{0xFFFD, 0xFFFD, 0x80}, {char(0xC1), char(0xBF), char(0xC2), char(0x80)}},
    {{0xFFFD, 0xFFFD}, {char(0xC1), char(0xBF)}},
    {{0xFFFD, 0xFFFD, 0xFFFD, 'x', 0x0800},
     {char(0xE0), char(0x80), char(0x80), 'x', char(0xE0), char(0xA0),
      char(0x80)}},
    {{0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 'x', 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD},
     {char(0xF0), char(0x80), char(0x80), char(0x80), 'x', char(0xF0),
      char(0x80), char(0x8F), char(0x80)}},
    {{0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD},
     {char(0xF4), char(0x90), char(0x80), char(0x80), char(0xF7), char(0xBF),
      char(0xBF), char(0xBF)}},
    {{0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 'x', 0xD800, 0xDC00, 0xFFFD, 0xFFFD,
      0xFFFD, 0xFFFD},
     {char(0xF0), char(0x8F), char(0xBF), char(0xBF), 'x', char(0xF0),
      char(0x90), char(0x80), char(0x80), char(0xF0), char(0x8F), char(0xBF),
      char(0xBF)}},
    {{0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 'x', 0xFFFD, 0xFFFD, 0xFFFD,
      0xFFFD, 0xFFFD},
     {char(0xF8), char(0x80), char(0x80), char(0x80), char(0x80), 'x',
      char(0xF8), char(0x88), char(0x80), char(0x80), char(0x80)}},
    {{0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD,
      0xFFFD, 0xFFFD},
     {char(0xFB), char(0xBF), char(0xBF), char(0xBF), char(0xBF), char(0xFC),
      char(0xA0), char(0x80), char(0x80), char(0x80), char(0x80)}},
    {{0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD,
      0xFFFD, 0xFFFD, 0xFFFD},
     {char(0xFC), char(0x80), char(0x80), char(0x80), char(0x80), char(0x80),
      char(0xFD), char(0xBF), char(0xBF), char(0xBF), char(0xBF), char(0xBF)}},
};

static const UTFStringsStringPair Malformed8Strings[] = {
    {{0xFFFD}, {char(0x80)}},
    {{'a', 0xFFFD, 'c'}, {'a', char(0xC8), 'c'}},
    {{'a', 0xFFFD}, {'a', char(0xC8)}},
    {{'a', 0xFFFD, 'c'}, {'a', char(0xE8), 'c'}},
    {{'a', 0xFFFD, 'c'}, {'a', char(0xE8), char(0x80), 'c'}},
    {{'a', 0xFFFD}, {'a', char(0xE8), char(0x80)}},
    {{0xFFFD, 0x7F, 0xFFFD}, {char(0xE8), 0x7F, char(0x80)}},
    {{'a', 0xFFFD, 0xFFFD}, {'a', char(0xE8), char(0xE8), char(0x80)}},
    {{'a', 0xFFFD}, {'a', char(0xF4)}},
    {{'a', 0xFFFD, 'c', 'c'},
     {'a', char(0xF4), char(0x80), char(0x80), 'c', 'c'}},
    {{'a', 0xFFFD, 'x', 0xFFFD},
     {'a', char(0xF4), char(0x80), 'x', char(0x80)}},
    {{0xDBC0, 0xDC00, 0xFFFD},
     {char(0xF4), char(0x80), char(0x80), char(0x80), char(0x80)}},
    {{'a', 0xFFFD, 'c'}, {'a', char(0xFA), 'c'}},
    {{'a', 0xFFFD, 0xFFFD, 0xFFFD, 0x7F, 0xFFFD, 'c'},
     {'a', char(0xFA), char(0x80), char(0x80), 0x7F, char(0x80), 'c'}},
    {{'a', 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 'c'},
     {'a', char(0xFA), char(0x80), char(0x80), char(0x80), char(0x80),
      char(0x80), 'c'}},
    {{'a', 0xFFFD}, {'a', char(0xFD)}},
    {{'a', 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 'c'},
     {'a', char(0xFD), char(0x80), char(0x80), char(0x80), char(0x80), 'c'}},
    {{'a', 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD, 0xFFFD},
     {'a', char(0xFD), char(0x80), char(0x80), char(0x80), char(0x80),
      char(0x80), char(0x80)}},
    {{'a', 0xFFFD, 0xFFFD, 0xFFFD, 0x40, 0xFFFD, 0xFFFD, 'c'},
     {'a', char(0xFD), char(0x80), char(0x80), 0x40, char(0x80), char(0x80),
      'c'}},
};

#endif
