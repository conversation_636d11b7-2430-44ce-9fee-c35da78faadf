Metadata-Version: 2.4
Name: mozilla-taskgraph
Version: 3.3.2
Summary: Mozilla specific transforms and utilities for Taskgraph
Project-URL: Repository, https://github.com/mozilla-releng/mozilla-taskgraph
Project-URL: Issues, https://github.com/mozilla-releng/mozilla-taskgraph/issues
Author-email: Mozilla Release Engineering <<EMAIL>>
License-File: LICENSE
Classifier: Development Status :: 4 - Beta
Classifier: Environment :: Console
Classifier: License :: OSI Approved :: Mozilla Public License 2.0 (MPL 2.0)
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Software Development
Requires-Python: >=3.8
Requires-Dist: taskcluster-taskgraph<15,>=11
Description-Content-Type: text/markdown

[![Task Status](https://firefox-ci-tc.services.mozilla.com/api/github/v1/repository/mozilla-releng/mozilla-taskgraph/main/badge.svg)](https://firefox-ci-tc.services.mozilla.com/api/github/v1/repository/mozilla-releng/mozilla-taskgraph/main/latest)
[![pre-commit.ci status](https://results.pre-commit.ci/badge/github/mozilla-releng/mozilla-taskgraph/main.svg)](https://results.pre-commit.ci/latest/github/taskcluster/taskgraph/main)
[![Code Coverage](https://codecov.io/gh/mozilla-releng/mozilla-taskgraph/branch/main/graph/badge.svg?token=GJIV52ZQNP)](https://codecov.io/gh/mozilla-releng/mozilla-taskgraph)
[![Pypi Version](https://badge.fury.io/py/mozilla-taskgraph.svg)](https://badge.fury.io/py/mozilla-taskgraph)
[![Documentation Status](https://readthedocs.org/projects/mozilla-taskgraph/badge/?version=latest)](https://mozilla-taskgraph.readthedocs.io/en/latest/?badge=latest)
[![License](https://img.shields.io/badge/license-MPL%202.0-orange.svg)](http://mozilla.org/MPL/2.0)

# mozilla-taskgraph
Mozilla-specific transforms and utilities for Taskgraph
