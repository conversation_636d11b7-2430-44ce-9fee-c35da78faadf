/* generated file - DO NOT EDIT */

/* includes 247 stub entries, and 5 sentinel entries */

/*
*  declarations of normal stubs...
*  0 is QueryInterface
*  1 is AddRef
*  2 is Release
*/
#if !defined(__ia64) || (!defined(__hpux) && !defined(__linux__) && !defined(__FreeBSD__))
NS_IMETHOD Stub3();
NS_IMETHOD Stub4();
NS_IMETHOD Stub5();
NS_IMETHOD Stub6();
NS_IMETHOD Stub7();
NS_IMETHOD Stub8();
NS_IMETHOD Stub9();
NS_IMETHOD Stub10();
NS_IMETHOD Stub11();
NS_IMETHOD Stub12();
NS_IMETHOD Stub13();
NS_IMETHOD Stub14();
NS_IMETHOD Stub15();
NS_IMETHOD Stub16();
NS_IMETHOD Stub17();
NS_IMETHOD Stub18();
NS_IMETHOD Stub19();
NS_IMETHOD Stub20();
NS_IMETHOD Stub21();
NS_IMETHOD Stub22();
NS_IMETHOD Stub23();
NS_IMETHOD Stub24();
NS_IMETHOD Stub25();
NS_IMETHOD Stub26();
NS_IMETHOD Stub27();
NS_IMETHOD Stub28();
NS_IMETHOD Stub29();
NS_IMETHOD Stub30();
NS_IMETHOD Stub31();
NS_IMETHOD Stub32();
NS_IMETHOD Stub33();
NS_IMETHOD Stub34();
NS_IMETHOD Stub35();
NS_IMETHOD Stub36();
NS_IMETHOD Stub37();
NS_IMETHOD Stub38();
NS_IMETHOD Stub39();
NS_IMETHOD Stub40();
NS_IMETHOD Stub41();
NS_IMETHOD Stub42();
NS_IMETHOD Stub43();
NS_IMETHOD Stub44();
NS_IMETHOD Stub45();
NS_IMETHOD Stub46();
NS_IMETHOD Stub47();
NS_IMETHOD Stub48();
NS_IMETHOD Stub49();
NS_IMETHOD Stub50();
NS_IMETHOD Stub51();
NS_IMETHOD Stub52();
NS_IMETHOD Stub53();
NS_IMETHOD Stub54();
NS_IMETHOD Stub55();
NS_IMETHOD Stub56();
NS_IMETHOD Stub57();
NS_IMETHOD Stub58();
NS_IMETHOD Stub59();
NS_IMETHOD Stub60();
NS_IMETHOD Stub61();
NS_IMETHOD Stub62();
NS_IMETHOD Stub63();
NS_IMETHOD Stub64();
NS_IMETHOD Stub65();
NS_IMETHOD Stub66();
NS_IMETHOD Stub67();
NS_IMETHOD Stub68();
NS_IMETHOD Stub69();
NS_IMETHOD Stub70();
NS_IMETHOD Stub71();
NS_IMETHOD Stub72();
NS_IMETHOD Stub73();
NS_IMETHOD Stub74();
NS_IMETHOD Stub75();
NS_IMETHOD Stub76();
NS_IMETHOD Stub77();
NS_IMETHOD Stub78();
NS_IMETHOD Stub79();
NS_IMETHOD Stub80();
NS_IMETHOD Stub81();
NS_IMETHOD Stub82();
NS_IMETHOD Stub83();
NS_IMETHOD Stub84();
NS_IMETHOD Stub85();
NS_IMETHOD Stub86();
NS_IMETHOD Stub87();
NS_IMETHOD Stub88();
NS_IMETHOD Stub89();
NS_IMETHOD Stub90();
NS_IMETHOD Stub91();
NS_IMETHOD Stub92();
NS_IMETHOD Stub93();
NS_IMETHOD Stub94();
NS_IMETHOD Stub95();
NS_IMETHOD Stub96();
NS_IMETHOD Stub97();
NS_IMETHOD Stub98();
NS_IMETHOD Stub99();
NS_IMETHOD Stub100();
NS_IMETHOD Stub101();
NS_IMETHOD Stub102();
NS_IMETHOD Stub103();
NS_IMETHOD Stub104();
NS_IMETHOD Stub105();
NS_IMETHOD Stub106();
NS_IMETHOD Stub107();
NS_IMETHOD Stub108();
NS_IMETHOD Stub109();
NS_IMETHOD Stub110();
NS_IMETHOD Stub111();
NS_IMETHOD Stub112();
NS_IMETHOD Stub113();
NS_IMETHOD Stub114();
NS_IMETHOD Stub115();
NS_IMETHOD Stub116();
NS_IMETHOD Stub117();
NS_IMETHOD Stub118();
NS_IMETHOD Stub119();
NS_IMETHOD Stub120();
NS_IMETHOD Stub121();
NS_IMETHOD Stub122();
NS_IMETHOD Stub123();
NS_IMETHOD Stub124();
NS_IMETHOD Stub125();
NS_IMETHOD Stub126();
NS_IMETHOD Stub127();
NS_IMETHOD Stub128();
NS_IMETHOD Stub129();
NS_IMETHOD Stub130();
NS_IMETHOD Stub131();
NS_IMETHOD Stub132();
NS_IMETHOD Stub133();
NS_IMETHOD Stub134();
NS_IMETHOD Stub135();
NS_IMETHOD Stub136();
NS_IMETHOD Stub137();
NS_IMETHOD Stub138();
NS_IMETHOD Stub139();
NS_IMETHOD Stub140();
NS_IMETHOD Stub141();
NS_IMETHOD Stub142();
NS_IMETHOD Stub143();
NS_IMETHOD Stub144();
NS_IMETHOD Stub145();
NS_IMETHOD Stub146();
NS_IMETHOD Stub147();
NS_IMETHOD Stub148();
NS_IMETHOD Stub149();
NS_IMETHOD Stub150();
NS_IMETHOD Stub151();
NS_IMETHOD Stub152();
NS_IMETHOD Stub153();
NS_IMETHOD Stub154();
NS_IMETHOD Stub155();
NS_IMETHOD Stub156();
NS_IMETHOD Stub157();
NS_IMETHOD Stub158();
NS_IMETHOD Stub159();
NS_IMETHOD Stub160();
NS_IMETHOD Stub161();
NS_IMETHOD Stub162();
NS_IMETHOD Stub163();
NS_IMETHOD Stub164();
NS_IMETHOD Stub165();
NS_IMETHOD Stub166();
NS_IMETHOD Stub167();
NS_IMETHOD Stub168();
NS_IMETHOD Stub169();
NS_IMETHOD Stub170();
NS_IMETHOD Stub171();
NS_IMETHOD Stub172();
NS_IMETHOD Stub173();
NS_IMETHOD Stub174();
NS_IMETHOD Stub175();
NS_IMETHOD Stub176();
NS_IMETHOD Stub177();
NS_IMETHOD Stub178();
NS_IMETHOD Stub179();
NS_IMETHOD Stub180();
NS_IMETHOD Stub181();
NS_IMETHOD Stub182();
NS_IMETHOD Stub183();
NS_IMETHOD Stub184();
NS_IMETHOD Stub185();
NS_IMETHOD Stub186();
NS_IMETHOD Stub187();
NS_IMETHOD Stub188();
NS_IMETHOD Stub189();
NS_IMETHOD Stub190();
NS_IMETHOD Stub191();
NS_IMETHOD Stub192();
NS_IMETHOD Stub193();
NS_IMETHOD Stub194();
NS_IMETHOD Stub195();
NS_IMETHOD Stub196();
NS_IMETHOD Stub197();
NS_IMETHOD Stub198();
NS_IMETHOD Stub199();
NS_IMETHOD Stub200();
NS_IMETHOD Stub201();
NS_IMETHOD Stub202();
NS_IMETHOD Stub203();
NS_IMETHOD Stub204();
NS_IMETHOD Stub205();
NS_IMETHOD Stub206();
NS_IMETHOD Stub207();
NS_IMETHOD Stub208();
NS_IMETHOD Stub209();
NS_IMETHOD Stub210();
NS_IMETHOD Stub211();
NS_IMETHOD Stub212();
NS_IMETHOD Stub213();
NS_IMETHOD Stub214();
NS_IMETHOD Stub215();
NS_IMETHOD Stub216();
NS_IMETHOD Stub217();
NS_IMETHOD Stub218();
NS_IMETHOD Stub219();
NS_IMETHOD Stub220();
NS_IMETHOD Stub221();
NS_IMETHOD Stub222();
NS_IMETHOD Stub223();
NS_IMETHOD Stub224();
NS_IMETHOD Stub225();
NS_IMETHOD Stub226();
NS_IMETHOD Stub227();
NS_IMETHOD Stub228();
NS_IMETHOD Stub229();
NS_IMETHOD Stub230();
NS_IMETHOD Stub231();
NS_IMETHOD Stub232();
NS_IMETHOD Stub233();
NS_IMETHOD Stub234();
NS_IMETHOD Stub235();
NS_IMETHOD Stub236();
NS_IMETHOD Stub237();
NS_IMETHOD Stub238();
NS_IMETHOD Stub239();
NS_IMETHOD Stub240();
NS_IMETHOD Stub241();
NS_IMETHOD Stub242();
NS_IMETHOD Stub243();
NS_IMETHOD Stub244();
NS_IMETHOD Stub245();
NS_IMETHOD Stub246();
NS_IMETHOD Stub247();
NS_IMETHOD Stub248();
NS_IMETHOD Stub249();
#else
NS_IMETHOD Stub3(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub4(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub5(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub6(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub7(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub8(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub9(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub10(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub11(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub12(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub13(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub14(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub15(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub16(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub17(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub18(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub19(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub20(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub21(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub22(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub23(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub24(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub25(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub26(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub27(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub28(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub29(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub30(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub31(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub32(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub33(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub34(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub35(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub36(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub37(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub38(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub39(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub40(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub41(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub42(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub43(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub44(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub45(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub46(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub47(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub48(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub49(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub50(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub51(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub52(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub53(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub54(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub55(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub56(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub57(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub58(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub59(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub60(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub61(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub62(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub63(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub64(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub65(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub66(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub67(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub68(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub69(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub70(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub71(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub72(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub73(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub74(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub75(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub76(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub77(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub78(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub79(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub80(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub81(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub82(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub83(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub84(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub85(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub86(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub87(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub88(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub89(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub90(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub91(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub92(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub93(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub94(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub95(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub96(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub97(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub98(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub99(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub100(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub101(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub102(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub103(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub104(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub105(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub106(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub107(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub108(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub109(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub110(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub111(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub112(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub113(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub114(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub115(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub116(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub117(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub118(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub119(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub120(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub121(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub122(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub123(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub124(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub125(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub126(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub127(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub128(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub129(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub130(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub131(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub132(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub133(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub134(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub135(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub136(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub137(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub138(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub139(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub140(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub141(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub142(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub143(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub144(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub145(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub146(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub147(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub148(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub149(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub150(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub151(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub152(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub153(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub154(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub155(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub156(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub157(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub158(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub159(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub160(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub161(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub162(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub163(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub164(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub165(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub166(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub167(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub168(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub169(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub170(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub171(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub172(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub173(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub174(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub175(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub176(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub177(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub178(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub179(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub180(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub181(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub182(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub183(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub184(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub185(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub186(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub187(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub188(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub189(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub190(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub191(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub192(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub193(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub194(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub195(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub196(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub197(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub198(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub199(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub200(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub201(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub202(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub203(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub204(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub205(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub206(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub207(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub208(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub209(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub210(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub211(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub212(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub213(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub214(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub215(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub216(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub217(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub218(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub219(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub220(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub221(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub222(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub223(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub224(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub225(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub226(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub227(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub228(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub229(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub230(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub231(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub232(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub233(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub234(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub235(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub236(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub237(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub238(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub239(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub240(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub241(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub242(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub243(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub244(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub245(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub246(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub247(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub248(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
NS_IMETHOD Stub249(uint64_t,uint64_t,
 uint64_t,uint64_t,uint64_t,uint64_t,uint64_t,uint64_t);
#endif

/* declarations of sentinel stubs */
NS_IMETHOD Sentinel0();
NS_IMETHOD Sentinel1();
NS_IMETHOD Sentinel2();
NS_IMETHOD Sentinel3();
NS_IMETHOD Sentinel4();
