# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
name = "leb128"
version = "0.2.5"
authors = ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"]
exclude = ["/.travis.yml", "/.coveralls.yml", "/format"]
description = "Read and write DWARF's \"Little Endian Base 128\" (LEB128) variable length integer encoding."
documentation = "https://docs.rs/leb128"
readme = "./README.md"
keywords = ["LEB128", "DWARF", "variable", "length", "encoding"]
license = "Apache-2.0/MIT"
repository = "https://github.com/gimli-rs/leb128"

[dependencies]
[dev-dependencies.quickcheck]
version = "0.8.0"

[features]
nightly = []
