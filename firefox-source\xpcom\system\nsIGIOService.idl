/* -*- Mode: IDL; tab-width: 2; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

#include "nsISupports.idl"
#include "nsIMIMEInfo.idl"

interface nsIUTF8StringEnumerator;
interface nsIURI;
interface nsIFile;
interface nsIMutableArray;

[scriptable, uuid(eb5a6937-addc-4d5d-85e6-f11798f90a0a)]
interface nsIGIOHandlerApp : nsIHandlerApp
{
  readonly attribute AUTF8String id;
  /* Launch files */
  void launchFile(in AUTF8String fileName);
  AUTF8String getMozIconURL();
};

/* nsIGIOMimeApp holds information about an application that is looked up
   with nsIGIOService::GetAppForMimeType. */
// *************-405b-9321-bf30420e34e6 prev uuid

[scriptable, uuid(ca6bad0c-8a48-48ac-82c7-27bb8f510fbe)]
interface nsIGIOMimeApp : nsIHandlerApp
{
  const long EXPECTS_URIS  = 0;
  const long EXPECTS_PATHS = 1;
  const long EXPECTS_URIS_FOR_NON_FILES = 2;

  readonly attribute AUTF8String         id;
  readonly attribute AUTF8String         command;
  readonly attribute long                expectsURIs;  // see constants above
  readonly attribute nsIUTF8StringEnumerator supportedURISchemes;

  void setAsDefaultForMimeType(in AUTF8String mimeType);
  void setAsDefaultForFileExtensions(in AUTF8String extensions);
  void setAsDefaultForURIScheme(in AUTF8String uriScheme);
};

/*
 * The VFS service makes use of two distinct registries.
 *
 * The application registry holds information about applications (uniquely
 * identified by id), such as which MIME types and URI schemes they are
 * capable of handling, whether they run in a terminal, etc.
 *
 * The MIME registry holds information about MIME types, such as which
 * extensions map to a given MIME type.  The MIME registry also stores the
 * id of the application selected to handle each MIME type.
 */

// prev id eda22a30-84e1-4e16-9ca0-cd1553c2b34a
[scriptable, builtinclass, uuid(51583703-cafc-4769-9ff1-edb349151930)]
interface nsIGIOService : nsISupports
{

  /*** MIME registry methods ***/

  /* Obtain the MIME type registered for an extension.  The extension
     should not include a leading dot. */
  AUTF8String        getMimeTypeFromExtension(in AUTF8String extension);

  /* Obtain the preferred application for opening a given URI scheme */
  nsIHandlerApp      getAppForURIScheme(in AUTF8String aURIScheme);

  /* Obtain list of application capable of opening given URI scheme */
  nsIMutableArray    getAppsForURIScheme(in AUTF8String aURIScheme);

  /* Obtain the preferred application for opening a given MIME type */
  nsIHandlerApp      getAppForMimeType(in AUTF8String mimeType);

  /* Create application info from given application desktop filename */
  nsIGIOHandlerApp   createHandlerAppFromAppId(in string appId);

  /* Create application info for given command and name */
  nsIGIOMimeApp      createAppFromCommand(in AUTF8String cmd,
                                          in AUTF8String appName);

  /* Find the application info by given command */
  nsIGIOMimeApp      findAppFromCommand(in AUTF8String cmd);

  /* Obtain a description for the given MIME type */
  AUTF8String        getDescriptionForMimeType(in AUTF8String mimeType);

  /*** Misc. methods ***/
  [infallible] readonly attribute boolean isRunningUnderFlatpak;
  [infallible] readonly attribute boolean isRunningUnderSnap;

  /* Open the given URI in the default application */
  [noscript] void showURI(in nsIURI uri);
  [noscript] void revealFile(in nsIFile file);
  [noscript] void launchFile(in ACString path);
};

%{C++
#define NS_GIOSERVICE_CONTRACTID "@mozilla.org/gio-service;1"
%}
