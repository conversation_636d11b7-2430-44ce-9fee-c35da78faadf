# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.63"
name = "jobserver"
version = "0.1.33"
authors = ["<PERSON> <<EMAIL>>"]
build = false
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = """
An implementation of the GNU Make jobserver for Rust.
"""
homepage = "https://github.com/rust-lang/jobserver-rs"
documentation = "https://docs.rs/jobserver"
readme = "README.md"
license = "MIT OR Apache-2.0"
repository = "https://github.com/rust-lang/jobserver-rs"

[lib]
name = "jobserver"
path = "src/lib.rs"

[[test]]
name = "client"
path = "tests/client.rs"
harness = false

[[test]]
name = "client-of-myself"
path = "tests/client-of-myself.rs"
harness = false

[[test]]
name = "helper"
path = "tests/helper.rs"

[[test]]
name = "make-as-a-client"
path = "tests/make-as-a-client.rs"
harness = false

[[test]]
name = "server"
path = "tests/server.rs"

[dev-dependencies.tempfile]
version = "3.10.1"

[target."cfg(unix)".dependencies.libc]
version = "0.2.171"

[target."cfg(unix)".dev-dependencies.nix]
version = "0.28.0"
features = ["fs"]

[target."cfg(windows)".dependencies.getrandom]
version = "0.3.2"
features = ["std"]
