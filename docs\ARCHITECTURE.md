# Megisto Browser - Architecture Documentation

## Overview

Megisto Browser is a Firefox fork that provides enhanced privacy and media features through a system add-on architecture. This document describes the technical architecture and implementation details.

## Core Components

### 1. Firefox Base
- **Source**: Mozilla Firefox/Gecko engine
- **Version**: Based on Firefox ESR or latest stable
- **Modifications**: Minimal changes to core browser, most functionality via system add-on

### 2. System Add-on (Megisto Core)
- **Location**: `browser/extensions/megisto-addon/`
- **Type**: WebExtension with XPCOM components
- **Purpose**: Provides cookie blocking and YouTube enhancement features

### 3. Build System
- **Configuration**: Custom `mozconfig` file
- **Branding**: Custom Megisto branding
- **Output**: Standalone browser executable

## System Add-on Architecture

```
Megisto System Add-on
├── WebExtension Layer
│   ├── Background Script (background.js)
│   ├── Content Scripts
│   │   ├── <PERSON>ie Blocker (cookieblocker.js)
│   │   └── Video Manager (videomanager.js)
│   └── Web Accessible Resources
├── XPCOM Components
│   └── MegistoService.js
├── Chrome Package
│   ├── Preferences UI (XUL/JS)
│   ├── Localization (DTD)
│   └── Styling (CSS)
└── Bootstrap Script (bootstrap.js)
```

## Feature Implementation

### Cookie Banner Blocking

#### Architecture
1. **Rule Engine**: JSON-based rules for different websites
2. **Content Script**: Detects and removes cookie banners
3. **Background Script**: Manages rules and statistics
4. **XPCOM Service**: System-level request blocking

#### Flow
```
Page Load → Content Script Injection → Banner Detection → Rule Application → Banner Removal
```

#### Components
- **Rules**: `rules/default-rules.json`
- **Content Script**: `content/cookieblocker.js`
- **Background**: `background.js`
- **Service**: `components/MegistoService.js`

### YouTube Enhancement

#### Architecture
1. **Embed Detection**: Identifies YouTube iframes
2. **Player Replacement**: Replaces with Video.js player
3. **API Integration**: Uses YouTube IFrame API
4. **Preference Management**: User-configurable options

#### Flow
```
Page Load → Iframe Detection → Video.js Loading → Player Replacement → Enhanced Controls
```

#### Components
- **Content Script**: `content/videomanager.js`
- **Video.js**: External CDN or bundled
- **YouTube Plugin**: videojs-youtube plugin
- **Preferences**: User settings integration

## Data Flow

### 1. Initialization
```
Browser Start → Bootstrap Script → XPCOM Registration → WebExtension Load → Content Script Injection
```

### 2. Cookie Blocking
```
HTTP Request → Service Intercept → Rule Check → Block/Allow → Statistics Update
DOM Change → Content Script → Banner Detection → Removal → Statistics Update
```

### 3. YouTube Enhancement
```
Page Load → Content Script → Iframe Detection → Video.js Load → Player Replace → User Interaction
```

## File Structure

```
browser/extensions/megisto-addon/
├── manifest.json              # WebExtension manifest
├── install.rdf               # Legacy add-on manifest
├── bootstrap.js              # Bootstrap script
├── chrome.manifest           # Chrome registration
├── background.js             # Background script
├── content/                  # Content scripts
│   ├── cookieblocker.js     # Cookie banner blocking
│   └── videomanager.js      # YouTube enhancement
├── rules/                    # Blocking rules
│   └── default-rules.json   # Default rule set
├── chrome/                   # Chrome package
│   ├── content/             # XUL/JS files
│   │   ├── preferences.xul  # Preferences dialog
│   │   └── preferences.js   # Preferences logic
│   ├── locale/              # Localization
│   │   └── en-US/
│   │       └── preferences.dtd
│   └── skin/                # Styling
└── components/              # XPCOM components
    └── MegistoService.js    # Core service
```

## Integration Points

### 1. Firefox Integration
- **Branding**: Custom branding in `browser/branding/megisto/`
- **Preferences**: Integration with Firefox preferences system
- **Build System**: Custom `mozconfig` configuration

### 2. System Add-on Integration
- **Auto-installation**: Bundled with browser
- **Privilege Level**: System-level permissions
- **Update Mechanism**: Independent of browser updates

### 3. WebExtension APIs
- **Storage**: Local storage for preferences and statistics
- **Tabs**: Tab management and content script injection
- **WebRequest**: HTTP request interception
- **Runtime**: Message passing between components

## Security Model

### 1. Permissions
- **System Add-on**: Full system privileges
- **Content Scripts**: Limited to page context
- **Background Script**: Extension context privileges

### 2. Isolation
- **Process Separation**: Content scripts run in content process
- **API Boundaries**: WebExtension API provides security boundaries
- **Privilege Escalation**: Controlled through XPCOM components

### 3. Updates
- **System Add-on**: Can be updated independently
- **Rule Updates**: Remote rule updates without browser restart
- **Security Patches**: Rapid deployment capability

## Performance Considerations

### 1. Content Script Injection
- **Timing**: Inject at `document_start` for early blocking
- **Scope**: All URLs for comprehensive coverage
- **Optimization**: Minimal script size and execution time

### 2. Rule Processing
- **Caching**: Rules cached in memory
- **Matching**: Efficient selector matching
- **Updates**: Incremental rule updates

### 3. Video.js Integration
- **Loading**: Lazy loading of Video.js resources
- **Caching**: CDN caching for external resources
- **Fallback**: Graceful fallback to original embeds

## Testing Strategy

### 1. Unit Tests
- **Content Scripts**: Isolated testing of blocking logic
- **Background Scripts**: API interaction testing
- **XPCOM Components**: Service functionality testing

### 2. Integration Tests
- **End-to-End**: Full browser testing with Playwright
- **Cross-Site**: Testing across different websites
- **Performance**: Load time and resource usage testing

### 3. Manual Testing
- **User Interface**: Preferences dialog testing
- **Feature Testing**: Cookie blocking and video enhancement
- **Compatibility**: Testing with various websites

## Deployment

### 1. Build Process
```
Source Code → Firefox Build → System Add-on Integration → Branding → Package → Installer
```

### 2. Distribution
- **Installer**: NSIS-based Windows installer
- **Portable**: Portable browser package
- **Updates**: Automatic update mechanism

### 3. Configuration
- **Default Settings**: Privacy-focused defaults
- **User Preferences**: Configurable through UI
- **Enterprise**: Group policy support (future)

## Maintenance

### 1. Firefox Updates
- **Base Updates**: Regular Firefox security updates
- **Compatibility**: Ensure add-on compatibility
- **Testing**: Regression testing after updates

### 2. Rule Updates
- **Community**: Community-contributed rules
- **Automation**: Automated rule testing
- **Distribution**: Remote rule distribution

### 3. Feature Development
- **Modular**: Add-on based feature development
- **Testing**: Comprehensive test coverage
- **Documentation**: Maintained architecture documentation

## Future Enhancements

### 1. Additional Blocking
- **Ad Blocking**: Integrated ad blocking
- **Tracker Blocking**: Enhanced tracker protection
- **Malware Protection**: Malicious site blocking

### 2. Media Enhancement
- **Multi-Platform**: Support for other video platforms
- **Audio Enhancement**: Audio player improvements
- **Streaming**: Live streaming enhancements

### 3. Privacy Features
- **VPN Integration**: Built-in VPN support
- **Tor Integration**: Tor network support
- **Encrypted DNS**: DNS over HTTPS/TLS
