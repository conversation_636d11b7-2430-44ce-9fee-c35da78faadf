/**
 * Megisto Browser - Global Test Teardown
 * Runs after all tests to clean up the environment
 */

const fs = require('fs');
const path = require('path');

async function globalTeardown(config) {
  console.log('🧹 Cleaning up after Megisto Browser tests...');
  
  // Log test results summary
  const testResultsDir = path.join(__dirname, 'test-results');
  
  if (fs.existsSync(testResultsDir)) {
    const resultsJsonPath = path.join(testResultsDir, 'results.json');
    
    if (fs.existsSync(resultsJsonPath)) {
      try {
        const results = JSON.parse(fs.readFileSync(resultsJsonPath, 'utf8'));
        
        console.log('📊 Test Results Summary:');
        console.log(`   - Total tests: ${results.stats?.total || 'unknown'}`);
        console.log(`   - Passed: ${results.stats?.passed || 'unknown'}`);
        console.log(`   - Failed: ${results.stats?.failed || 'unknown'}`);
        console.log(`   - Skipped: ${results.stats?.skipped || 'unknown'}`);
        console.log(`   - Duration: ${results.stats?.duration || 'unknown'}ms`);
        
        if (results.stats?.failed > 0) {
          console.log('❌ Some tests failed. Check the HTML report for details.');
        } else {
          console.log('✅ All tests passed!');
        }
      } catch (error) {
        console.warn('⚠️  Could not parse test results:', error.message);
      }
    }
    
    // Log report locations
    const htmlReportDir = path.join(testResultsDir, 'html-report');
    if (fs.existsSync(htmlReportDir)) {
      console.log(`📄 HTML Report: ${htmlReportDir}/index.html`);
    }
    
    const junitPath = path.join(testResultsDir, 'junit.xml');
    if (fs.existsSync(junitPath)) {
      console.log(`📄 JUnit Report: ${junitPath}`);
    }
  }
  
  // Clean up temporary files if needed
  // (Add any cleanup logic here)
  
  console.log('✅ Global teardown completed');
}

module.exports = globalTeardown;
