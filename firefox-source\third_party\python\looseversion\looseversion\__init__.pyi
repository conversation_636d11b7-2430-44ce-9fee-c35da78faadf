from re import Pattern
from typing import List, Union

class LooseVersion:
    component_re: Pattern[str]
    vstring: str
    version: List[Union[str, int]]
    def __init__(self, vstring: Union[str, None] = ...) -> None: ...
    def __eq__(self, other: object) -> bool: ...
    def __lt__(self, other: object) -> bool: ...
    def __le__(self, other: object) -> bool: ...
    def __gt__(self, other: object) -> bool: ...
    def __ge__(self, other: object) -> bool: ...
    def parse(self, vstring: str) -> None: ...

class LooseVersion2(LooseVersion): ...
