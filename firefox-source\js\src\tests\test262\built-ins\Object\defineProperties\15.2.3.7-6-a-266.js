// Copyright (c) 2012 Ecma International.  All rights reserved.
// This code is governed by the BSD license found in the LICENSE file.

/*---
es5id: ********-6-a-266
description: >
    Object.defineProperties - 'O' is an Array, 'P' is generic property
    that won't exist on 'O', and 'desc' is data descriptor, test 'P'
    is defined in 'O' with all correct attribute values (******** step
    5)
includes: [propertyHelper.js]
---*/


var arr = [];

Object.defineProperties(arr, {
  "property": {
    value: 12,
    writable: true,
    enumerable: true,
    configurable: true
  }
});

verifyProperty(arr, "property", {
  value: 12,
  writable: true,
  enumerable: true,
  configurable: true,
});

if (arr.length !== 0) {
  throw new Test262Error('Expected arr.length === 0, actually ' + arr.length);
}

reportCompare(0, 0);
