<!--
     Any copyright is dedicated to the Public Domain.
     http://creativecommons.org/publicdomain/zero/1.0/
-->
<html>
<style>
.reftest {
  background-color: rgba(0, 255, 0, 0.5);
}

.child {
  width: 100px;
  height: 100px;
  opacity: 0.5;
  background-color: rgb(0, 127, 0);
}

.topleft {
  float:left;

  width: 100px;
  height: 100px;

  /*First blend black rect with green in place*/
  /* Cs = (1 - αb) x Cs + αb x B(Cb, Cs)*/
  /* B(Cb, Cs) = | Cb - Cs | = (0, 0, 0) */
  /* 0.5 * (0, 255, 0) + 0.5 * (0, 0, 0) = (0, 127.5, 0)*/

  /* Now, composite the resulting color with src-over; the alpha is the original alpha for the top layer
    while the color is the blending result*/
  /* co = αs x Fa x Cs + αb x Fb x Cb  - this is premultiplied */
  /* αo = αs + αb x (1 – αs) */
  /* Source over:     Fa = 1; Fb = 1 – αs */
  /*co = 0.5 * 1 * (0, 127.5, 0) + 0.5 * 0.5 * (0, 255, 0) = (0, 63.5, 0) + 0.25 * (0, 255, 0) = (0, 127.5, 0)*/
  /*ao = 0.5 + 0.5 * 0.5 = 0.75*/
  /* Co = co/ao = (0, 127.5, 0) / 0.75*/

  /* Now alpha composite on white background */
  /*co = 0.75 * 1 * (0, 127.5, 0) / 0.75 + 1 * 0.25 * (255, 255, 255) = (0, 127.5, 0) + (63.75, 63.75, 63.75) = (63.75, 159, 63.75) = (64, 191, 64) */

  background-color: rgb(64, 191, 64);
}

.topright {
  float:left;
  width: 100px;
  height: 100px;
}

.bottom {
  width:200px;
  height: 100px;
  clear:both;
}

</style>

<div class="topleft"></div>
<div class="reftest topright"></div>
<div class="reftest bottom"></div>

</html>
