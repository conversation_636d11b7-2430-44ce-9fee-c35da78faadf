<!DOCTYPE html>
<title>
offset-distance animation doesn't create a stacking context if
offset-path is none
</title>
<style>
span {
  height: 100px;
  width: 100px;
  position: fixed;
  background: green;
  top: 50px;
}
@keyframes distance {
  from, to { offset-distance: 0% }
}
#test {
  width: 100px; height: 100px;
  background: blue;
  animation: distance 100s infinite;
}
</style>
<span></span>
<div id="test"></div>
