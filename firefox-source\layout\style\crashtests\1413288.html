<style>
:root { column-width: 0px; }
:not(article) {	padding: 2 0px; }
</style>
<script>
function jsfuzzer() {
try { htmlvar00007.setAttribute("align", "LEFT"); } catch(e) { }
try { svgvar00016.getStartPositionOfChar(0); } catch(e) { }
try { htmlvar00016.style.setProperty("column-gap", "0"); } catch(e) { }
}
</script>
<body onload=jsfuzzer()>
<iframe id="htmlvar00007"></iframe>
<details>
<summary id="htmlvar00016">)
<svg>
<tspan id="svgvar00016" />
